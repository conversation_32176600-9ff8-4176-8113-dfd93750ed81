package com.yunyi.express2b.module.crm.service.apppersonal;


import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.yunyi.express2b.framework.common.enums.CommonStatusEnum;
import com.yunyi.express2b.framework.common.enums.UserTypeEnum;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.json.JsonUtils;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.common.util.servlet.ServletUtils;
import com.yunyi.express2b.framework.security.core.util.CrmSecurityFrameworkUtils;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.crm.controller.admin.personalinfo.vo.PersonalInfoPageReqVO;
import com.yunyi.express2b.module.crm.controller.admin.personalinfo.vo.PersonalInfoRespVO;
import com.yunyi.express2b.module.crm.controller.app.v1.personal.vo.UserLoginRequest;
import com.yunyi.express2b.module.crm.dal.dataobject.companyaudithistory.CompanyAuditHistoryDO;
import com.yunyi.express2b.module.crm.dal.dataobject.companyinfo.CompanyInfoDO;
import com.yunyi.express2b.module.crm.dal.dataobject.personalinfo.PersonalInfoDO;
import com.yunyi.express2b.module.crm.dal.mysql.companyaudithistory.CompanyAuditHistoryMapper;
import com.yunyi.express2b.module.crm.dal.mysql.companyinfo.CompanyInfoMapper;
import com.yunyi.express2b.module.crm.dal.mysql.personalinfo.PersonalInfoMapper;
import com.yunyi.express2b.module.crm.enums.AuditStateEnum;
import com.yunyi.express2b.module.crm.enums.CompanyTypeEnum;
import com.yunyi.express2b.module.crm.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.crm.enums.Express2bCrmConstants;
import com.yunyi.express2b.module.express.api.order.OrderStatusApi;
import com.yunyi.express2b.module.infra.api.config.ConfigApi;
import com.yunyi.express2b.module.system.api.oauth2.OAuth2TokenApi;
import com.yunyi.express2b.module.system.api.oauth2.dto.OAuth2AccessTokenCreateReqDTO;
import com.yunyi.express2b.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import com.yunyi.express2b.module.system.api.social.SocialUserApi;
import com.yunyi.express2b.module.system.api.social.dto.SocialUserInsertReqDTO;
import com.yunyi.express2b.module.system.enums.oauth2.OAuth2ClientConstants;
import com.yunyi.express2b.module.system.enums.social.SocialTypeEnum;
import com.yunyi.framework.api.base.config.YunyiApiConfig;
import com.yunyi.framework.api.base.enums.CommonCodeConstants;
import com.yunyi.framework.api.login.api.usermanage.UserManagementApi;
import com.yunyi.framework.api.login.api.usermanage.vo.LoginRequest;
import com.yunyi.framework.api.login.api.usermanage.vo.LoginResponse;
import com.yunyi.framework.api.login.api.usermanage.vo.SsoData;
import com.yunyi.express2b.module.crm.utils.SecurityUtil;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.Instant;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.crm.enums.ErrorCodeConstants.*;
import static com.yunyi.express2b.module.crm.enums.Express2bCrmConstants.DEFAULT_AVATAR;

/**
 * appPersonal实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/25 20:20
 */

@Service
@Validated
@Slf4j
public class AppPersonalServiceImpl implements AppPersonalService {


    @Resource
    private PersonalInfoMapper personalInfoMapper;
    @Resource
    private SecurityUtil securityUtil;
    @Resource
    UserManagementApi userManagementApi;
    @Resource
    ConfigApi configApi;
    @Resource
    SocialUserApi socialUserApi;
    @Resource
    OAuth2TokenApi oAuth2TokenApi;
    @Resource
    CompanyInfoMapper companyInfoMapper;
    @Resource
    OrderStatusApi orderStatusApi;
    @Resource
    private YunyiApiConfig yunyiApiConfig;
    @Resource
    private CompanyAuditHistoryMapper companyAuditHistoryMapper;
    /*public static final Integer APPLICATIONID = 7;
    private static final Integer ISQRCODE = 0;*/


    // 获取用户升级审核结果

    /**
     * 获取用户升级审核结果
     *
     * @param memberId
     * @return
     */
    @Override
    public String getmsg(Long memberId) {
        PersonalInfoDO personalInfoDO = personalInfoMapper.selectById(memberId);
        if (personalInfoDO == null) {
            throw exception(ErrorCodeConstants.COMPANY_AUDIT_HISTORY_NOT_EXISTS);
        }
        String state = personalInfoDO.getState();
        if (state == null) {
            throw exception(ErrorCodeConstants.COMPANY_AUDIT_SUCCESS_ERROR);

        }
        if (personalInfoDO.getState().equals(AuditStateEnum.AUDIT_PENDING.getCode())) {
            return AuditStateEnum.AUDIT_PENDING.getDescription();
        } else if (personalInfoDO.getState().equals(AuditStateEnum.AUDIT_APPROVED.getCode())) {
            return AuditStateEnum.AUDIT_APPROVED.getDescription();
        } else {
            return AuditStateEnum.AUDIT_REJECTED.getDescription();
        }
    }

    /**
     * 登录调用统一登录平台
     *
     * @param loginRequest
     * @return
     */
    @Operation(summary = "登录调用统一登录平台")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public OAuth2AccessTokenRespDTO login(UserLoginRequest loginRequest) {
        log.info("开始登录业务");
        // 虚拟的用户昵称前缀
        String yunyi = "YunYi";
        // 默认的头像
        String defaultAvatar = "";
        // 1. 用户信息校验
        // 3. 统一登录平台登录 UserManagementApi.login()
        CommonResult<LoginResponse> loginResponse = this.preLogin(loginRequest);
        LoginResponse data = loginResponse.getData();
        log.info("统一登录返回："+JsonUtils.toJsonString(loginResponse));
        // 2. 保存本地信息 express2b_personal_info
        Long memberId = Long.valueOf(data.getSsoUserId());
        PersonalInfoDO personalInfo = personalInfoMapper.selectByMemberId(memberId);
        log.info("用户信息："+JsonUtils.toJsonString(personalInfo));
        if (personalInfo == null) {
            // 在配置信息中获取默认头像
            String configValueByKey = configApi.getConfigValueByKey(DEFAULT_AVATAR);
            log.info("默认头像："+configValueByKey);
            // 4. 如果用户不存在,则创建一个新的用户
            personalInfo = new PersonalInfoDO();
            personalInfo.setMemberId(memberId);
            personalInfo.setMemberName(yunyi + RandomUtil.randomString(10));
            personalInfo.setAvatar(StrUtil.isBlank(configValueByKey) ? defaultAvatar : configValueByKey);
            personalInfo.setWeStatus(CommonStatusEnum.DISABLE.getStatus());
            personalInfoMapper.insert(personalInfo);
            log.info("用户创建成功："+personalInfo);
            // 4. 创建一个社交用户
            SocialUserInsertReqDTO socialUserInsertReqDTO = buildSocialUserInsertReqDTO
                    (memberId, personalInfo.getAvatar(), personalInfo.getMemberName(), data.getOpenid(), data.getSsoUserId());
            socialUserApi.insertSocialUser(socialUserInsertReqDTO);
            log.info("社交用户创建成功："+socialUserInsertReqDTO);
        }
        // 5. 实现用户登录
        OAuth2AccessTokenCreateReqDTO tokenCreateReqDTO = new OAuth2AccessTokenCreateReqDTO();
        tokenCreateReqDTO.setUserId(memberId);
        tokenCreateReqDTO.setUserType(UserTypeEnum.MEMBER.getValue());
        tokenCreateReqDTO.setClientId(OAuth2ClientConstants.CLIENT_ID_DEFAULT);
        tokenCreateReqDTO.setScopes(null);
        log.info("创建token请求："+tokenCreateReqDTO);
        return oAuth2TokenApi.createAccessToken(tokenCreateReqDTO);
    }

    /**
     * 登录统一登录平台
     * @param loginRequest
     * @return
     */
    private CommonResult<LoginResponse> preLogin(UserLoginRequest loginRequest) {
        SsoData ssoData = new SsoData();
        ssoData.setCode(loginRequest.getCode());
        ssoData.setType(Express2bCrmConstants.LOGIN_TYPE);
        ssoData.setSessionKeyVersion(Express2bCrmConstants.LOGIN_SESSION_KEY);
        LoginRequest loginRequestApi = LoginRequest.builder()
                .ssoData(ssoData)
                .clientId(yunyiApiConfig.getClientId())
                .ip(ServletUtils.getClientIP())
                .build();
        CommonResult<LoginResponse> login = userManagementApi.login(BeanUtils.toBean(loginRequestApi, LoginRequest.class));
        log.debug("统一登录返回内容：{}", JsonUtils.toJsonString(login));
        if (login == null || !CommonCodeConstants.SUCCESS.equals(login.getCode()) || login.getData() == null) {
            throw exception(PERSONAL_LOGIN_FAILED);
        }
        return login;
    }

    /**
     * 构建一个社交用户信息
     *
     * @param memberId oosUserId
     * @param avatar   头像
     * @param nickName 昵称
     * @param openid   openid
     * @return
     */
    private static SocialUserInsertReqDTO buildSocialUserInsertReqDTO(Long memberId, String avatar, String nickName, String openid, String ssoUserId) {
        SocialUserInsertReqDTO socialUserInsertReqDTO = new SocialUserInsertReqDTO();
        socialUserInsertReqDTO.setSocialType(SocialTypeEnum.WECHAT_MINI_APP.getType());
        socialUserInsertReqDTO.setUserId(memberId);
        socialUserInsertReqDTO.setAvatar(avatar);
        socialUserInsertReqDTO.setNickname(nickName);
        socialUserInsertReqDTO.setOpenid(openid);
        socialUserInsertReqDTO.setUserType(UserTypeEnum.MEMBER.getValue());
        socialUserInsertReqDTO.setSsoUserId(ssoUserId);
        return socialUserInsertReqDTO;
    }

    /**
     * 登记用户信息登记
     *
     * @param personalInfoRespVO
     * @return
     */
    @Operation(summary = "个人信息登记")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean upUserLevel(PersonalInfoRespVO personalInfoRespVO) {

        // 获取登录用户信息
        Long companyId = personalInfoRespVO.getCompanyId();

        // 校验公司审核历史是否存在
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        PersonalInfoDO personalInfoDO1 = personalInfoMapper.selectByMemberId(loginUserId);
        if (personalInfoDO1 != null) {
            throw exception(ErrorCodeConstants.COMPANY_AUDIT_HISTORY_EXISTS);
        }

        // 更新身份证件
        personalInfoRespVO.setFrontCard(personalInfoRespVO.getFrontCard());
        personalInfoRespVO.setReverseCard(personalInfoRespVO.getReverseCard());

        // 设置审核初始状态
        personalInfoRespVO.setState(Integer.valueOf(AuditStateEnum.AUDIT_PENDING.getCode()));

        // 将数据存到 express2b_personal_info 表中
        PersonalInfoDO personalInfoDO = BeanUtils.toBean(personalInfoRespVO, PersonalInfoDO.class);

        // --- 身份证号加密与脱敏处理 ---
        String rawIdCard = personalInfoRespVO.getMemberIdCard();
        if (StrUtil.isNotBlank(rawIdCard)) {
            // 1. 对身份证号进行脱敏，用于常规展示
            personalInfoDO.setMemberIdCard(securityUtil.maskIdCard(rawIdCard));
            // 2. 对身份证号进行加密，用于安全存储
            personalInfoDO.setMemberIdCardEncrypted(securityUtil.encrypt(rawIdCard));
        }
        // --- 处理结束 ---

        personalInfoDO.setId(personalInfoRespVO.getId());
        personalInfoDO.setMemberId(CrmSecurityFrameworkUtils.getLoginUserId());
        personalInfoDO.setCompanyId(companyId);
        personalInfoDO.setMemberName(personalInfoRespVO.getMemberName());
        personalInfoDO.setMemberPhone(personalInfoRespVO.getMemberPhone());
        personalInfoMapper.insert(personalInfoDO);

        // 将数据存到 express2b_company_audit_history 表中
        CompanyAuditHistoryDO companyAuditHistoryDO = new CompanyAuditHistoryDO();
        companyAuditHistoryDO.setCompanyId(companyId);
        companyAuditHistoryDO.setCompanyName(personalInfoDO.getMemberName());
        companyAuditHistoryDO.setStatus(Integer.valueOf(AuditStateEnum.AUDIT_PENDING.getCode()));
        companyAuditHistoryMapper.insert(companyAuditHistoryDO);

        // 将数据存到 express2b_company_info 表中
        CompanyInfoDO companyInfoDO = new CompanyInfoDO();
        companyInfoDO.setCompanyId(companyId);
        companyInfoDO.setCompanyType(String.valueOf(CompanyTypeEnum.PERSONAL.getCode()));
        companyInfoDO.setContactName(personalInfoDO.getMemberName());
        companyInfoDO.setContactPhone(personalInfoDO.getMemberPhone());
        companyInfoDO.setCompanyName(personalInfoDO.getMemberName());
        companyInfoDO.setCompanyLicense(personalInfoDO.getFrontCard());
        companyInfoDO.setCompanyLicense1(personalInfoDO.getReverseCard());
        companyInfoDO.setCompanyLicense2(personalInfoDO.getReverseCard()); //备用字段没用到
        companyInfoDO.setStatus(AuditStateEnum.AUDIT_PENDING.getCode());
        companyInfoDO.setCreditCode(Instant.now().toEpochMilli()+"");
        companyInfoMapper.insert(companyInfoDO);

        return true;
    }


    /**
     * 分页查询个人信息
     * @param respVO
     * @return
     */
    @Override
    public PageResult<PersonalInfoDO> selectPage(PersonalInfoPageReqVO respVO) {
        PageResult<PersonalInfoDO> personalInfoDOPageResult = personalInfoMapper.selectPage(respVO);
        return BeanUtils.toBean(personalInfoDOPageResult, PersonalInfoDO.class);
    }

    /**
     * 校验个人信息存在注销
     * @param id
     */
    private void validatePersonalInfoExists(Long id) {
        if (personalInfoMapper.selectById(id) == null) {
            throw exception(PERSONAL_INFO_NOT_EXISTS);
        }
    }

    /**
     * 删除个人信息
     * @param memberId
     * @return
     */
    @Override
    public Boolean deletePersonalInfo(Long memberId) {
        // 校验存在
        validatePersonalInfoExists(memberId);
        //判断账户下边是否未完成订单
        if (orderStatusApi.isOrderStatus(memberId)) {
            throw exception(ErrorCodeConstants.PERSONAL_INFO_DELETE_FAILED);
        } else if (memberId == null) {
            throw exception(ErrorCodeConstants.PERSONAL_INFO_DELETE_FAILED_ADMIN);
        }
        personalInfoMapper.deleteById(memberId);
        return true;
    }

    /**
     * 获取关注状态
     * @param memberId
     * @return
     */
    @Override
    public Integer getFollowStatus(Long memberId) {
        PersonalInfoDO personalInfoDO = personalInfoMapper.selectById(memberId);
        return personalInfoDO.getWeStatus();
    }


    /**
     * 获取个人信息
     * @param memberId
     * @return
     */
    private PersonalInfoDO getPersonalInfo(Long memberId) {
        PersonalInfoDO personalInfoDO = personalInfoMapper.selectByMemberId(memberId);
        if (personalInfoDO == null) {
            throw exception(ErrorCodeConstants.PERSONAL_INFO_NOT_EXISTS);
        }
        return personalInfoDO;
    }

    /**
     * 更新头像
     * @param memberId
     * @param avatar
     * @return
     */
    @Override
    public Boolean updateHead(Long memberId,  String avatar) {
        try {
            // 获取个人用户信息
            PersonalInfoDO personalInfoDO = getPersonalInfo(memberId);
            // 获取文件名作为头像
            personalInfoDO.setAvatar(avatar);
            // 更新个人用户信息
            boolean updateSuccess = personalInfoMapper.updateById(personalInfoDO) > 0;
            if (!updateSuccess) {
                throw exception(ErrorCodeConstants.UPDATE_FAILED);
            }
            return true;
        } catch (Exception e) {
            log.error("更新头像失败: {}", e.getMessage(), e);
            throw exception(ErrorCodeConstants.UNKNOWN_ERROR);
        }
    }

    /**
     * 更新昵称
     * @param memberId
     * @param nickname
     * @return
     */
    @Override
    public Boolean updateNickname(Long memberId, String nickname) {
        PersonalInfoDO personalInfoDO = getPersonalInfo(memberId);
        personalInfoDO.setMemberName(nickname);
        personalInfoMapper.updateById(personalInfoDO);
        return true;
    }

    /**
     * 移除访问令牌
     * @param token
     * @param type
     */
    @Override
    public void logout(String token, Integer type) {
        oAuth2TokenApi.removeAccessToken(token);

    }


}