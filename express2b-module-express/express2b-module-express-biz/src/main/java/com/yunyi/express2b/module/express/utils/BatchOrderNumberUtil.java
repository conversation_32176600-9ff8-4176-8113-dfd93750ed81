package com.yunyi.express2b.module.express.utils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/27 13:59
 * @description:批次编号生成器
 */

public class BatchOrderNumberUtil {
    // 使用AtomicInteger保证线程安全
    private static final AtomicInteger dailyCounter = new AtomicInteger(1);
    private static String currentDate = "";

    /**
     * 生成批次订单编号
     * @return 格式 P + 月日(4位) + 序号(3位)，如 P0327001
     */
    public static String generate() {
        // 获取当前月日(MMdd格式)
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("MMdd"));

        // 如果是新的一天，重置计数器
        if (!today.equals(currentDate)) {
            currentDate = today;
            dailyCounter.set(1);
        }

        // 生成序号并自增
        int seq = dailyCounter.getAndIncrement();
        return "P" + today + String.format("%03d", seq);
    }
}
