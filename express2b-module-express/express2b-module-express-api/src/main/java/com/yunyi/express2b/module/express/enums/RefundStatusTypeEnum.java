package com.yunyi.express2b.module.express.enums;

import com.yunyi.express2b.framework.common.core.ArrayValuable;

import java.util.Arrays;

/**
 * 退款状态字典KEY :REFUND_STATUS_ENUM
 * * 退款状态枚举
 * <AUTHOR>
 */
public enum RefundStatusTypeEnum implements ArrayValuable<String> {

    PROCESSING("PROCESSING", "退款中"),
    REFUNDED("REFUNDED", "已退款"),
    FAILED("FAILED", "退款失败"),
    CLOSED("CLOSED", "退款关闭"),
    APPLIED("APPLIED", "退款申请中"); // 新增退款申请状态

    public static final String[] ARRAYS = Arrays.stream(values()).map(RefundStatusTypeEnum::getCode).toArray(String[]::new);

    private final String code;
    private final String description;

    RefundStatusTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String[] array() {
        return ARRAYS;
    }
}
