package com.yunyi.express2b.module.crm.controller.admin.carduser.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
/**
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 持卡用户新增/修改 Request VO")
@Data
public class CardUserSaveReqVO {

    @Schema(description = "注释", requiredMode = Schema.RequiredMode.REQUIRED, example = "11432")
    private Long id;

    @Schema(description = "卡种id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8566")
    @NotNull(message = "卡种id不能为空")
    private Long cardlimitedId;

    @Schema(description = "卡编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "卡编号不能为空")
    private String cardNumber;

    @Schema(description = "卡名-冗余字段", example = "张三")
    private String cardName;

    @Schema(description = "持有用户id", example = "9674")
    private Long personalId;

    @Schema(description = "购买时的总次数")
    private Integer total;

    @Schema(description = "剩余可用次数")
    private Integer remaining;

    @Schema(description = "生效日期（包含）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "生效日期（包含）不能为空")
    private LocalDateTime cardStartDate;

    @Schema(description = "结束日期（当日可用）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "结束日期（当日可用）不能为空")
    private LocalDateTime cardEndDate;

    @Schema(description = "启用状态 0-可用 1-不可用", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "启用状态 0-可用 1-不可用不能为空")
    private Integer status;

}