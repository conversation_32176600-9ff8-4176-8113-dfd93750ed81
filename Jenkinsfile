#!groovy
pipeline {

    agent any

    tools {
        // 使用Jenkins中配置的Maven工具
        maven 'maven'
    }

    environment {
        // 应用名称
        APP_NAME = 'express2b-server'
    }

    stages {
        stage('构建') {
            steps {
                sh 'mvn clean package -Dmaven.test.skip=true'
            }
        }

        stage("部署 fsc-dev") {
            when {
                branch 'fsc-dev'
            }
            steps {
                // 归档构建产物
                archiveArtifacts "express2b-server/target/*.jar"
                
                script {
                    def fscRemoteDir = env.DEVFSC_REMOTE_DIR
                    
                    // 使用Publish Over SSH插件进行远程部署
                    sshPublisher(
                        publishers: [
                            sshPublisherDesc(
                                configName: '240server',  // Jenkins系统配置中的SSH服务器名称
                                verbose: true,
                                transfers: [
                                    sshTransfer(
                                        sourceFiles: 'express2b-server/target/*.jar',  // 要传输的源文件
                                        removePrefix: 'express2b-server/target',  // 移除的路径前缀
                                        remoteDirectory: '/home/<USER>/express2b-server-fsc/target',  // 远程目录路径
                                        execCommand: "bash /home/<USER>/express2b-server-fsc/build.sh"  // 执行的远程命令
                                    )
                                ]
                            )
                        ]
                    )
                }
            }
        }

        stage("部署 dev") {
            when {
                branch 'dev'
            }
            steps {
                // 归档构建产物
                archiveArtifacts "express2b-server/target/*.jar"
                
                script {
                    def devRemoteDir = env.DEV_REMOTE_DIR
                    
                    // 使用Publish Over SSH插件进行远程部署
                    sshPublisher(
                        publishers: [
                            sshPublisherDesc(
                                configName: '240server',  // Jenkins系统配置中的SSH服务器名称
                                verbose: true,
                                transfers: [
                                    sshTransfer(
                                        sourceFiles: 'express2b-server/target/*.jar',  // 要传输的源文件
                                        removePrefix: 'express2b-server/target',  // 移除的路径前缀
                                        remoteDirectory: '/home/<USER>/express2b-server/target',  // 远程目录，简化路径
                                        execCommand: "bash /home/<USER>/express2b-server/build.sh"  // 执行的远程命令
                                    )
                                ]
                            )
                        ]
                    )
                }
            }
        }
        
        stage('通知') {
            steps {
                echo "部署完成"
            }
        }
    }
    
    post {
        success {
            echo "构建成功: ${env.JOB_NAME} [${env.BUILD_NUMBER}]"
        }
        failure {
            echo "构建失败: ${env.JOB_NAME} [${env.BUILD_NUMBER}]"
        }
    }
}