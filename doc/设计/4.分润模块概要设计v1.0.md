# 4.分润模块概要设计v1.0

# 分润模块概要设计说明书

## 1\. 模块划分 (Module Division)

分润模块 (`express2b-module-commission`) 负责根据平台的商业模式，在订单完成后计算应分配给不同参与方的佣金分成。

### 功能模块划分

```mermaid
graph TD
    A["分润模块(express2b-module-commission)"] --> B["分润计算"]
    A --> C["分润结算"]
    
    B --> B1["订单分润计算"]
    B --> B2["分润明细记录"]
    
    C --> C1["结算到代理商钱包"]
    C --> C2["坏账处理"]
```
### 模块关系

分润模块与其他模块的关系如下：

```mermaid
graph TD
    A["分润模块"] --> |依赖|B["订单模块"]
    A --> |依赖|C["代理商模块"]
    A --> |依赖|D["钱包模块"]
```
### 模块职责

1. **分润计算**：基于订单信息和代理商等级，按照"向上查找"算法计算订单完成后各参与方应得的分润金额，并生成分润明细记录。具体包括：

   - 接收订单完成事件
   - 查询代理商信息（等级、上下级关系）
   - 执行分润计算逻辑
   - 生成分润明细记录
2. **分润结算**：负责执行分润的实际结算操作，将已确认的分润划转至相关方钱包，并处理坏账情况。具体包括：

   - 结算分润到代理商钱包
   - 处理坏账情况下的分润取消和恢复
   - 提供分润查询接口
## 2\. 数据对象使用规范 (Data Object Usage Specification)

### 领域对象 (DO)

- **CommissionDetailDO**：分润明细领域对象，记录每笔订单的分润计算结果。

```java
public class CommissionDetailDO {
    private Long id;              // 主键ID
    private Long orderId;         // 订单ID
    private String orderNo;       // 订单编号
    private Long targetId;        // 分润目标ID（代理商ID）
    private Integer targetType;   // 分润目标类型（2:V2代理商,3:V3代理商）
    private Integer amount;       // 分润金额（单位：分）
    private Integer status;       // 状态 (1:未结算, 2:已结算, 3:已取消)
    private String reason;        // 取消原因（当status=3时有效）
    private Date calculateTime;   // 计算时间
    private Date createTime;      // 创建时间
    private Date updateTime;      // 更新时间
    // 其他必要的标准字段（creator, updater, deleted, tenant_id等）
}
```
 
- **CommissionSummaryDO**：(可选)分润汇总领域对象，用于定期汇总分润数据。

```java
public class CommissionSummaryDO {
    private Long id;              // 主键ID
    private Long targetId;        // 代理商ID
    private Date summaryDate;     // 汇总日期
    private Integer totalAmount;  // 汇总金额（单位：分）
    private Integer detailCount;  // 明细数量
    private Date createTime;      // 创建时间
    private Date updateTime;      // 更新时间
    // 其他必要的标准字段
}
```
 
### 数据传输对象 (DTO)

- **OrderCommissionDTO**：订单分润信息DTO，用于订单模块与分润模块之间的数据传输。

```java
public class OrderCommissionDTO {
    private Long orderId;           // 订单ID
    private String orderNo;         // 订单编号
    private Integer orderAmount;    // 订单金额（单位：分）
    private Long agentId;           // 代理商ID
    private Integer weight;         // 包裹重量（单位：克）
    private Integer firstWeight;    // 首重重量（单位：克）
    private Date orderTime;         // 订单时间
    private Integer orderStatus;    // 订单状态
    // getter和setter方法
}
```
 
- **CommissionResultDTO**：分润计算结果DTO，用于分润模块向钱包模块传递分润结果。

```java
public class CommissionResultDTO {
    private Long detailId;           // 分润明细ID
    private Long targetId;           // 分润目标ID（代理商ID）
    private Integer targetType;      // 分润目标类型（2:V2代理商,3:V3代理商）
    private Integer amount;          // 分润金额（单位：分）
    private Date commissionTime;     // 分润时间
    // getter和setter方法
}
```
 
### 视图对象 (VO)

- **CommissionDetailPageReqVO**：分润明细分页查询请求VO

```java
public class CommissionDetailPageReqVO extends PageParam {
    private Long orderId;           // 订单ID
    private Long targetId;          // 分润目标ID
    
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTimeBegin; // 创建时间区间开始
    
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTimeEnd;   // 创建时间区间结束
    // getter和setter方法
}
```
 
- **CommissionDetailRespVO**：分润明细响应VO

```java
public class CommissionDetailRespVO {
    private Long id;                // 明细ID
    private Long orderId;           // 订单ID
    private String orderNo;         // 订单编号
    private Integer amount;         // 分润金额（单位：分）
    private Integer status;         // 状态
    private Date createTime;        // 创建时间
    // getter和setter方法
}
```
 
- **CommissionStatisticsRespVO**：分润统计响应VO

```java
public class CommissionStatisticsRespVO {
    private Integer totalAmount;       // 累计分润金额（单位：分）
    private Integer settledAmount;     // 已结算金额（单位：分）
    private Integer pendingAmount;     // 待结算金额（单位：分）
    private List<PeriodStatisticsVO> periodStatistics; // 周期统计数据
    
    public static class PeriodStatisticsVO {
        private String period;         // 统计周期
        private Integer amount;        // 周期分润金额（单位：分）
    }
    // getter和setter方法
}
```
 
## 3\. 接口设计 (Interface Design)

### 3.1 模块间接口

#### OrderCommissionApi

```java
/**
 * 订单分润API接口
 */
public interface OrderCommissionApi {
    /**
     * 计算订单分润
     *
     * @param orderCommissionDTO 订单分润信息
     * @throws BusinessException 业务异常
     */
    void calculateOrderCommission(OrderCommissionDTO orderCommissionDTO);
    
    /**
     * 处理订单坏账
     *
     * @param orderId 订单ID
     * @throws BusinessException 业务异常
     */
    void handleBadDebtOrder(Long orderId);
    
    /**
     * 处理坏账收回
     *
     * @param orderId 订单ID
     * @throws BusinessException 业务异常
     */
    void handleBadDebtRecovery(Long orderId);
}
```
 
#### CommissionWalletApi

```java
/**
 * 分润钱包API接口
 */
public interface CommissionWalletApi {
    /**
     * 结算分润到钱包
     *
     * @param commissionResultDTO 分润结果
     * @return 结算结果
     * @throws BusinessException 业务异常
     */
    boolean settleCommissionToWallet(CommissionResultDTO commissionResultDTO);
    
    /**
     * 获取待结算金额
     *
     * @param targetId 目标ID
     * @return 待结算金额（单位：分）
     */
    Integer getPendingSettlementAmount(Long targetId);
}
```
 
### 3.2 对外接口 (Controller 设计)

#### 前端接口(app-api)

##### 分润明细查询接口

- **包路径**: `com.yunyi.express2b.module.commission.controller.app.v1`
- **接口路径**: `/commission/details`
- **HTTP方法**: GET
- **功能描述**: 查询当前用户的分润明细
- **请求参数**:
   - 查询参数:
      - startDate: String, 非必须, 开始日期 (格式：yyyy-MM-dd)
      - endDate: String, 非必须, 结束日期 (格式：yyyy-MM-dd)
      - status: Integer, 非必须, 状态 (1:未结算,2:已结算,3:已取消)
      - page: Integer, 非必须, 页码 (默认1)
      - size: Integer, 非必须, 每页大小 (默认10)
- **响应**:
   - 响应状态码: 200 OK
   - 响应体:
```java
// CommonResult<PageResult<CommissionDetailRespVO>>
{
  "code": 0,                 // 成功状态码
  "data": {
    "list": [               // 分润明细列表
      {
        "id": 1234,         // 明细ID
        "orderId": 5678,    // 订单ID
        "orderNo": "ORD2023060112345", // 订单编号
        "amount": 1050,     // 分润金额（单位：分）
        "status": 1,         // 状态 (1:未结算,2:已结算,3:已取消)
        "createTime": "2023-06-01 12:34:56" // 创建时间
      }
      // ...
    ],
    "total": 100           // 总数
  }
}
```
 
- **错误处理**:
   - 401: TOKEN_EXPIRED / 2010001 - 登录已过期，请重新登录
- **认证/授权要求**: 需要用户登录认证

##### 分润统计接口

- **包路径**: `com.yunyi.express2b.module.commission.controller.app.v1`
- **接口路径**: `/commission/statistics`
- **HTTP方法**: GET
- **功能描述**: 获取当前用户的分润统计信息
- **请求参数**:
   - 查询参数:
      - period: String, 非必须, 统计周期 (day:日,week:周,month:月,year:年), 默认month
- **响应**:
   - 响应状态码: 200 OK
   - 响应体:
```java
// CommonResult<CommissionStatisticsRespVO>
{
  "code": 0,                     // 成功状态码
  "data": {
    "totalAmount": 123456,       // 累计分润金额（单位：分）
    "settledAmount": 100000,     // 已结算金额（单位：分）
    "pendingAmount": 23456,      // 待结算金额（单位：分）
    "periodStatistics": [        // 周期统计数据
      {
        "period": "2023-06",     // 统计周期
        "amount": 34567          // 周期分润金额（单位：分）
      }
      // ...
    ]
  }
}
```
 
- **错误处理**:
   - 401: TOKEN_EXPIRED / 2010001 - 登录已过期，请重新登录
- **认证/授权要求**: 需要用户登录认证

#### 后端接口(admin-api)

##### 分润明细管理接口

- **包路径**: `com.yunyi.express2b.module.commission.controller.admin`
- **接口路径**: `/commission/details`
- **HTTP方法**: GET
- **功能描述**: 管理员查询分润明细
- **请求参数**:
   - 查询参数: 同前端分润明细查询接口，增加targetId查询条件
- **响应**: 同前端分润明细查询接口
- **错误处理**:
   - 401: UNAUTHORIZED / 1000401 - 未授权
   - 403: FORBIDDEN / 1000403 - 权限不足
- **认证/授权要求**: 需要管理员登录认证和"commission:detail:query"权限

##### 处理坏账接口

- **包路径**: `com.yunyi.express2b.module.commission.controller.admin`
- **接口路径**: `/commission/bad-debt`
- **HTTP方法**: POST
- **功能描述**: 管理员手动处理订单坏账
- **请求参数**:
   - 请求体:
      - 类型: JSON
      - 结构:
```java
{
  "orderId": 123456,       // 订单ID
  "reason": "支付失败"      // 坏账原因
}
```
 
- **响应**:
   - 响应状态码: 200 OK
   - 响应体:
```java
// CommonResult<Boolean>
{
  "code": 0,     // 成功状态码
  "data": true   // 处理结果
}
```
 
- **错误处理**:
   - 400: INVALID_PARAMS / 1000400 - 请求参数不正确
   - 401: UNAUTHORIZED / 1000401 - 未授权
   - 403: FORBIDDEN / 1000403 - 权限不足
- **认证/授权要求**: 需要管理员登录认证和"commission:bad-debt:handle"权限

### 3.3 接口契约

- **接口稳定性**：分润模块的接口一旦发布，应保持稳定，避免频繁变更
- **版本控制**：前端接口使用v1、v2等子目录实现版本管理
- **RESTful设计**：所有接口必须符合RESTful设计规范
- **接口幂等性**：关键操作如处理坏账等必须实现幂等性，保证重复请求不会产生副作用
- **文档生成**：所有Controller使用Swagger注解自动生成API文档

## 4\. 数据流和交互 (Data Flow and Interaction)

### 订单分润计算流程

```mermaid
sequenceDiagram
    participant OrderSystem as "订单系统"
    participant CommissionModule as "分润模块"
    participant AgentModule as "代理商模块"
    participant WalletModule as "钱包模块"
    
    OrderSystem->>CommissionModule: 订单完成事件(OrderCommissionDTO)
    CommissionModule->>AgentModule: 获取代理商信息(agentId, agentLevel)
    AgentModule-->>CommissionModule: 返回代理商信息
    CommissionModule->>AgentModule: 获取代理商上级链
    AgentModule-->>CommissionModule: 返回上级链信息
    CommissionModule->>CommissionModule: 执行"向上查找"算法
    CommissionModule->>CommissionModule: 计算分润金额(区分首重和续重)
    CommissionModule->>CommissionModule: 保存分润明细(CommissionDetailDO)
    CommissionModule->>WalletModule: 结算分润到钱包(CommissionResultDTO)
    WalletModule-->>CommissionModule: 结算结果
```
### 坏账处理流程

```mermaid
sequenceDiagram
    participant AdminUser as "管理员/系统"
    participant CommissionModule as "分润模块"
    participant WalletModule as "钱包模块"
    
    AdminUser->>CommissionModule: 处理坏账请求(orderId)
    CommissionModule->>CommissionModule: 查询订单相关分润明细
    alt 存在分润明细
        CommissionModule->>CommissionModule: 更新分润明细状态为"已取消"
    end
    CommissionModule->>WalletModule: 从代理商钱包扣除平台零售价
    WalletModule-->>CommissionModule: 扣除结果
    CommissionModule-->>AdminUser: 返回处理结果
    
    Note over AdminUser,CommissionModule: 坏账收回流程
    AdminUser->>CommissionModule: 处理坏账收回请求(orderId)
    CommissionModule->>CommissionModule: 重新执行分润计算
    CommissionModule->>WalletModule: 结算恢复的分润到钱包
    WalletModule-->>CommissionModule: 结算结果
    CommissionModule-->>AdminUser: 返回处理结果
```
## 5\. 数据库设计概要 (Database Design Overview)

### 主要数据表

```mermaid
erDiagram
    commission_detail {
        bigint id PK "主键"
        bigint order_id "订单ID"
        varchar_32_ order_no "订单编号"
        bigint target_id "分润目标ID"
        tinyint target_type "分润目标类型"
        int amount "分润金额（单位：分）"
        tinyint status "状态 (1:未结算, 2:已结算, 3:已取消)"
        varchar_255_ reason "取消原因"
        datetime calculate_time "计算时间"
        varchar_64_ creator "创建者"
        datetime create_time "创建时间"
        varchar_64_ updater "更新者"
        datetime update_time "更新时间"
        bit_1_ deleted "是否删除"
        bigint tenant_id "租户编号"
    }

    commission_summary {
        bigint id PK "主键"
        bigint target_id "代理商ID"
        date summary_date "汇总日期"
        int total_amount "汇总金额（单位：分）"
        int detail_count "明细数量"
        varchar_64_ creator "创建者"
        datetime create_time "创建时间"
        varchar_64_ updater "更新者"
        datetime update_time "更新时间"
        bit_1_ deleted "是否删除"
        bigint tenant_id "租户编号"
    }
```
#### 分润明细表(commission_detail)

| 字段名         | 类型         | 说明              | 约束                                                            |
|----------------|--------------|-------------------|-----------------------------------------------------------------|
| id             | bigint       | 主键              | PRIMARY KEY, AUTO_INCREMENT                                     |
| order_id       | bigint       | 订单ID            | NOT NULL                                                        |
| order_no       | varchar(32)  | 订单编号          | NOT NULL                                                        |
| target_id      | bigint       | 分润目标ID        | NOT NULL                                                        |
| target_type    | tinyint      | 分润目标类型      | NOT NULL                                                        |
| amount         | int          | 分润金额（单位：分） | NOT NULL                                                        |
| status         | tinyint      | 状态              | NOT NULL, DEFAULT 1                                             |
| reason         | varchar(255) | 取消原因          | DEFAULT NULL                                                    |
| calculate_time | datetime     | 计算时间          | NOT NULL                                                        |
| creator        | varchar(64)  | 创建者            | NOT NULL, DEFAULT ''                                            |
| create_time    | datetime     | 创建时间          | NOT NULL, DEFAULT CURRENT_TIMESTAMP                             |
| updater        | varchar(64)  | 更新者            | NOT NULL, DEFAULT ''                                            |
| update_time    | datetime     | 更新时间          | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |
| deleted        | bit(1)       | 是否删除          | NOT NULL, DEFAULT b'0'                                          |
| tenant_id      | bigint       | 租户编号          | NOT NULL, DEFAULT 0                                             |

#### 分润汇总表(commission_summary) (可选)

| 字段名       | 类型        | 说明              | 约束                                                            |
|--------------|-------------|-------------------|-----------------------------------------------------------------|
| id           | bigint      | 主键              | PRIMARY KEY, AUTO_INCREMENT                                     |
| target_id    | bigint      | 代理商ID          | NOT NULL                                                        |
| summary_date | date        | 汇总日期          | NOT NULL                                                        |
| total_amount | int         | 汇总金额（单位：分） | NOT NULL                                                        |
| detail_count | int         | 明细数量          | NOT NULL                                                        |
| creator      | varchar(64) | 创建者            | NOT NULL, DEFAULT ''                                            |
| create_time  | datetime    | 创建时间          | NOT NULL, DEFAULT CURRENT_TIMESTAMP                             |
| updater      | varchar(64) | 更新者            | NOT NULL, DEFAULT ''                                            |
| update_time  | datetime    | 更新时间          | NOT NULL, DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP |
| deleted      | bit(1)      | 是否删除          | NOT NULL, DEFAULT b'0'                                          |
| tenant_id    | bigint      | 租户编号          | NOT NULL, DEFAULT 0                                             |

### 数据访问策略

- 使用 MyBatis Plus 框架简化基础CRUD操作
- 复杂查询通过自定义 Mapper XML 实现
- 服务层负责事务管理和业务逻辑
- 分润明细查询使用分页查询
- 考虑使用数据库分区策略对大表进行分区，如按时间分区

## 6\. 字典数据定义

为保证系统统一性，以下定义所有分润模块使用的字典类型数据：

### 6.1 分润目标类型 (target_type)

| 值 | 名称     | 说明                                  |
|----|----------|---------------------------------------|
| 2  | V2_AGENT | V2级代理商，有资格获得V1-V2的差价分润  |
| 3  | V3_AGENT | V3级代理商，有资格获得所有等级差价分润 |

### 6.2 分润明细状态 (commission_detail.status)

| 值 | 名称      | 说明                                  |
|----|-----------|---------------------------------------|
| 1  | PENDING   | 未结算，计算完成但尚未进行结算         |
| 2  | SETTLED   | 已结算，已完成结算并转入对应钱包       |
| 3  | CANCELLED | 已取消，因订单坏账或其他原因取消的分润 |

### 6.3 统计周期 (period)

| 值    | 名称 | 说明             |
|-------|------|------------------|
| day   | 日   | 按天统计分润数据 |
| week  | 周   | 按周统计分润数据 |
| month | 月   | 按月统计分润数据 |
| year  | 年   | 按年统计分润数据 |
