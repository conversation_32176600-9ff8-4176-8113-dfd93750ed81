package com.yunyi.express2b.module.express.enums;

import com.yunyi.express2b.framework.common.exception.ErrorCode;

/**
 * express 错误码枚举类
 * <p>
 * express 系统，使用 1_003_000_000 段
 * 地址管理模块：1_003_001_xxx
 * 退款管理模块：1_003_002_xxx
 * 支付订单模块：1_003_003_xxx
 * 订单管理模块：1_003_004_xxx
 * 卡券管理模块：1_003_005_xxx
 * 预约管理模块：1_003_006_xxx
 * 品牌管理模块：1_003_007_xxx
 * 订阅管理模块：1_003_018_xxx
 * 数据处理模块：1_003_020_xxx
 * 价格管理模块：1_003_022_xxx
 * 文件管理模块：1_003_024_xxx
 * 消息通知模块：1_003_040_xxx
 */
public interface ErrorCodeConstants {

    // ========== 全局消息 1_003_000_xxx ==========
    ErrorCode FAILED_SEND_REFUND_MESSAGE = new ErrorCode(1_003_000_001, "发送退款消息失败");

    // ========== 地址管理模块 1_003_001_xxx ==========
    ErrorCode ADDRESS_NOT_EXISTS = new ErrorCode(1_003_001_000, "地址不存在");
    ErrorCode LOCATIONS_NOT_EXISTS = new ErrorCode(1_003_001_001, "地址不存在");
    ErrorCode SENDER_ADDRESS_NOT_EXISTS = new ErrorCode(1_003_001_002, "寄件人地址不存在");
    ErrorCode RECEIVER_ADDRESS_NOT_EXISTS = new ErrorCode(1_003_001_003, "收件人地址不存在");
    ErrorCode ADDRESS_PARSE_EMPTY = new ErrorCode(1_003_001_004, "识别地址格式有误");
    ErrorCode ADDRESS_INSERT_ERROR = new ErrorCode(1_003_001_005, "保存地址失败");
    ErrorCode ADDRESS_PARSE_IS_EMPTY = new ErrorCode(1_003_001_006, "调用接口返回的数据不存在");
    ErrorCode INVALID_ADDRESSES = new ErrorCode(1_003_001_007, "无效地址详情");
    ErrorCode ADDRESS_MAP_NULL = new ErrorCode(1_003_001_008, "地址集合为空");

    // ========== 退款管理模块 1_003_002_xxx ==========
    ErrorCode REFUND_ORDER_NOT_EXISTS = new ErrorCode(1_003_002_000, "退款单管理不存在");
    ErrorCode REFUND_AMOUNT_VERIFY_FAIL = new ErrorCode(1_003_002_001, "退款金额校验失败");
    ErrorCode REFUND_BIGGER_THAN_PAYMENT = new ErrorCode(1_003_002_002, "总退款金额大于支付金额");
    ErrorCode REFUND_CREATE_FAIL = new ErrorCode(1_003_002_003, "新建退款单失败");
    ErrorCode REQUEST_REFUND_FAIL = new ErrorCode(1_003_002_004, "请求发起退款失败");
    ErrorCode REFUND_STATUS_INVALID = new ErrorCode(1_003_002_005, "退款状态不合法");
    ErrorCode REFUND_ORDER_EMPTY = new ErrorCode(1_003_002_006, "创建退款单所需参数为空");
    ErrorCode NETWORK_FAILURE_PLEASE = new ErrorCode(1_003_036_008, "网络失败，请重试退款。");

    // ========== 支付订单模块 1_003_003_xxx ==========
    ErrorCode PAYMENT_ORDER_NOT_EXISTS = new ErrorCode(1_003_003_000, "支付订单不存在");
    ErrorCode PAYMENT_AMOUNT_VERIFY_FAIL = new ErrorCode(1_003_003_001, "支付金额校验失败");
    ErrorCode TRANSACTIONNO_VERIFY_FAIL = new ErrorCode(1_003_003_002, "交易编号校验失败");
    ErrorCode INSERT_PAYMENT_ORDER_FAIL = new ErrorCode(1_003_003_003, "支付订单插入失败");
    ErrorCode ORDER_AMOUNT_INVALID = new ErrorCode(1_003_033_010, "支付金额不合法");

    // ========== 订单管理模块 1_003_004_xxx ==========
    ErrorCode ORDER_NOT_EXISTS = new ErrorCode(1_003_004_000, "订单不存在");
    ErrorCode ORDER_ERROR = new ErrorCode(1_003_004_001, "订单信息不完整");
    ErrorCode ORDER_LOCATION_NOT_EXISTS = new ErrorCode(1_003_004_002, "订单地址信息不存在");
    ErrorCode BATCH_ORDER_NOT_EXISTS = new ErrorCode(1_003_004_003, "批量订单管理不存在");
    ErrorCode ORDER_RECIPIENT_EMPTY = new ErrorCode(1_003_004_004, "收件人信息不存在");
    ErrorCode ORDER_STATUS_NOT_EXISTS = new ErrorCode(1_003_004_005, "订单状态不存在");
    ErrorCode ORDER_STATUS_INVALID = new ErrorCode(1_003_004_006, "订单状态不合法");
    ErrorCode ORDER_STATUS_ERROR = new ErrorCode(1_003_004_007, "订单状态不是未支付");
    ErrorCode ORDER_PAY_FAIL = new ErrorCode(1_003_004_008, "订单支付失败");
    ErrorCode QT_ORDER_PAY_FAIL = new ErrorCode(1_003_004_009, "订单支付异常");
    ErrorCode UPDATE_ORDER_PAY_FAIL = new ErrorCode(1_003_004_010, "修改订单状态失败");
    ErrorCode RECORD_ORDER_PAY_FAIL = new ErrorCode(1_003_004_011, "记录订单状态失败");
    ErrorCode ORDER_CREATE_FAIL = new ErrorCode(1_003_004_012, "创建订单失败");
    ErrorCode ORDER_AMOUNT_ERROR = new ErrorCode(1_003_004_013, "订单实收金额与询价结果不一致");
    ErrorCode CANCEL_ORDER_FAILED = new ErrorCode(1_003_004_014, "订单取消失败");
    ErrorCode ORDER_CREATE_EXCEPTION = new ErrorCode(1_003_004_015, "下单接口异常");
    ErrorCode ORDER_CREATE_FAILURE = new ErrorCode(1_003_004_016, "向运力下单失败");
    ErrorCode ORDER_INFO_ERROR = new ErrorCode(1_003_050_030, "订单信息有误");
    //推送订单部分
    ErrorCode ORDER_CREATE_ERROR = new ErrorCode(1_003_060_001, "推送订单失败");

    // ========== 卡券管理模块 1_003_005_xxx ==========
    ErrorCode CARD_USER_NOT_EXISTS = new ErrorCode(1_003_005_000, "持卡用户不存在");
    ErrorCode CARD_USER_NOT_SELECT = new ErrorCode(1_003_005_001, "用户未选择使用卡");
    ErrorCode CARD_USER_CARD_ERROR = new ErrorCode(1_003_005_002, "卡卷未知异常");
    ErrorCode CARD_USER_REMAIN_NOT = new ErrorCode(1_003_005_003, "卡卷余额不足");
    ErrorCode CARD_ID_NOT_FOUND = new ErrorCode(1_003_005_004, "卡卷ID未找到");
    ErrorCode CARD_USER_PARAM_ERROR = new ErrorCode(1_003_005_005, "参数不合法");
    ErrorCode CARD_USER_STATUS_UPDATE_FAIL = new ErrorCode(1_003_005_006, "卡卷更新失败");
    ErrorCode CARD_USER_STREAM_INSERT_FAIL = new ErrorCode(1_003_005_007, "卡卷流水插入失败");

    // ========== 预约管理模块 1_003_006_xxx ==========
    ErrorCode ORDER_SCHEDULED_TIME_NOT_EXISTS = new ErrorCode(1_003_006_000, "订单预约取件时间不存在");
    ErrorCode ORDER_ORDERNO_EXISTS = new ErrorCode(1_003_006_001, "订单号已存在");

    // ========== 品牌管理模块 1_003_007_xxx ==========
    ErrorCode BRAND_SCHEDULED_CONFIG_NOT_EXISTS = new ErrorCode(1_003_007_000, "快递品牌预约配置不存在");
    ErrorCode BRAND_NOT_EXISTS = new ErrorCode(1_003_007_001, "快递品牌不存在");

    // ========== 订阅管理模块 1_003_018_xxx ==========
    ErrorCode SUBSCRIBE_NOT_EXISTS = new ErrorCode(1_003_018_000, "用户订阅信息表不存在");
    ErrorCode IMPORT_LIST_IS_EMPTY = new ErrorCode(1_003_018_001, "导入的数据不存在");
    ErrorCode FAILED_QUERY_SUBSCRIPTION = new ErrorCode(1_003_018_002, "查询订阅列表失败");

    // ========== 数据处理模块 1_003_020_xxx ==========
    ErrorCode QUERY_DATA_IS_EMPTY = new ErrorCode(1_003_020_000, "查询的数据不存在");
    ErrorCode JSON_ZH_ERROR = new ErrorCode(1_003_020_001, "json数据转化异常");
    ErrorCode PARAMETER_IS_EMPTY = new ErrorCode(1_003_020_002, "请求参数为空");
    ErrorCode OBJECT_NOT_EXIST = new ErrorCode(1_003_020_003, "对象不存在");
    ErrorCode RESULT_ERROR = new ErrorCode(1_003_020_004, "返回结果异常");
    ErrorCode CALLING_INTERFACE_ERROR = new ErrorCode(1_003_020_006, "调用接口异常");

    // ========== 价格管理模块 1_003_022_xxx ==========
    ErrorCode PRICING_NOT_EXISTS = new ErrorCode(1_003_022_000, "阶梯价优惠不存在");
    ErrorCode EXPRESS_PRICE_NOT_EXISTS = new ErrorCode(1_003_022_001, "运价信息不存在");
    ErrorCode EXPRESS_PRICE_QUERY_FAIL = new ErrorCode(1_003_022_002, "查询运价信息失败");
    ErrorCode CITY_NOT_EXISTS = new ErrorCode(1_003_022_003, "城市不存在");

    // ========== 文件管理模块 1_003_024_xxx ==========
    ErrorCode READ_EXCEL_ERROR = new ErrorCode(1_003_024_000, "读取excel文件数据失败");
    ErrorCode FILE_TRANSFER_ERROR = new ErrorCode(1_003_024_001, "文件类型转换失败");
    ErrorCode EXCEL_UPLOAD_ERROR = new ErrorCode(1_003_024_002, "请选择有效的Excel文件进行导入");
    ErrorCode UPLOAD_ERROR = new ErrorCode(1_003_024_003, "异步上传文件失败");

    // ========== 物品管理模块 1_003_028_xxx ==========
    ErrorCode PRODUCTS_NOT_EXISTS = new ErrorCode(1_003_028_000, "物品信息不存在");

    // ========== 消息通知模块 1_003_040_xxx ==========
    ErrorCode FAILED_SEND_AUDIT_STATUS_MESSAGE = new ErrorCode(1_003_040_000, "发送审核状态通知消息失败");
    ErrorCode SENDING_ORDER_PAYMENT = new ErrorCode(1_003_040_001, "发送订单支付提醒消息失败");
    ErrorCode SENDING_GOODS_STATUS = new ErrorCode(1_003_040_002, "发送货物状态更新提醒失败");

    // // ========== 工单模块 1_003_030_xxx ==========
    ErrorCode WORK_ORDER_NOT_EXISTS = new ErrorCode(1_003_030_001, "工单不存在");
    ErrorCode WORK_ORDER_CREATE_FAIL = new ErrorCode(1_003_030_002, "工单创建失败");
    ErrorCode WORK_ORDER_WEIGHT_REQUIRED = new ErrorCode(1_003_030_003, "重量异常类型工单必需填写weight参数");
    ErrorCode WORK_ORDER_PRICE_REQUIRED = new ErrorCode(1_003_030_004, "破损、遇失、少件、错件类型工单必需填写price参数");
    ErrorCode WORK_ORDER_ORDER_NOT_EXISTS = new ErrorCode(1_003_030_005, "关联订单不存在或数据不全");
    ErrorCode WORK_ORDER_UPDATE_FAIL = new ErrorCode(1_003_030_006, "工单修改失败");

    //  ========== 登录管理模块 1_003_050_xxx ==========
    ErrorCode USER_NOT_EXISTS = new ErrorCode(1_003_050_000, "用户不存在");

    // ========== 客服管理模块 1_003_060_xxx ==========
    ErrorCode ONLINE_CUSTOMER_SERVICE_UNAVAILABLE = new ErrorCode(1_003_060_000, "当前客服系统繁忙，请稍后再试");
    ErrorCode ONLINE_CUSTOMER_SERVICE_EXCEPTION = new ErrorCode(1_003_060_001, "网络异常，请检查您的连接或稍后重试");

   // ========== 用户信息修改 1_003_070_xxx ==========
   ErrorCode USER_INFO_UPDATE_FAIL = new ErrorCode(1_003_070_000, "用户信息修改失败");

   //  ========== 发票管理模块 1_003_080_xxx ==========
    ErrorCode CREATE_INVOICE_USER_FAIL = new ErrorCode(1_003_080_000, "创建用户开票信息失败");
    ErrorCode CREATE_INVOICE_ORDER_FAIL = new ErrorCode(1_003_080_001, "提交开票申请失败");
    ErrorCode GET_INVOICE_ORDER_FAIL = new ErrorCode(1_003_080_002, "获取指定开票记录详情失败");
    ErrorCode GET_INVOICE_DOWNLOAD_URL_FAIL = new ErrorCode(1_003_080_003, "获取发票下载链接失败");
    ErrorCode EXPORT_INVOICE_FAIL = new ErrorCode(1_003_080_004, "导出发票失败");
    ErrorCode FILE_IS_EMPTY = new ErrorCode(1_003_080_005, "文件为空");
    ErrorCode FILE_FORMAT_ERROR = new ErrorCode(1_003_080_006, "文件格式错误");
    ErrorCode INVOICE_IMPORT_ERROR = new ErrorCode(1_003_080_007, "导入开票结果失败");
    ErrorCode USER_NOT_EXIST = new ErrorCode(1_003_080_008, "用户不存在");
    ErrorCode UPDATE_INVOICE_USER_FAIL = new ErrorCode(1_003_080_009, "更新用户开票信息失败");
    ErrorCode UPDATE_INVOICE_ORDER_FAIL = new ErrorCode(1_003_080_010, "更新开票申请失败");
}
