#set ($subTable = $subTables.get($subIndex))##当前表
#set ($subColumns = $subJoinColumnsList.get($subIndex))##当前字段数组
#set ($subJoinColumn = $subJoinColumns.get($subIndex))##当前 join 字段
#set ($SubJoinColumnName = $subJoinColumn.javaField.substring(0,1).toUpperCase() + ${subJoinColumn.javaField.substring(1)})##首字母大写
package ${basePackage}.module.${subTable.moduleName}.dal.mysql.${subTable.businessName};

import java.util.*;

import ${PageResultClassName};
import ${PageParamClassName};
import ${QueryWrapperClassName};
import ${BaseMapperClassName};
import ${basePackage}.module.${subTable.moduleName}.dal.dataobject.${subTable.businessName}.${subTable.className}DO;
import org.apache.ibatis.annotations.Mapper;

/**
 * ${subTable.classComment} Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ${subTable.className}Mapper extends BaseMapperX<${subTable.className}DO> {

## 情况一：MASTER_ERP 时，需要分查询页子表
#if ( $table.templateType == 11 )
    default PageResult<${subTable.className}DO> selectPage(PageParam reqVO, ${subJoinColumn.javaType} ${subJoinColumn.javaField}) {
        return selectPage(reqVO, new LambdaQueryWrapperX<${subTable.className}DO>()
            .eq(${subTable.className}DO::get${SubJoinColumnName}, ${subJoinColumn.javaField})
            .orderByDesc(${subTable.className}DO::getId));## 大多数情况下，id 倒序

    }
## 主表与子表是一对一时 
    #if (!$subTable.subJoinMany)
        default ${subTable.className}DO selectBy${SubJoinColumnName}(${subJoinColumn.javaType} ${subJoinColumn.javaField}) {
        return selectOne(${subTable.className}DO::get${SubJoinColumnName}, ${subJoinColumn.javaField});
        }
    #end

## 情况二：非 MASTER_ERP 时，需要列表查询子表
#else
    #if ( $subTable.subJoinMany)
    default List<${subTable.className}DO> selectListBy${SubJoinColumnName}(${subJoinColumn.javaType} ${subJoinColumn.javaField}) {
        return selectList(${subTable.className}DO::get${SubJoinColumnName}, ${subJoinColumn.javaField});
    }

    #else
    default ${subTable.className}DO selectBy${SubJoinColumnName}(${subJoinColumn.javaType} ${subJoinColumn.javaField}) {
        return selectOne(${subTable.className}DO::get${SubJoinColumnName}, ${subJoinColumn.javaField});
    }

    #end
    #end
    default int deleteBy${SubJoinColumnName}(${subJoinColumn.javaType} ${subJoinColumn.javaField}) {
        return delete(${subTable.className}DO::get${SubJoinColumnName}, ${subJoinColumn.javaField});
    }

}
