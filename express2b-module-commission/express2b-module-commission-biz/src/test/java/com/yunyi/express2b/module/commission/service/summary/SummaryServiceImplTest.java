package com.yunyi.express2b.module.commission.service.summary;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.module.commission.controller.admin.summary.vo.SummaryPageReqVO;
import com.yunyi.express2b.module.commission.controller.admin.summary.vo.SummarySaveReqVO;
import com.yunyi.express2b.module.commission.dal.dataobject.summary.SummaryDO;
import com.yunyi.express2b.module.commission.dal.mysql.summary.SummaryMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import static com.yunyi.express2b.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static com.yunyi.express2b.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertPojoEquals;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertServiceException;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomLongId;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomPojo;
import static com.yunyi.express2b.module.commission.enums.ErrorCodeConstants.SUMMARY_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link SummaryServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(SummaryServiceImpl.class)
public class SummaryServiceImplTest extends BaseDbUnitTest {

    @Resource
    private SummaryServiceImpl summaryService;

    @Resource
    private SummaryMapper summaryMapper;

    @Test
    public void testCreateSummary_success() {
        // 准备参数
        SummarySaveReqVO createReqVO = randomPojo(SummarySaveReqVO.class).setId(null);

        // 调用
        Long summaryId = summaryService.createSummary(createReqVO);
        // 断言
        assertNotNull(summaryId);
        // 校验记录的属性是否正确
        SummaryDO summary = summaryMapper.selectById(summaryId);
        assertPojoEquals(createReqVO, summary, "id");
    }

    @Test
    public void testUpdateSummary_success() {
        // mock 数据
        SummaryDO dbSummary = randomPojo(SummaryDO.class);
        summaryMapper.insert(dbSummary);// @Sql: 先插入出一条存在的数据
        // 准备参数
        SummarySaveReqVO updateReqVO = randomPojo(SummarySaveReqVO.class, o -> {
            o.setId(dbSummary.getId()); // 设置更新的 ID
        });

        // 调用
        summaryService.updateSummary(updateReqVO);
        // 校验是否更新正确
        SummaryDO summary = summaryMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, summary);
    }

    @Test
    public void testUpdateSummary_notExists() {
        // 准备参数
        SummarySaveReqVO updateReqVO = randomPojo(SummarySaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> summaryService.updateSummary(updateReqVO), SUMMARY_NOT_EXISTS);
    }

    @Test
    public void testDeleteSummary_success() {
        // mock 数据
        SummaryDO dbSummary = randomPojo(SummaryDO.class);
        summaryMapper.insert(dbSummary);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbSummary.getId();

        // 调用
        summaryService.deleteSummary(id);
        // 校验数据不存在了
        assertNull(summaryMapper.selectById(id));
    }

    @Test
    public void testDeleteSummary_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> summaryService.deleteSummary(id), SUMMARY_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetSummaryPage() {
        // mock 数据
        SummaryDO dbSummary = randomPojo(SummaryDO.class, o -> { // 等会查询到
            o.setTargetId(null);
            o.setSummaryDate(null);
            o.setTotalAmount(null);
            o.setDetailCount(null);
            o.setCreateTime(null);
        });
        summaryMapper.insert(dbSummary);
        // 测试 targetId 不匹配
        summaryMapper.insert(cloneIgnoreId(dbSummary, o -> o.setTargetId(null)));
        // 测试 summaryDate 不匹配
        summaryMapper.insert(cloneIgnoreId(dbSummary, o -> o.setSummaryDate(null)));
        // 测试 totalAmount 不匹配
        summaryMapper.insert(cloneIgnoreId(dbSummary, o -> o.setTotalAmount(null)));
        // 测试 detailCount 不匹配
        summaryMapper.insert(cloneIgnoreId(dbSummary, o -> o.setDetailCount(null)));
        // 测试 createTime 不匹配
        summaryMapper.insert(cloneIgnoreId(dbSummary, o -> o.setCreateTime(null)));
        // 准备参数
        SummaryPageReqVO reqVO = new SummaryPageReqVO();
        reqVO.setTargetId(null);
        //reqVO.setSummaryDate(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
        reqVO.setTotalAmount(null);
        reqVO.setDetailCount(null);
        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

        // 调用
        PageResult<SummaryDO> pageResult = summaryService.getSummaryPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbSummary, pageResult.getList().get(0));
    }

}