package com.yunyi.express2b.module.express.service.orderscheduledtime;

import com.yunyi.express2b.module.express.controller.admin.orderscheduledtime.vo.*;
import com.yunyi.express2b.module.express.dal.dataobject.brandscheduledconfig.BrandScheduledConfigDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import com.yunyi.express2b.module.express.dal.dataobject.orderscheduledtime.OrderScheduledTimeDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;

import com.yunyi.express2b.module.express.dal.mysql.orderscheduledtime.OrderScheduledTimeMapper;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.*;

/**
 * 订单预约取件时间 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class OrderScheduledTimeServiceImpl implements OrderScheduledTimeService {

    @Resource
    private OrderScheduledTimeMapper orderScheduledTimeMapper;

    /**
     * 创建订单预约取件时间
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Long createOrderScheduledTime(OrderScheduledTimeSaveReqVO createReqVO) {
        // 插入
        OrderScheduledTimeDO orderScheduledTime = BeanUtils.toBean(createReqVO, OrderScheduledTimeDO.class);
        orderScheduledTimeMapper.insert(orderScheduledTime);
        // 返回
        return orderScheduledTime.getId();
    }

    /**
     * 更新订单预约取件时间
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateOrderScheduledTime(OrderScheduledTimeSaveReqVO updateReqVO) {
        // 校验存在
        validateOrderScheduledTimeExists(updateReqVO.getId());
        // 更新
        OrderScheduledTimeDO updateObj = BeanUtils.toBean(updateReqVO, OrderScheduledTimeDO.class);
        orderScheduledTimeMapper.updateById(updateObj);
    }

    /**
     * 删除订单预约取件时间
     * @param id 编号
     */
    @Override
    public void deleteOrderScheduledTime(Long id) {
        // 校验存在
        validateOrderScheduledTimeExists(id);
        // 删除
        orderScheduledTimeMapper.deleteById(id);
    }

    /**
     * 校验订单预约取件时间是否存在
     * @param id
     */
    private void validateOrderScheduledTimeExists(Long id) {
        if (orderScheduledTimeMapper.selectById(id) == null) {
            throw exception(ORDER_SCHEDULED_TIME_NOT_EXISTS);
        }
    }

    /**
     * 获取订单预约取件时间
     * @param id 编号
     * @return
     */
    @Override
    public OrderScheduledTimeDO getOrderScheduledTime(Long id) {
        return orderScheduledTimeMapper.selectById(id);
    }

    /**
     * 获取订单预约取件时间分页
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<OrderScheduledTimeDO> getOrderScheduledTimePage(OrderScheduledTimePageReqVO pageReqVO) {
        return orderScheduledTimeMapper.selectPage(pageReqVO);
    }

    /**
     * 获取品牌预约时间
     * @param brandKey
     * @return
     */
    @Override
    public List<AvailableDate> getBrandScheduledConfigTime(String brandKey) {
        AvailableDate availableDate = new AvailableDate();
        // 存储每个日期对应的时间槽
        Map<String, List<String>> dateTimeSlotMap = new TreeMap<>();
        // 获取多少天
        List<BrandScheduledConfigDO> brandScheduledConfigTime = orderScheduledTimeMapper.getBrandScheduledConfigTime(brandKey);

        // 检查列表是否为空，避免空指针异常
        if (brandScheduledConfigTime == null || brandScheduledConfigTime.isEmpty()) {
            return Collections.emptyList();
        }
        //预约天数变量
        int days = 0;
        // 获取当前时间
        LocalTime currentTime = LocalTime.now();
        // 下班时间
        LocalTime offWorkTime = LocalTime.of(18, 0);
        int startDay = 0;
        // 判断是否是下班时间之后
        if (currentTime.isAfter(offWorkTime)) {
            startDay = 1;
            days = 1;
        }else{
            days = brandScheduledConfigTime.get(0).getScheduledDays()-1;
        }
        //根据这个days几天生成后几天的日期
        for (int i = startDay; i <= days + startDay; i++) {
            LocalDate nextDate = LocalDate.now().plusDays(i);
            String nextDateString = nextDate.toString();
            dateTimeSlotMap.put(nextDateString, new ArrayList<>());
        }
        // 遍历每个时间配置，将时间槽添加到对应的日期
        for (BrandScheduledConfigDO brandScheduledConfigDO : brandScheduledConfigTime) {
            LocalTime startTime = brandScheduledConfigDO.getScheduledTimeStart();
            LocalTime endTime = brandScheduledConfigDO.getScheduledTimeEnd();
            String timeSlotStr = startTime.toString() + "-" + endTime.toString();
            for (Map.Entry<String, List<String>> entry : dateTimeSlotMap.entrySet()) {
                String dateStr = entry.getKey();
                LocalDate date = LocalDate.parse(dateStr);
                if (date.isEqual(LocalDate.now()) && currentTime.isAfter(startTime)) {
                    continue;
                }
                entry.getValue().add(timeSlotStr);
            }
        }
        // 在 for 循环之前声明并初始化 dateWithTimeSlotsList
        List<DateWithTimeSlots> dateWithTimeSlotsList = new ArrayList<>();
        // 构建 DateWithTimeSlots 列表，过滤掉时间槽为空的日期
        for (Map.Entry<String, List<String>> entry : dateTimeSlotMap.entrySet()) {
            List<String> timeSlots = entry.getValue();
            // 判断时间槽列表是否为空
            if (!timeSlots.isEmpty()) {
                DateWithTimeSlots dto = new DateWithTimeSlots();
                dto.setDate(entry.getKey());
                dto.setTimeSlots(timeSlots);
                dateWithTimeSlotsList.add(dto);
            }
        }
        // 设置结果
        availableDate.setDateWithTimeSlotsList(dateWithTimeSlotsList);
        return Collections.singletonList(availableDate);
    }


    /**
     * 将日期字符串转换为对应的描述（今天、明天等）
     * @param dateStr 日期字符串，格式为 yyyy-MM-dd
     * @return 对应的描述字符串
     */
    private String getDateDescription(String dateStr) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE;
        // 将输入的日期字符串转换为 LocalDate 对象
        LocalDate inputDate = LocalDate.parse(dateStr, formatter);
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        if (inputDate.isEqual(currentDate)) {
            return "今天";
        } else if (inputDate.isEqual(currentDate.plusDays(1))) {
            return "明天";
        }
        else if (inputDate.isEqual(currentDate.plusDays(2))) {
            return "后天";
        }
        else {
            return inputDate.format(formatter);
        }
    }

}