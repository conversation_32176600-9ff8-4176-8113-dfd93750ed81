package com.yunyi.express2b.module.agent.dal.mysql.agentprofile;

import java.util.*;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.yunyi.express2b.module.agent.api.dto.AgentProfileRespDTO;
import com.yunyi.express2b.module.agent.api.dto.RelationShipDTO;
import com.yunyi.express2b.module.agent.controller.admin.agentprofile.vo.AgentProfileRespVO;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.AgentProfileDO;
import com.yunyi.express2b.module.agent.dal.dataobject.levelconfig.LevelConfigDO;
import com.yunyi.express2b.module.agent.dal.dataobject.relationship.RelationshipDO;
import org.apache.ibatis.annotations.Mapper;
import com.yunyi.express2b.module.agent.controller.admin.agentprofile.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 代理商档案 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AgentProfileMapper extends BaseMapperX<AgentProfileDO> {

    default PageResult<AgentProfileDO> selectPage(AgentProfilePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AgentProfileDO>()
                .eqIfPresent(AgentProfileDO::getMemberId, reqVO.getMemberId())
                .likeIfPresent(AgentProfileDO::getName, reqVO.getName())
                .eqIfPresent(AgentProfileDO::getMobile, reqVO.getMobile())
                .eqIfPresent(AgentProfileDO::getEmail, reqVO.getEmail())
                .eqIfPresent(AgentProfileDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AgentProfileDO::getLevelId, reqVO.getLevelId())
                .eqIfPresent(AgentProfileDO::getReferrerAgentId, reqVO.getReferrerAgentId())
                .eqIfPresent(AgentProfileDO::getCustomPricingTemplateId, reqVO.getCustomPricingTemplateId())
                .eqIfPresent(AgentProfileDO::getWalletId, reqVO.getWalletId())
                .eqIfPresent(AgentProfileDO::getSystemTenantId, reqVO.getSystemTenantId())
                .eqIfPresent(AgentProfileDO::getDirectDownlineCount, reqVO.getDirectDownlineCount())
                .eqIfPresent(AgentProfileDO::getTotalDownlineCount, reqVO.getTotalDownlineCount())
                .betweenIfPresent(AgentProfileDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AgentProfileDO::getId));
    }




    /**
     * 实现团队成员查询
     * @param agentId
     * @return
     */
    //List<AgentProfileDO> getTeamById(Long agentId);
    default List<AgentProfileDO> getTeamById(Long agentId){
        return selectList(new MPJLambdaWrapperX<AgentProfileDO>()
                .select(AgentProfileDO::getId,
                        AgentProfileDO::getName,
                        AgentProfileDO::getMobile,
                        AgentProfileDO::getMemberId,
                        AgentProfileDO::getEmail,
                        AgentProfileDO::getStatus,
                        AgentProfileDO::getLevelId,
                        AgentProfileDO::getReferrerAgentId,
                        AgentProfileDO::getCustomPricingTemplateId,
                        AgentProfileDO::getWalletId,
                        AgentProfileDO::getSystemTenantId,
                        AgentProfileDO::getDirectDownlineCount,
                        AgentProfileDO::getTotalDownlineCount,
                        AgentProfileDO::getCreateTime)
                .leftJoin(RelationshipDO.class,RelationshipDO::getDescendantId, AgentProfileDO::getId)
                .eq(RelationshipDO::getAncestorId,  agentId)
                .gt(RelationshipDO::getDepth,  0)
        );
    }


    /**
     * 该mapper可以同时实现通过等级id查找名字，以及获取代理商基本信息
     * @param agentId
     * @return
     */
    default AgentProfileRespDTO selectAgentAndLevelNameById(Long agentId) {
        return selectJoinOne(AgentProfileRespDTO.class,
                new MPJLambdaWrapperX<AgentProfileDO>()
                        .select(
                                AgentProfileDO::getId,
                                AgentProfileDO::getName,
                                AgentProfileDO::getMobile,
                                AgentProfileDO::getMemberId,
                                AgentProfileDO::getEmail,
                                AgentProfileDO::getStatus,
                                AgentProfileDO::getLevelId,
                                AgentProfileDO::getReferrerAgentId,
                                AgentProfileDO::getCustomPricingTemplateId,
                                AgentProfileDO::getWalletId,
                                AgentProfileDO::getSystemTenantId,
                                AgentProfileDO::getDirectDownlineCount,
                                AgentProfileDO::getTotalDownlineCount,
                                AgentProfileDO::getCreateTime
                        )
                        .leftJoin(LevelConfigDO.class, LevelConfigDO::getId, AgentProfileDO::getLevelId)
                        .selectAs(LevelConfigDO::getLevelName, AgentProfileRespDTO::getLevelName)
                        .eq(AgentProfileDO::getId, agentId)
        );
    }

    /**
     * 传递一个推荐代理商id进来，拿到一条推荐代理商数据
     * 并且还要返回一个id值 为下面方法做判断
     * @param referrerAgentId
     * @return
     */
    default AgentProfileDO selectReferrerAgentById(Long referrerAgentId) {
        return selectOne(new LambdaQueryWrapperX<AgentProfileDO>()
                .select(AgentProfileDO::getReferrerAgentId,  AgentProfileDO::getId)
                .eq(AgentProfileDO::getId, referrerAgentId));
    }

    /**
     * 根据member获取代理商
     * @param memberId
     * @return
     */
    default AgentProfileDO getLevelId(Long memberId){
        return selectOne(new LambdaQueryWrapperX<AgentProfileDO>()
                .select(AgentProfileDO::getLevelId)
                .eq(AgentProfileDO::getMemberId, memberId));
    }

    /**
     * 根据memberId获取代理商
     * @param memberId
     * @return
     */
    default AgentProfileDO getAgentIdByMemberId(Long memberId){
        return selectOne(new LambdaQueryWrapperX<AgentProfileDO>()
                .select(AgentProfileDO::getId,AgentProfileDO::getMemberId,AgentProfileDO::getReferrerAgentId,AgentProfileDO::getCustomPricingTemplateId)
                .eq(AgentProfileDO::getMemberId, memberId));
    }


    /**
     * 根据手机号查询代理商
     * @param mobile
     * @return
     */
    default AgentProfileDO getAgentByMobile(String mobile){
        return selectOne(new LambdaQueryWrapperX<AgentProfileDO>()
                .select(AgentProfileDO::getId,
                        AgentProfileDO::getMemberId,
                        AgentProfileDO::getMobile,
                        AgentProfileDO::getStatus,
                        AgentProfileDO::getName)
                .eq(AgentProfileDO::getMobile, mobile)
        );
    }
    /**
     * 通过当前传入的对象获取到推荐人id
     *
     * @param agentProfileDO
     * @return
     */
    default Long selectByReferrerAgentId(AgentProfileDO agentProfileDO){
        if (agentProfileDO == null || agentProfileDO.getId() == null) {
            return null;
        }
        AgentProfileDO result = selectOne(new LambdaQueryWrapperX<AgentProfileDO>()
                .select(AgentProfileDO::getReferrerAgentId)
                .eq(AgentProfileDO::getId, agentProfileDO.getId()));
        return result != null ? result.getReferrerAgentId() : null;

    }

    /**
     * 根据代理商id以及模板id更新代理商价格模板
     * @param id
     * @param templateId
     * @return
     */

    //   根据代理商ID修改代理商定价模版ID
    default int updateCustomPricingTemplateId(Long id, Long templateId){
        return update(new LambdaUpdateWrapper<AgentProfileDO>()
                .set(AgentProfileDO::getCustomPricingTemplateId,templateId)
                .eq(AgentProfileDO::getId, id)
        );
    }



    int updateLicenseUr(@Param("imageUrl")String imageUrl,@Param("memberId") Long memberId);

    default AgentProfileDO selectByMemberId(Long memberId) {
        return selectOne(new LambdaQueryWrapperX<AgentProfileDO>()
                .eq(AgentProfileDO::getMemberId, memberId));
    }
}