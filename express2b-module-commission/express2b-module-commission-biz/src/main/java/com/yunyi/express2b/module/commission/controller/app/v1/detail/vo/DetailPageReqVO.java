package com.yunyi.express2b.module.commission.controller.app.v1.detail.vo;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 分润明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DetailPageReqVO extends PageParam {

    @Schema(description = "订单ID", example = "29090")
    private Long orderId;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "分润目标ID（代理商ID）", example = "11732")
    private Long targetId;

    @Schema(description = "分润目标类型（2:V2代理商,3:V3代理商）", example = "1")
    private Integer targetType;

    @Schema(description = "分润金额（单位：分）")
    private Integer amount;

    @Schema(description = "状态 (1:未结算, 2:已结算, 3:已取消)", example = "2")
    private Integer status;

    @Schema(description = "取消原因", example = "不对")
    private String reason;

    @Schema(description = "计算时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] calculateTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}