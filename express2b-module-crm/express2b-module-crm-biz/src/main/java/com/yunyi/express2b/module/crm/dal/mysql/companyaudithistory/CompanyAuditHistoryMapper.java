package com.yunyi.express2b.module.crm.dal.mysql.companyaudithistory;

import java.util.*;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.module.crm.dal.dataobject.companyaudithistory.CompanyAuditHistoryDO;
import com.yunyi.express2b.module.crm.enums.AuditStateEnum;
import org.apache.ibatis.annotations.Mapper;
import com.yunyi.express2b.module.crm.controller.admin.companyaudithistory.vo.*;

/**
 * 企业账户审核记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface   CompanyAuditHistoryMapper extends BaseMapperX<CompanyAuditHistoryDO> {

    default PageResult<CompanyAuditHistoryDO> selectPage(CompanyAuditHistoryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CompanyAuditHistoryDO>()
                .eqIfPresent(CompanyAuditHistoryDO::getCompanyId, reqVO.getCompanyId())
                .likeIfPresent(CompanyAuditHistoryDO::getCompanyName, reqVO.getCompanyName())
                .eqIfPresent(CompanyAuditHistoryDO::getAuditUser, reqVO.getAuditUser())
                .eqIfPresent(CompanyAuditHistoryDO::getStatus, reqVO.getStatus())
                .eqIfPresent(CompanyAuditHistoryDO::getRemarks, reqVO.getRemarks())
                .betweenIfPresent(CompanyAuditHistoryDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(CompanyAuditHistoryDO::getId));
    }










}