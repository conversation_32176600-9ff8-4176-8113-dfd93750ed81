package com.yunyi.express2b.module.crm.dal.mysql.cardlimited;

import java.util.*;


import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.module.crm.controller.admin.cardlimitedbrand.vo.CardlimitedBrandPageReqVO;
import com.yunyi.express2b.module.crm.controller.admin.cardlimitedbrand.vo.CardlimitedBrandRespVO;
import com.yunyi.express2b.module.crm.dal.dataobject.cardlimited.CardlimitedBrandDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 卡种可使用品牌 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CardlimitedBrandMapper extends BaseMapperX<CardlimitedBrandDO> {
    default PageResult<CardlimitedBrandDO> selectPage(CardlimitedBrandPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CardlimitedBrandDO>()
                .eqIfPresent(CardlimitedBrandDO::getCardlimitedId, reqVO.getCardlimitedId())
                .eqIfPresent(CardlimitedBrandDO::getBrandId, reqVO.getBrandId())
                .orderByDesc(CardlimitedBrandDO::getId));
    }
    /**
     * 根据卡限制ID选择卡限制品牌列表
     *
     * @param cardlimitedId 卡限制ID，用于筛选符合条件的品牌
     * @return 返回一个CardlimitedBrandDO对象列表，其中所有对象的卡限制ID与参数cardlimitedId匹配
     */
    default List<CardlimitedBrandDO> selectListByCardlimitedId(Long cardlimitedId) {
        return selectList(CardlimitedBrandDO::getCardlimitedId, cardlimitedId);
    }
    /**
     * 根据卡限制ID删除相关的品牌信息
     *
     * @param cardlimitedId 卡限制ID，用于标识特定的卡限制条件
     * @return 返回删除操作影响的行数，表明删除成功与否
     */
    default int deleteByCardlimitedId(Long cardlimitedId) {
        return delete(CardlimitedBrandDO::getCardlimitedId, cardlimitedId);
    }


}
