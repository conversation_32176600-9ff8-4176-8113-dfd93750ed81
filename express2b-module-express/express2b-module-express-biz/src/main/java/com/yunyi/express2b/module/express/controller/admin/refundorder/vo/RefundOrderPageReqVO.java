package com.yunyi.express2b.module.express.controller.admin.refundorder.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 退款单管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RefundOrderPageReqVO extends PageParam {

    @Schema(description = "关联支付订单ID", example = "3811")
    private Long paymentOrderId;

    @Schema(description = "退款金额")
    private Integer amount;

    @Schema(description = "退款单号")
    private String refundOrderSn;

    @Schema(description = "退款原因", example = "不对")
    private String refundReason;

    @Schema(description = "退款方式（例如：原路返回）")
    private String refundMethod;

    @Schema(description = "退款状态", example = "2")
    private String refundStatus;

    @Schema(description = "最近退款申请时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] recentRefundApplyTime;

    @Schema(description = "退款提交时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] refundSubmitTime;

    @Schema(description = "退款状态最后更新时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] refundStatusUpdateTime;

    @Schema(description = "退款完成时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] refundCompleteTime;

    @Schema(description = "对账注解", example = "30259")
    private Long reconciliationId;

    @Schema(description = "对账时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] reconciliationTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}