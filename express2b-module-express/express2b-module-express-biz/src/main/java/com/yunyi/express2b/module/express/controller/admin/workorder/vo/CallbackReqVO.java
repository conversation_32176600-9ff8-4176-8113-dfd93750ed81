package com.yunyi.express2b.module.express.controller.admin.workorder.vo;

import com.yunyi.express2b.framework.common.validation.InEnum;
import com.yunyi.express2b.module.express.enums.WorkOrderStatusEnum;
import com.yunyi.express2b.module.express.enums.WorkOrderTypeEnum;
import lombok.Data;

/**
 * 回调参数实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/6/5 14:31
 */
@Data
public class CallbackReqVO {

    private BasePOJO data;
    private String sign;

    @Data
    public static class BasePOJO{
        private Long consultId;//工单id
        @InEnum(value = WorkOrderStatusEnum.class)
        private Integer status;//工单状态
        private String expressSn;//运单号
        private String ucmOrderSn;//UCM订单号
        @InEnum(value = WorkOrderTypeEnum.class)
        private Integer type;//工单类型
        private String result;//客服回复  (处理结果)
        private String responseTime;//回复时间
    }
}
