-- 将该建表 SQL 语句，添加到 express2b-module-wallet-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "wallet_transactions" (
                                                     "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                     "wallet_id" bigint NOT NULL,
                                                     "agent_id" bigint NOT NULL,
                                                     "pay_order_sn" varchar,
                                                     "transaction_id" varchar NOT NULL,
                                                     "transaction_type" varchar NOT NULL,
                                                     "amount" int NOT NULL,
                                                     "balance_before_transaction" int,
                                                     "balance_after_transaction" int NOT NULL,
                                                     "available_balance_after_transaction" int,
                                                     "frozen_balance_after_transaction" int,
                                                     "related_order_id" varchar,
                                                     "related_withdrawal_id" bigint,
                                                     "related_event_id" varchar,
                                                     "status" varchar NOT NULL,
                                                     "description" varchar,
                                                     "available_at" varchar,
                                                     "remark" varchar,
                                                     "creator" varchar DEFAULT '',
                                                     "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                     "updater" varchar DEFAULT '',
                                                     "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                     "deleted" bit NOT NULL DEFAULT FALSE,
                                                     "tenant_id" bigint NOT NULL DEFAULT 0,
                                                     "version" int NOT NULL,
                                                     PRIMARY KEY ("id")
    ) COMMENT '钱包流水表';
-- 将该建表 SQL 语句，添加到 express2b-module-wallet-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "withdrawal_requests" (
                                                     "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                     "wallet_id" bigint NOT NULL,
                                                     "agent_id" bigint NOT NULL,
                                                     "request_no" varchar NOT NULL,
                                                     "amount" int NOT NULL,
                                                     "actual_amount" int,
                                                     "fee_amount" int NOT NULL,
                                                     "status" varchar NOT NULL,
                                                     "channel" varchar NOT NULL,
                                                     "channel_account_info" varchar,
                                                     "channel_transaction_id" varchar,
                                                     "failure_reason" varchar,
                                                     "audit_remark" varchar,
                                                     "auditor_id" bigint,
                                                     "requested_at" varchar NOT NULL,
                                                     "audited_at" varchar,
                                                     "processing_started_at" varchar,
                                                     "completed_at" varchar,
                                                     "is_auto_triggered" bit NOT NULL,
                                                     "creator" varchar DEFAULT '',
                                                     "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                     "updater" varchar DEFAULT '',
                                                     "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                     "deleted" bit NOT NULL DEFAULT FALSE,
                                                     "tenant_id" bigint NOT NULL DEFAULT 0,
                                                     "version" int NOT NULL,
                                                     PRIMARY KEY ("id")
    ) COMMENT '提现申请表';
-- 将该建表 SQL 语句，添加到 express2b-module-wallet-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "agent_wallets" (
                                               "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                               "agent_id" bigint NOT NULL,
                                               "available_balance" int NOT NULL,
                                               "frozen_balance" int NOT NULL,
                                               "total_profit_earned" int NOT NULL,
                                               "total_commission_earned" int NOT NULL,
                                               "total_withdrawn_amount" int NOT NULL,
                                               "status" int NOT NULL,
                                               "last_withdrawal_deadline" varchar,
                                               "creator" varchar DEFAULT '',
                                               "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                               "updater" varchar DEFAULT '',
                                               "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                               "deleted" bit NOT NULL DEFAULT FALSE,
                                               "tenant_id" bigint NOT NULL DEFAULT 0,
                                               "version" int NOT NULL,
                                               PRIMARY KEY ("id")
    ) COMMENT '代理商钱包表';
