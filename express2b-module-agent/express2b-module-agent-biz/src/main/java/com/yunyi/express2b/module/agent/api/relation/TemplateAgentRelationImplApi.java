package com.yunyi.express2b.module.agent.api.relation;

import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.agent.api.TemplateAgentRelationshipApi;
import com.yunyi.express2b.module.agent.api.dto.TemplateDTO;
import com.yunyi.express2b.module.agent.dal.mysql.relationship.TemplateAgentRelationshipMapperA;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实现代理商ID查询定价模版
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/22 下午4:05
 */

@Service
public class TemplateAgentRelationImplApi implements TemplateAgentRelationshipApi {
    @Resource
    private TemplateAgentRelationshipMapperA templateAgentRelationMapperA;

    /**
     * 根据代理商ID查询定价模版信息
     * @param agentId 代理商ID
     * @return
     */
    @Override
    public TemplateDTO getTemplateIdByAgentId(Long agentId) {
//       这个就是查询出来所有的数据
        TemplateDTO templateDTO = templateAgentRelationMapperA.selectByAgentId(agentId);
        return templateDTO;
    }
}