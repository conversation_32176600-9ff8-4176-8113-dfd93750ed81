package com.yunyi.express2b.module.express.utils;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;
class OrderNumberUtilTest {

    @Test
    void testGenerateCheckCode_Valid() {
        // 测试生成校验码功能（有效订单号）
        String orderNumber = "EA2504131234565612";
        String checkCode = OrderNumberUtil.generateCheckCode(orderNumber);
        assertNotNull(checkCode);
        assertEquals(1, checkCode.length());
        System.out.println("生成的校验码: " + checkCode);
    }

    @Test
    void testGenerateCheckCode_EmptyOrderNumber() {
        // 测试生成校验码功能（空订单号）
        assertThrows(IllegalArgumentException.class, () -> {
            OrderNumberUtil.generateCheckCode("");
        });
    }

    @Test
    void testGenerateCheckCode_NullOrderNumber() {
        // 测试生成校验码功能（null订单号）
        assertThrows(IllegalArgumentException.class, () -> {
            OrderNumberUtil.generateCheckCode(null);
        });
    }

    @Test
    void testValidateCheckCode_Valid() {
        // 测试验证校验码功能（有效情况）
        String fullOrderNumber = "EA25041312345656121";
        boolean isValid = OrderNumberUtil.validateCheckCode(fullOrderNumber);
        assertTrue(isValid);
    }

    @Test
    void testValidateCheckCode_Invalid() {
        // 测试验证校验码功能（无效情况）
        String fullOrderNumber = "EA25041312345656125";
        boolean isValid = OrderNumberUtil.validateCheckCode(fullOrderNumber);
        assertFalse(isValid);
    }

    @Test
    void testValidateCheckCode_InvalidLength() {
        // 测试验证校验码功能（长度不合法）
        String fullOrderNumber = "EA2504131234565"; // 长度不足19
        boolean isValid = OrderNumberUtil.validateCheckCode(fullOrderNumber);
        assertFalse(isValid);
    }
}