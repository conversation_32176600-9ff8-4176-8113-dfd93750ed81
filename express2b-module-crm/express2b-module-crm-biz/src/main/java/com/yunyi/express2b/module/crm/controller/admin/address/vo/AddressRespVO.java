package com.yunyi.express2b.module.crm.controller.admin.address.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 地址簿管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AddressRespVO extends PageParam {

    @Schema(description = "地址ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9416")
    @ExcelProperty("地址ID")
    private Long id;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2217")
    @ExcelProperty("用户ID")
    private Long memberId;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("姓名")
    private String name;

    @Schema(description = "电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("电话")
    private String phone;

    @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("详细地址")
    private String address;
    @Schema(description = "是否默认地址（0: 否, 1: 是）")
    @ExcelProperty("是否默认地址（0: 否, 1: 是）")
    private Integer isDefault;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED, example = "2027")
    @ExcelProperty("省")
    private Long provinceCode;

    @Schema(description = "地址code")
    @ExcelProperty("地址code")
    private String adccode;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED, example = "26429")
    @ExcelProperty("市")
    private Long cityCode;

    @Schema(description = "区", example = "25255")
    @ExcelProperty("区")
    private Long districtCode;

    @Schema(description = "省份名称")
    private String provinceName;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "区名称")
    private String districtName;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}