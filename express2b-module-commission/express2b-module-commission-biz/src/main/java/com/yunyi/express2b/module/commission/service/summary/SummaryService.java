package com.yunyi.express2b.module.commission.service.summary;

import java.util.*;
import jakarta.validation.*;
import com.yunyi.express2b.module.commission.controller.admin.summary.vo.*;
import com.yunyi.express2b.module.commission.dal.dataobject.summary.SummaryDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;

/**
 * 分润汇总 Service 接口
 *
 * <AUTHOR>
 */
public interface SummaryService {

    /**
     * 创建分润汇总
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSummary(@Valid SummarySaveReqVO createReqVO);

    /**
     * 更新分润汇总
     *
     * @param updateReqVO 更新信息
     */
    void updateSummary(@Valid SummarySaveReqVO updateReqVO);

    /**
     * 删除分润汇总
     *
     * @param id 编号
     */
    void deleteSummary(Long id);

    /**
     * 获得分润汇总
     *
     * @param id 编号
     * @return 分润汇总
     */
    SummaryDO getSummary(Long id);

    /**
     * 获得分润汇总分页
     *
     * @param pageReqVO 分页查询
     * @return 分润汇总分页
     */
    PageResult<SummaryDO> getSummaryPage(SummaryPageReqVO pageReqVO);

}