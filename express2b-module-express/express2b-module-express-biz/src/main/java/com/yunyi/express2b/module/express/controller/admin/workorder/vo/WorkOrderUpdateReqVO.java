package com.yunyi.express2b.module.express.controller.admin.workorder.vo;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yunyi.express2b.framework.common.enums.CommonStatusEnum;
import com.yunyi.express2b.framework.common.validation.InEnum;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;
import com.yunyi.express2b.module.express.enums.EmergencyLevelEnum;
import com.yunyi.express2b.module.express.enums.WorkOrderStatusEnum;
import com.yunyi.express2b.module.express.enums.WorkOrderTypeEnum;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 工单 修改请求VO
 *
 * <AUTHOR>
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WorkOrderUpdateReqVO{

    /**
     * 工单id
     */
    @NonNull
    private Long id;
    /**
     * 工单编号
     */
    private String workOrderSn;
    /**
     * SSO用户ID
     */
    private Long memberId;
    /**
     * 操作员ID
     */
    private Long operatorId;
    /**
     * 关联订单编号
     */
    private String orderSn;
    /**
     * 类型
     */
    @InEnum(value = WorkOrderTypeEnum.class)
    private Integer type;
    /**
     * 是否需要客服介入
     *
     * 枚举 {@link CommonStatusEnum}
     */
    @InEnum(value = CommonStatusEnum.class)
    private Integer needCustomerService;
    /**
     * 问题描述
     */
    private String passengerQuestion;
    /**
     * 工单状态
     *
     * 枚举 {@link WorkOrderStatusEnum}
     */
    @InEnum(value = WorkOrderStatusEnum.class)
    private Integer workOrderStatus;
    /**
     * 客服工号
     */
    private String customerServiceNo;
    /**
     * 客服受理时间
     */
    private LocalDateTime customerServiceTime;
    /**
     * 客服完成时间
     */
    private LocalDateTime customerServiceFinishTime;
    /**
     * 客服回复
     */
    private String customerServiceReply;
    /**
     * 工单备注
     */
    private String workNotes;
    /**
     * 紧急程度
     *
     * 枚举 {@link EmergencyLevelEnum}
     */
    @InEnum(value = EmergencyLevelEnum.class)
    private Integer emergencyLevel;
    /**
     * 一级工单类型编码
     */
    private String firstWorkType;
    /**
     * 二级工单类型编码
     */
    private String secondWorkType;

    /**
     * 三方Id，调用远程服务时返回并存储进来
     */
    private Long tripleId;
}