package com.yunyi.express2b.module.express.controller.admin.brand;

import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;
import com.yunyi.express2b.module.express.controller.admin.brand.vo.BrandPageReqVO;
import com.yunyi.express2b.module.express.controller.admin.brand.vo.BrandRespVO;
import com.yunyi.express2b.module.express.controller.admin.brand.vo.BrandSaveReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.brand.BrandDO;
import com.yunyi.express2b.module.express.service.brand.BrandService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;
/**
 * 快递品牌信息表 controller层
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 快递品牌信息表")
@RestController
@RequestMapping("/express/brand")
@Validated
public class BrandController {

    @Resource
    private BrandService brandService;

    @PostMapping("/create")
    @Operation(summary = "创建快递品牌信息表")
    @PreAuthorize("@ss.hasPermission('express:brand:create')")
    public CommonResult<Long> createBrand(@Valid @RequestBody BrandSaveReqVO createReqVO) {
        return success(brandService.createBrand(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新快递品牌信息表")
    @PreAuthorize("@ss.hasPermission('express:brand:update')")
    public CommonResult<Boolean> updateBrand(@Valid @RequestBody BrandSaveReqVO updateReqVO) {
        brandService.updateBrand(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除快递品牌信息表")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('express:brand:delete')")
    public CommonResult<Boolean> deleteBrand(@RequestParam("id") Long id) {
        brandService.deleteBrand(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得快递品牌信息表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('express:brand:query')")
    public CommonResult<BrandRespVO> getBrand(@RequestParam("id") Long id) {
        BrandDO brand = brandService.getBrand(id);
        return success(BeanUtils.toBean(brand, BrandRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得快递品牌信息表分页")
    @PreAuthorize("@ss.hasPermission('express:brand:query')")
    public CommonResult<PageResult<BrandRespVO>> getBrandPage(@Valid BrandPageReqVO pageReqVO) {
        PageResult<BrandDO> pageResult = brandService.getBrandPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BrandRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出快递品牌信息表 Excel")
    @PreAuthorize("@ss.hasPermission('express:brand:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportBrandExcel(@Valid BrandPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BrandDO> list = brandService.getBrandPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "快递品牌信息表.xls", "数据", BrandRespVO.class,
                        BeanUtils.toBean(list, BrandRespVO.class));
    }

}