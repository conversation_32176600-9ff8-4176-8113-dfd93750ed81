package com.yunyi.express2b.module.crm.service.cardlimitedignorecities;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.module.crm.controller.admin.cardlimitedignorecities.vo.CardlimitedIgnoreCitiesPageReqVO;
import com.yunyi.express2b.module.crm.controller.admin.cardlimitedignorecities.vo.CardlimitedIgnoreCitiesSaveReqVO;
import com.yunyi.express2b.module.crm.dal.dataobject.cardlimited.CardlimitedIgnoreCitiesDO;
import com.yunyi.express2b.module.crm.dal.mysql.cardlimited.CardlimitedIgnoreCitiesMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import static com.yunyi.express2b.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static com.yunyi.express2b.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertPojoEquals;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertServiceException;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomLongId;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomPojo;
import static com.yunyi.express2b.module.crm.enums.ErrorCodeConstants.CARDLIMITED_IGNORE_CITIES_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link CardlimitedIgnoreCitiesServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(CardlimitedIgnoreCitiesServiceImpl.class)
public class CardlimitedIgnoreCitiesServiceImplTest extends BaseDbUnitTest {

    @Resource
    private CardlimitedIgnoreCitiesServiceImpl cardlimitedIgnoreCitiesService;

    @Resource
    private CardlimitedIgnoreCitiesMapper cardlimitedIgnoreCitiesMapper;

    @Test
    public void testCreateCardlimitedIgnoreCities_success() {
        // 准备参数
        CardlimitedIgnoreCitiesSaveReqVO createReqVO = randomPojo(CardlimitedIgnoreCitiesSaveReqVO.class).setId(null);

        // 调用
        Long cardlimitedIgnoreCitiesId = cardlimitedIgnoreCitiesService.createCardlimitedIgnoreCities(createReqVO);
        // 断言
        assertNotNull(cardlimitedIgnoreCitiesId);
        // 校验记录的属性是否正确
        CardlimitedIgnoreCitiesDO cardlimitedIgnoreCities = cardlimitedIgnoreCitiesMapper.selectById(cardlimitedIgnoreCitiesId);
        assertPojoEquals(createReqVO, cardlimitedIgnoreCities, "id");
    }

    @Test
    public void testUpdateCardlimitedIgnoreCities_success() {
        // mock 数据
        CardlimitedIgnoreCitiesDO dbCardlimitedIgnoreCities = randomPojo(CardlimitedIgnoreCitiesDO.class);
        cardlimitedIgnoreCitiesMapper.insert(dbCardlimitedIgnoreCities);// @Sql: 先插入出一条存在的数据
        // 准备参数
        CardlimitedIgnoreCitiesSaveReqVO updateReqVO = randomPojo(CardlimitedIgnoreCitiesSaveReqVO.class, o -> {
            o.setId(dbCardlimitedIgnoreCities.getId()); // 设置更新的 ID
        });

        // 调用
        cardlimitedIgnoreCitiesService.updateCardlimitedIgnoreCities(updateReqVO);
        // 校验是否更新正确
        CardlimitedIgnoreCitiesDO cardlimitedIgnoreCities = cardlimitedIgnoreCitiesMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, cardlimitedIgnoreCities);
    }

    @Test
    public void testUpdateCardlimitedIgnoreCities_notExists() {
        // 准备参数
        CardlimitedIgnoreCitiesSaveReqVO updateReqVO = randomPojo(CardlimitedIgnoreCitiesSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> cardlimitedIgnoreCitiesService.updateCardlimitedIgnoreCities(updateReqVO), CARDLIMITED_IGNORE_CITIES_NOT_EXISTS);
    }

    @Test
    public void testDeleteCardlimitedIgnoreCities_success() {
        // mock 数据
        CardlimitedIgnoreCitiesDO dbCardlimitedIgnoreCities = randomPojo(CardlimitedIgnoreCitiesDO.class);
        cardlimitedIgnoreCitiesMapper.insert(dbCardlimitedIgnoreCities);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbCardlimitedIgnoreCities.getId();

        // 调用
        cardlimitedIgnoreCitiesService.deleteCardlimitedIgnoreCities(id);
        // 校验数据不存在了
        assertNull(cardlimitedIgnoreCitiesMapper.selectById(id));
    }

    @Test
    public void testDeleteCardlimitedIgnoreCities_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> cardlimitedIgnoreCitiesService.deleteCardlimitedIgnoreCities(id), CARDLIMITED_IGNORE_CITIES_NOT_EXISTS);
    }

}