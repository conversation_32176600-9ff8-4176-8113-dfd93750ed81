package com.yunyi.express2b.module.crm.controller.app.v1.carduser.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
/**
 *
 * <AUTHOR>
 */
@Schema(description = "持卡用户 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CardUserRespVO {

    @Schema(description = "注释", requiredMode = Schema.RequiredMode.REQUIRED, example = "11432")
    @ExcelProperty("注释")
    private Long id;

    @Schema(description = "卡种id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8566")
    @ExcelProperty("卡种id")
    private Long cardlimitedId;

    @Schema(description = "卡编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("卡编号")
    private String cardNumber;

    @Schema(description = "卡名-冗余字段", example = "张三")
    @ExcelProperty("卡名-冗余字段")
    private String cardName;

    @Schema(description = "持有用户id", example = "9674")
    @ExcelProperty("持有用户id")
    private Long personalId;

    @Schema(description = "购买时的总次数")
    @ExcelProperty("购买时的总次数")
    private Integer total;

    @Schema(description = "剩余可用次数")
    @ExcelProperty("剩余可用次数")
    private Integer remaining;

    @Schema(description = "生效日期（包含）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("生效日期（包含）")
    private LocalDateTime cardStartDate;

    @Schema(description = "结束日期（当日可用）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("结束日期（当日可用）")
    private LocalDateTime cardEndDate;

    @Schema(description = "启用状态 0-可用 1-不可用", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("启用状态 0-可用 1-不可用")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "支付单号")
    private String payOrderSn;;

}