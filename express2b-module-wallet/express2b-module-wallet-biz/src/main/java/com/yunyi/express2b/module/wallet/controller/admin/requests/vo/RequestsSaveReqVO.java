package com.yunyi.express2b.module.wallet.controller.admin.requests.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 提现申请新增/修改 Request VO")
@Data
public class RequestsSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17675")
    private Long id;

    @Schema(description = "钱包ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27708")
    @NotNull(message = "钱包ID不能为空")
    private Long walletId;

    @Schema(description = "代理商用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19995")
    @NotNull(message = "代理商用户ID不能为空")
    private Long agentId;

    @Schema(description = "提现申请唯一编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "提现申请唯一编号不能为空")
    private String requestNo;

    @Schema(description = "申请提现金额 (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "申请提现金额 (单位: 分)不能为空")
    private Integer amount;

    @Schema(description = "实际到账金额 (扣除手续费后) (单位: 分)")
    private Integer actualAmount;

    @Schema(description = "手续费金额 (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "手续费金额 (单位: 分)不能为空")
    private Integer feeAmount;

    @Schema(description = "提现状态 (具体取值见字典说明)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "提现状态 (具体取值见字典说明)不能为空")
    private String status;

    @Schema(description = "提现渠道 (WECHAT_PROFIT_SHARING)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "提现渠道 (WECHAT_PROFIT_SHARING)不能为空")
    private String channel;

    @Schema(description = "渠道账户信息 (如微信OpenID, 商户号)")
    private String channelAccountInfo;

    @Schema(description = "渠道交易号 (微信分账订单号/批次号)", example = "27661")
    private String channelTransactionId;

    @Schema(description = "失败原因", example = "不对")
    private String failureReason;

    @Schema(description = "审核备注", example = "随便")
    private String auditRemark;

    @Schema(description = "审核员ID", example = "30451")
    private Long auditorId;

    @Schema(description = "申请时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "申请时间不能为空")
    private LocalDateTime requestedAt;

    @Schema(description = "审核时间")
    private LocalDateTime auditedAt;

    @Schema(description = "开始处理时间（调用三方接口）")
    private LocalDateTime processingStartedAt;

    @Schema(description = "完成时间/失败时间")
    private LocalDateTime completedAt;

    @Schema(description = "是否自动提现触发", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否自动提现触发不能为空")
    private Boolean isAutoTriggered;

}