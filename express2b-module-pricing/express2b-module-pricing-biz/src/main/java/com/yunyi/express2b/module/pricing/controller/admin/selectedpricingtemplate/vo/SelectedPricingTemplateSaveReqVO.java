package com.yunyi.express2b.module.pricing.controller.admin.selectedpricingtemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 代理商为下级选择的价格模板记录新增/修改 Request VO")
@Data
public class SelectedPricingTemplateSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "21845")
    private Long id;

    @Schema(description = "操作选择的上级代理商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18109")
    @NotNull(message = "操作选择的上级代理商ID不能为空")
    private Long superiorAgentId;

    @Schema(description = "被指定模板的下级代理商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10787")
    @NotNull(message = "被指定模板的下级代理商ID不能为空")
    private Long downlineAgentId;

    @Schema(description = "选择的价格模板ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9485")
    @NotNull(message = "选择的价格模板ID不能为空")
    private Long pricingTemplateId;

}