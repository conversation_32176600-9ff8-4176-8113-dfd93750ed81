package com.yunyi.express2b.module.crm.controller.admin.cardlimitedignorecities.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 卡种禁用地区新增/修改 Request VO")
@Data
public class CardlimitedIgnoreCitiesSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "3947")
    private Long id;

    @Schema(description = "卡种id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7290")
    @NotNull(message = "卡种id不能为空")
    private Long cardlimitedId;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED, example = "12774")
    @NotNull(message = "省不能为空")
    private Long provinceCode;

    @Schema(description = "市", example = "5299")
    private Long cityCode;

    @Schema(description = "区", example = "4499")
    private Long districtCode;

}