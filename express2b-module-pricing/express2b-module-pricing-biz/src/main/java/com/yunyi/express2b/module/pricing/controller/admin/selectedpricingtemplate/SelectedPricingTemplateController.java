package com.yunyi.express2b.module.pricing.controller.admin.selectedpricingtemplate;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;

import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.*;

import com.yunyi.express2b.module.pricing.controller.admin.selectedpricingtemplate.vo.*;
import com.yunyi.express2b.module.pricing.dal.dataobject.selectedpricingtemplate.SelectedPricingTemplateDO;
import com.yunyi.express2b.module.pricing.service.selectedpricingtemplate.SelectedPricingTemplateService;

@Tag(name = "管理后台 - 代理商为下级选择的价格模板记录")
@RestController
@RequestMapping("/pricing/selected-pricing-template")
@Validated
public class SelectedPricingTemplateController {

    @Resource
    private SelectedPricingTemplateService selectedPricingTemplateService;

    @PostMapping("/create")
    @Operation(summary = "创建代理商为下级选择的价格模板记录")
    @PreAuthorize("@ss.hasPermission('pricing:selected-pricing-template:create')")
    public CommonResult<Long> createSelectedPricingTemplate(@Valid @RequestBody SelectedPricingTemplateSaveReqVO createReqVO) {
        return success(selectedPricingTemplateService.createSelectedPricingTemplate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代理商为下级选择的价格模板记录")
    @PreAuthorize("@ss.hasPermission('pricing:selected-pricing-template:update')")
    public CommonResult<Boolean> updateSelectedPricingTemplate(@Valid @RequestBody SelectedPricingTemplateSaveReqVO updateReqVO) {
        selectedPricingTemplateService.updateSelectedPricingTemplate(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代理商为下级选择的价格模板记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pricing:selected-pricing-template:delete')")
    public CommonResult<Boolean> deleteSelectedPricingTemplate(@RequestParam("id") Long id) {
        selectedPricingTemplateService.deleteSelectedPricingTemplate(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代理商为下级选择的价格模板记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pricing:selected-pricing-template:query')")
    public CommonResult<SelectedPricingTemplateRespVO> getSelectedPricingTemplate(@RequestParam("id") Long id) {
        SelectedPricingTemplateDO selectedPricingTemplate = selectedPricingTemplateService.getSelectedPricingTemplate(id);
        return success(BeanUtils.toBean(selectedPricingTemplate, SelectedPricingTemplateRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代理商为下级选择的价格模板记录分页")
    @PreAuthorize("@ss.hasPermission('pricing:selected-pricing-template:query')")
    public CommonResult<PageResult<SelectedPricingTemplateRespVO>> getSelectedPricingTemplatePage(@Valid SelectedPricingTemplatePageReqVO pageReqVO) {
        PageResult<SelectedPricingTemplateDO> pageResult = selectedPricingTemplateService.getSelectedPricingTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SelectedPricingTemplateRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代理商为下级选择的价格模板记录 Excel")
    @PreAuthorize("@ss.hasPermission('pricing:selected-pricing-template:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSelectedPricingTemplateExcel(@Valid SelectedPricingTemplatePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SelectedPricingTemplateDO> list = selectedPricingTemplateService.getSelectedPricingTemplatePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代理商为下级选择的价格模板记录.xls", "数据", SelectedPricingTemplateRespVO.class,
                        BeanUtils.toBean(list, SelectedPricingTemplateRespVO.class));
    }

}