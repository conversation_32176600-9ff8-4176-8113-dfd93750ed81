package com.yunyi.express2b.module.system.service.news;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.yunyi.express2b.module.system.controller.admin.news.vo.*;
import com.yunyi.express2b.module.system.dal.dataobject.news.NewsDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;

import com.yunyi.express2b.module.system.dal.mysql.news.NewsMapper;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.system.enums.ErrorCodeConstants.*;

/**
 * 文章管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NewsServiceImpl implements NewsService {

    @Resource
    private NewsMapper newsMapper;

    @Override
    public Long createNews(NewsSaveReqVO createReqVO) {
        // 插入
        NewsDO news = BeanUtils.toBean(createReqVO, NewsDO.class);
        newsMapper.insert(news);
        // 返回
        return news.getId();
    }

    @Override
    public void updateNews(NewsSaveReqVO updateReqVO) {
        // 校验存在
        validateNewsExists(updateReqVO.getId());
        // 更新
        NewsDO updateObj = BeanUtils.toBean(updateReqVO, NewsDO.class);
        newsMapper.updateById(updateObj);
    }

    @Override
    public void deleteNews(Long id) {
        // 校验存在
        validateNewsExists(id);
        // 删除
        newsMapper.deleteById(id);
    }

    private void validateNewsExists(Long id) {
        if (newsMapper.selectById(id) == null) {
            throw exception(NEWS_NOT_EXISTS);
        }
    }

    @Override
    public NewsDO getNews(Long id) {
        return newsMapper.selectById(id);
    }

    @Override
    public NewsDO getNewsByType(int type) {
        LambdaQueryWrapper<NewsDO> queryWrapper = new LambdaQueryWrapper<NewsDO>()
                .eq(NewsDO::getType, type)
                .orderByDesc(NewsDO::getCreateTime);

        return newsMapper.selectOne(queryWrapper);
    }

    @Override
    public PageResult<NewsDO> getNewsPage(NewsPageReqVO pageReqVO) {
        return newsMapper.selectPage(pageReqVO);
    }

}