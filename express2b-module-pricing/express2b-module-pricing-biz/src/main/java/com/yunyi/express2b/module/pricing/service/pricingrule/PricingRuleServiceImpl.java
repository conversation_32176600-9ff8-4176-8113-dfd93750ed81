package com.yunyi.express2b.module.pricing.service.pricingrule;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.yunyi.express2b.module.pricing.controller.admin.pricingrule.vo.*;
import com.yunyi.express2b.module.pricing.dal.dataobject.pricingrule.PricingRuleDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;

import com.yunyi.express2b.module.pricing.dal.mysql.pricingrule.PricingRuleMapper;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.pricing.enums.ErrorCodeConstants.*;

/**
 * 代理商定价（终端零售价）规则 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PricingRuleServiceImpl implements PricingRuleService {

    @Resource
    private PricingRuleMapper ruleMapper;

    @Override
    public Long createRule(PricingRuleSaveReqVO createReqVO) {
        // 插入
        PricingRuleDO rule = BeanUtils.toBean(createReqVO, PricingRuleDO.class);
        ruleMapper.insert(rule);
        // 返回
        return rule.getId();
    }

    @Override
    public void updateRule(PricingRuleSaveReqVO updateReqVO) {
        // 校验存在
        validateRuleExists(updateReqVO.getId());
        // 更新
        PricingRuleDO updateObj = BeanUtils.toBean(updateReqVO, PricingRuleDO.class);
        ruleMapper.updateById(updateObj);
    }

    @Override
    public void deleteRule(Long id) {
        // 校验存在
        validateRuleExists(id);
        // 删除
        ruleMapper.deleteById(id);
    }

    private void validateRuleExists(Long id) {
        if (ruleMapper.selectById(id) == null) {
            throw exception(RULE_NOT_EXISTS);
        }
    }

    @Override
    public PricingRuleDO getRule(Long id) {
        return ruleMapper.selectById(id);
    }

    @Override
    public PageResult<PricingRuleDO> getRulePage(PricingRulePageReqVO pageReqVO) {
        return ruleMapper.selectPage(pageReqVO);
    }

}