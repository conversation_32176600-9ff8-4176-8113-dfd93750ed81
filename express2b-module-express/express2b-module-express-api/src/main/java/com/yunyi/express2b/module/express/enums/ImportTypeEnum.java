package com.yunyi.express2b.module.express.enums;

import com.yunyi.express2b.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 *  批量导入类型 key: Import_Type_Enum
 * @version 1.0
 * @since 17
 * @since 2025/3/28 12:48
 */
@Getter
@AllArgsConstructor
public enum ImportTypeEnum implements ArrayValuable<Integer> {
    EXCEL(1, "Excel导入"),
    MANUAL(2, "手动粘贴");

    private final int value;
    private final String desc;

//    ImportType(int value, String desc) {
//        this.value = value;
//        this.desc = desc;
//    }

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(ImportTypeEnum::getValue).toArray(Integer[]::new);

    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}
