package com.yunyi.express2b.module.express.controller.app.v1.order.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/2 16:35
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PaymentStatusAmountReqVO {
    /**
     * 已经支付金额
     */
    private Integer paidAmount;

    /**
     * 待支付金额
     */
    private Integer unpaidAmount;

    /**
     * 待支付订单的数量
     */
    private Integer orderCounts;



//    /**
//     * 已支付订单列表
//     */
//    private List<OrderRespVO> paidOrderList;
//
//    /**
//     * 待支付订单列表
//     */
//    private List<OrderRespVO> unpaidOrderList;

}
