package com.yunyi.express2b.module.pricing.controller.admin.route;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;

import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.*;

import com.yunyi.express2b.module.pricing.controller.admin.route.vo.*;
import com.yunyi.express2b.module.pricing.dal.dataobject.route.RouteDO;
import com.yunyi.express2b.module.pricing.service.route.RouteService;

@Tag(name = "管理后台 - 快递全国运价基础")
@RestController
@RequestMapping("/pricing/route")
@Validated
public class RouteController {

    @Resource
    private RouteService routeService;

    @GetMapping("/get-linePrice")
    @Operation(summary = " 获取指定品牌和线路的基础价格信息")
    public CommonResult<PageResult<com.yunyi.express2b.module.pricing.controller.app.v1.route.vo.RouteRespVO>> getgetLinePrice(@Valid RoutePageReqVO pageReqVO) {
        PageResult<RouteDO> pageResult = routeService.getRoutePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, com.yunyi.express2b.module.pricing.controller.app.v1.route.vo.RouteRespVO.class));
    }


    @PostMapping("/create")
    @Operation(summary = "创建快递全国运价基础")
    @PreAuthorize("@ss.hasPermission('pricing:route:create')")
    public CommonResult<Long> createRoute(@Valid @RequestBody RouteSaveReqVO createReqVO) {
        return success(routeService.createRoute(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新快递全国运价基础")
    @PreAuthorize("@ss.hasPermission('pricing:route:update')")
    public CommonResult<Boolean> updateRoute(@Valid @RequestBody RouteSaveReqVO updateReqVO) {
        routeService.updateRoute(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除快递全国运价基础")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pricing:route:delete')")
    public CommonResult<Boolean> deleteRoute(@RequestParam("id") Long id) {
        routeService.deleteRoute(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得快递全国运价基础")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pricing:route:query')")
    public CommonResult<RouteRespVO> getRoute(@RequestParam("id") Long id) {
        RouteDO route = routeService.getRoute(id);
        return success(BeanUtils.toBean(route, RouteRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得快递全国运价基础分页")
    @PreAuthorize("@ss.hasPermission('pricing:route:query')")
    public CommonResult<PageResult<RouteRespVO>> getRoutePage(@Valid RoutePageReqVO pageReqVO) {
        PageResult<RouteDO> pageResult = routeService.getRoutePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RouteRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出快递全国运价基础 Excel")
    @PreAuthorize("@ss.hasPermission('pricing:route:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportRouteExcel(@Valid RoutePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<RouteDO> list = routeService.getRoutePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "快递全国运价基础.xls", "数据", RouteRespVO.class,
                        BeanUtils.toBean(list, RouteRespVO.class));
    }

}