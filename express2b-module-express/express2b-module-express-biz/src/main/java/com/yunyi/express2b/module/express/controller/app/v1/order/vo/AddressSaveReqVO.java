package com.yunyi.express2b.module.express.controller.app.v1.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 地址簿管理新增/修改 Request VO")
@Data
public class AddressSaveReqVO {

    @Schema(description = "地址ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9416")
    private Long id;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2217")
    @NotNull(message = "用户ID不能为空")
    private Long personalId;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "姓名不能为空")
    private String name;

    @Schema(description = "电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "电话不能为空")
    private String phone;

    @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "详细地址不能为空")
    private String address;

    @Schema(description = "地址类型（RECIPIENT: 收件地址, SHIPPER: 寄件地址，NULL：可以出现在任意标签中）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "地址类型（RECIPIENT: 收件地址, SHIPPER: 寄件地址，NULL：可以出现在任意标签中）不能为空")
    private String addressType;

    @Schema(description = "是否默认地址（0: 否, 1: 是）")
    private String isDefault;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED, example = "2027")
    @NotNull(message = "省不能为空")
    private Long provinceCode;

    @Schema(description = "地址code")
    private String adccode;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED, example = "26429")
    @NotNull(message = "市不能为空")
    private Long cityCode;

    @Schema(description = "区", example = "25255")
    private Long districtCode;

}