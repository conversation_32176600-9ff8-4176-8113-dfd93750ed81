package com.yunyi.express2b.module.express.controller.admin.locations.vo;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 全国行政区信息管理分页 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 全国行政区信息管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LocationsPageReqVO extends PageParam {

    @Schema(description = "父级ID，表示当前记录的父级记录的ID", example = "4024")
    private Integer parentId;

    @Schema(description = "父级代码，表示当前记录的父级记录的代码")
    private String parentCode;

    @Schema(description = "级别，表示地址的层级（如省、市、区、街道等）")
    private String level;

    @Schema(description = "城市代码，表示所在城市的代码")
    private String citycode;

    @Schema(description = "行政区划代码，表示所在行政区划的代码")
    private String adcode;

    @Schema(description = "名称，表示地址的具体名称（如省名、市名、区名、街道名等）", example = "张三")
    private String name;

    @Schema(description = "GPS纬度，表示地址的纬度坐标")
    private BigDecimal gpsLat;

    @Schema(description = "GPS经度，表示地址的经度坐标")
    private BigDecimal gpsLon;

    @Schema(description = "最后更新时间，表示记录的最后更新时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] lastTime;

    @Schema(description = "状态，表示记录的状态（通常用于标识记录是否有效等）", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}