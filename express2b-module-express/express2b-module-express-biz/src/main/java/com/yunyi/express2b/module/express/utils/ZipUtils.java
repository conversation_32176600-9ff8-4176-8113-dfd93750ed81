package com.yunyi.express2b.module.express.utils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

public class ZipUtils {

    /**
     * 解压ZIP文件
    */
    public static Map<String, byte[]> extractZipFile(InputStream inputStream, Charset charset) throws IOException {
        Map<String, byte[]> fileMap = new HashMap<>();
        // 将输入流写入临时文件
        Path tempFile = Files.createTempFile("zip-", ".tmp");
        try (OutputStream out = Files.newOutputStream(tempFile)) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = inputStream.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        }
        // 使用 ZipFile 并指定编码
        try (ZipFile zipFile = new ZipFile(tempFile.toFile(), charset)) {
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                if (!entry.isDirectory()) {
                    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                    try (InputStream entryStream = zipFile.getInputStream(entry)) {
                        byte[] buffer = new byte[1024];
                        int len;
                        while ((len = entryStream.read(buffer)) > 0) {
                            outputStream.write(buffer, 0, len);
                        }
                    }
                    fileMap.put(entry.getName(), outputStream.toByteArray());
                }
            }
        } finally {
            Files.deleteIfExists(tempFile); // 删除临时文件
        }
        return fileMap;
    }
}
