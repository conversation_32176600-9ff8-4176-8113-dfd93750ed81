package com.yunyi.express2b.module.express.service.batchorder;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.crm.api.address.AddressQueryApi;
import com.yunyi.express2b.module.express.controller.admin.batchorder.vo.BatchOrderPageReqVO;
import com.yunyi.express2b.module.express.controller.admin.batchorder.vo.BatchOrderSaveReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.batchorder.BatchOrderDO;
import com.yunyi.express2b.module.express.dal.mysql.batchorder.BatchOrderMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.BATCH_ORDER_NOT_EXISTS;

/**
 * 批量订单管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BatchOrderServiceImpl implements BatchOrderService {

    @Resource
    private BatchOrderMapper batchOrderMapper;
    @Resource
    private AddressQueryApi api;

    /**
     * 创建批量订单管理
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Long createBatchOrder(BatchOrderSaveReqVO createReqVO) {
        // 插入
        BatchOrderDO batchOrder = BeanUtils.toBean(createReqVO, BatchOrderDO.class);
        batchOrderMapper.insert(batchOrder);
        // 返回
        return batchOrder.getId();
    }

    /**
     * 更新批量订单管理
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateBatchOrder(BatchOrderSaveReqVO updateReqVO) {
        // 校验存在
        validateBatchOrderExists(updateReqVO.getId());
        // 更新
        BatchOrderDO updateObj = BeanUtils.toBean(updateReqVO, BatchOrderDO.class);
        batchOrderMapper.updateById(updateObj);
    }

    /**
     * 删除批量
     * @param id 编号
     */
    @Override
    public void deleteBatchOrder(Long id) {
        // 校验存在
        validateBatchOrderExists(id);
        // 删除
        batchOrderMapper.deleteById(id);
    }

    /**
     * 校验批量订单管理是否存在
     * @param id
     */
    private void validateBatchOrderExists(Long id) {
        if (batchOrderMapper.selectById(id) == null) {
            throw exception(BATCH_ORDER_NOT_EXISTS);
        }
    }

    /**
     * 获得批量订单管理
     * @param id 编号
     * @return
     */
    @Override
    public BatchOrderDO getBatchOrder(Long id) {
        return batchOrderMapper.selectById(id);
    }

    /**
     * 获得批量订单管理分页
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<BatchOrderDO> getBatchOrderPage(BatchOrderPageReqVO pageReqVO) {
        return batchOrderMapper.selectPage(pageReqVO);
    }

}