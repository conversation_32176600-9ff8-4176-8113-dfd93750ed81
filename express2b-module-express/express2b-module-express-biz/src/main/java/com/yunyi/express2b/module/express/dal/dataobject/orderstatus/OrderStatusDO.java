package com.yunyi.express2b.module.express.dal.dataobject.orderstatus;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 订单状态管理 DO
 *
 * <AUTHOR>
 */
@TableName("express2b_order_status")
@KeySequence("express2b_order_status_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderStatusDO extends BaseDO {

    /**
     * 状态ID
     */
    @TableId
    private Long id;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 订单状态-变更前
     */
    private String statusStart;
    /**
     * 订单状态-变更后
     */
    private String statusEnd;
    /**
     * 操作人ID（系统或用户）
     */
    private Long operatorId;
    /**
     * 操作人姓名
     */
    private String operatorName;
    /**
     * 备注（状态变更原因）
     */
    private String remark;

}