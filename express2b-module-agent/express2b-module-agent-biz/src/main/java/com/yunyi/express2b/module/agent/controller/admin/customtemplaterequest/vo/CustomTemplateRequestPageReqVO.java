package com.yunyi.express2b.module.agent.controller.admin.customtemplaterequest.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 9:53
 */
@Schema(description = "管理后台 - 代理商自定义价格模板申请记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CustomTemplateRequestPageReqVO extends PageParam {

    @Schema(description = "申请代理商ID", example = "27662")
    private Long agentId;

    @Schema(description = "申请的模板参数（JSON格式）")
    private String requestedTemplateDetails;

    @Schema(description = "申请状态（0-待审核, 1-已批准, 2-已拒绝）", example = "1")
    private Integer status;

    @Schema(description = "若批准，关联到pricing模块中为此申请创建的模板ID", example = "16587")
    private Long approvedTemplateId;

    @Schema(description = "申请时间")
    private LocalDateTime requestedAt;

    @Schema(description = "审核时间")
    private LocalDateTime reviewedAt;

    @Schema(description = "审核备注")
    private String reviewerComments;

    @Schema(description = "申请备注")
    private String remarks;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}