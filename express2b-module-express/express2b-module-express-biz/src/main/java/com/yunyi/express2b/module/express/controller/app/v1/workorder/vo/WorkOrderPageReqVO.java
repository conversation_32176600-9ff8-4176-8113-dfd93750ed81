package com.yunyi.express2b.module.express.controller.app.v1.workorder.vo;

import com.yunyi.express2b.framework.common.validation.InEnum;
import com.yunyi.express2b.module.express.enums.WorkOrderStatusEnum;
import com.yunyi.express2b.module.express.enums.WorkOrderTypeEnum;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;

@Schema(description = "工单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WorkOrderPageReqVO extends PageParam {
    @Schema(description = "工单id")
    private Long id;

    @Schema(description = "工单编号")
    private String workOrderSn;

    @Schema(description = "SSO用户ID", example = "3349")
    private Long memberId;


    @Schema(description = "关联订单编号")
    private String orderSn;

    @Schema(description = "工单类型", example = "1")
    @InEnum(value = WorkOrderTypeEnum.class)
    private Integer type;


    @Schema(description = "工单状态", example = "1")
    @InEnum(value = WorkOrderStatusEnum.class)
    private Integer workOrderStatus;


    @Schema(description = "三方平台Id", example = "1")
    private Long tripleId;





}