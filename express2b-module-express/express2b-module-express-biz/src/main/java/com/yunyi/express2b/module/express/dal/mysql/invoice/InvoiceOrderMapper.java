package com.yunyi.express2b.module.express.dal.mysql.invoice;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;

import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorder.InvoiceOrderPageDTO;
import com.yunyi.express2b.module.express.dal.dataobject.invoice.InvoiceOrderDO;
import org.apache.ibatis.annotations.Mapper;


/**
 * 订单发票 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InvoiceOrderMapper extends BaseMapperX<InvoiceOrderDO> {

    default PageResult<InvoiceOrderDO> selectPage(InvoiceOrderPageDTO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InvoiceOrderDO>()
                .eqIfPresent(InvoiceOrderDO::getId, reqVO.getId())
                .eqIfPresent(InvoiceOrderDO::getOrderNo, reqVO.getOrderNo())
                .eqIfPresent(InvoiceOrderDO::getInvoiceCode, reqVO.getInvoiceCode())
                .eqIfPresent(InvoiceOrderDO::getInvoiceNumber, reqVO.getInvoiceNumber())
                .eqIfPresent(InvoiceOrderDO::getInvoiceUrl, reqVO.getInvoiceUrl())
                .eqIfPresent(InvoiceOrderDO::getAmount, reqVO.getAmount())
                .eqIfPresent(InvoiceOrderDO::getEmail, reqVO.getEmail())
                .eqIfPresent(InvoiceOrderDO::getInvoiceStatus, reqVO.getInvoiceStatus())
                .eqIfPresent(InvoiceOrderDO::getRejectReason, reqVO.getRejectReason())
                .eqIfPresent(InvoiceOrderDO::getReviewerId, reqVO.getReviewerId())
                .eqIfPresent(InvoiceOrderDO::getCreator, reqVO.getCreator())
                .betweenIfPresent(InvoiceOrderDO::getReviewTime, reqVO.getReviewTime())
                .betweenIfPresent(InvoiceOrderDO::getInvoiceTime, reqVO.getInvoiceTime())
                .eqIfPresent(InvoiceOrderDO::getCustomerServiceNo, reqVO.getCustomerServiceNo())
                .betweenIfPresent(InvoiceOrderDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InvoiceOrderDO::getId));
    }

}