package com.yunyi.express2b.module.crm.controller.admin.cardstream.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
/**
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 卡流水分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CardStreamPageReqVO extends PageParam {

    @Schema(description = "卡id", example = "23124")
    private Long cardId;

    @Schema(description = "用户id", example = "10837")
    private Long personalId;

    @Schema(description = "订单id", example = "26145")
    private Long orderId;

    @Schema(description = "支付单号", example = "24716")
    private Long payId;

    @Schema(description = "退款单号", example = "30297")
    private Long refundId;

    @Schema(description = "兑换码")
    private String redeemCode;

    @Schema(description = "增减次数")
    private Integer incDec;

    @Schema(description = "变化原因", example = "不对")
    private String reason;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}