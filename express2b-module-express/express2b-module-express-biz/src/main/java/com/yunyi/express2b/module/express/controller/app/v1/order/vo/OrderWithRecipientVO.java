package com.yunyi.express2b.module.express.controller.app.v1.order.vo;

import com.yunyi.express2b.module.express.controller.admin.order.vo.OrderSaveReqVO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.RecipientVO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/29 19:43
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderWithRecipientVO {
    private OrderSaveReqVO createReqVO;
    private List<RecipientVO> recipientVOs;
    private List<OrderDO> reqVOList;
}
