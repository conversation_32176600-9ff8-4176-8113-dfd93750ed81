package com.yunyi.express2b.module.crm.service.appcpmpany;

import cn.hutool.core.bean.BeanUtil;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.io.FileUtils;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.security.core.LoginUser;
import com.yunyi.express2b.framework.security.core.util.CrmSecurityFrameworkUtils;
import com.yunyi.express2b.module.crm.controller.admin.companyinfo.vo.CompanyInfoRespVO;
import com.yunyi.express2b.module.crm.controller.admin.personalinfo.vo.PersonalInfoRespVO;
import com.yunyi.express2b.module.crm.dal.dataobject.companyaudithistory.CompanyAuditHistoryDO;
import com.yunyi.express2b.module.crm.dal.dataobject.companyinfo.CompanyInfoDO;
import com.yunyi.express2b.module.crm.dal.dataobject.personalinfo.PersonalInfoDO;
import com.yunyi.express2b.module.crm.dal.mysql.appcompany.AppCompanyMapper;
import com.yunyi.express2b.module.crm.dal.mysql.companyaudithistory.CompanyAuditHistoryMapper;
import com.yunyi.express2b.module.crm.dal.mysql.companyinfo.CompanyInfoMapper;
import com.yunyi.express2b.module.crm.dal.mysql.personalinfo.PersonalInfoMapper;
import com.yunyi.express2b.module.crm.enums.AuditStateEnum;
import com.yunyi.express2b.module.crm.enums.CompanyTypeEnum;
import com.yunyi.express2b.module.crm.enums.ErrorCodeConstants;
import com.yunyi.framework.api.login.api.image.ImageApi;
import com.yunyi.framework.api.login.api.image.vo.UploadImageRequest;
import com.yunyi.framework.api.login.api.image.vo.UploadImageResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.crm.enums.ErrorCodeConstants.AUDIT_FAIL;

/**
 * 企业信息impl
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/26 13:42
 */

@Service
@Validated
@Slf4j
public class AppCompanyServiceImpl implements AppCompanyService{
    @Autowired
    private AppCompanyMapper appCompanyMapper;

    @Autowired
    private CompanyInfoMapper companyInfoMapper;
    @Resource
    private PersonalInfoMapper personalInfoMapper;
    @Resource
    private CompanyAuditHistoryMapper companyAuditHistoryMapper;
    @Resource
    private ImageApi imageApi;

    public static final Integer APPLICATIONID = 7;
    private static final Integer ISQRCODE = 0;
    // 认证企业用户
    @Override
    public boolean authenticateEnterpriseUser(PersonalInfoRespVO personalInfoRespVO) {
       /* List<MultipartFile> files = personalInfoRespVO.getFile();
        if (files == null || files.isEmpty()) {
            throw exception(ErrorCodeConstants.COMPANY_NOT_EXISTS);
        }*/

        // 查询企业是否存在
        CompanyInfoDO companyInfoDO1 = companyInfoMapper.selectAllCompany(personalInfoRespVO.getCreditCode());
        if (companyInfoDO1 != null) {
            throw exception(ErrorCodeConstants.COMPANY_EXISTS);
        }

        PersonalInfoDO infoDO = personalInfoMapper.selectById(CrmSecurityFrameworkUtils.getLoginUserId());
        if (infoDO.getState() != null) {
            throw exception(ErrorCodeConstants.COMPANY_AUDIT_HISTORY_EXISTS);
        }

        Long companyId = personalInfoRespVO.getCompanyId();
        CompanyInfoDO companyInfoDO = companyInfoMapper.selectBycompanyId(companyId);
        if (companyInfoDO != null ) {
            throw exception(ErrorCodeConstants.COMPANY_AUDIT_HISTORY_EXISTS);
        }

        // 图片上传逻辑
       // List<String> imageUrls = uploadImages(files);

        // 更新身份证件并设置审核状态
        setAuditPendingStateAndSaveData(CrmSecurityFrameworkUtils.getLoginUserId(), personalInfoRespVO, personalInfoRespVO.getImageUrls());

        return true;
    }

    /**
     * 上传图片并返回图片URL列表
     */
    private List<String> uploadImages(List<MultipartFile> files) {
        List<String> imageUrls = new ArrayList<>();
        UploadImageRequest request = new UploadImageRequest();
        request.setApplicationId(APPLICATIONID);
        request.setIsQrCode(ISQRCODE);

        try {
            for (MultipartFile multipartFile : files) {
                File tempFile = FileUtils.createTempFile(multipartFile.getBytes());
                List<File> fileList = new ArrayList<>();
                fileList.add(tempFile);
                request.setFile(fileList);

                CommonResult<List<UploadImageResponse>> uploadImageResponses = imageApi.uploadImage(request);
                if (uploadImageResponses != null && uploadImageResponses.getData() != null) {
                    for (UploadImageResponse response : uploadImageResponses.getData()) {
                        imageUrls.add(response.getImgUrl());
                    }
                }
            }
        } catch (IOException e) {
            throw exception(AUDIT_FAIL);
        }
        return imageUrls;
    }

    /**
     * 设置审核初始状态并保存数据
     */
    private void setAuditPendingStateAndSaveData(Long memberId, PersonalInfoRespVO personalInfoRespVO, List<String> imageUrls) {
        // 设置审核状态
        Integer auditPendingCode = Integer.valueOf(AuditStateEnum.AUDIT_PENDING.getCode());
        personalInfoRespVO.setState(auditPendingCode);

        // 更新身份证件
        personalInfoRespVO.setFrontCard(imageUrls.get(0));
        personalInfoRespVO.setReverseCard(imageUrls.get(1));

        // 转换并保存个人认证信息
        PersonalInfoDO saveReqVO = BeanUtils.toBean(personalInfoRespVO, PersonalInfoDO.class);
        saveReqVO.setId(personalInfoRespVO.getId());
        saveReqVO.setMemberId(memberId);
        saveReqVO.setCompanyId(personalInfoRespVO.getCompanyId());
        personalInfoMapper.updateById(saveReqVO);

        // 保存审核历史记录
        CompanyAuditHistoryDO companyAuditHistoryDO = new CompanyAuditHistoryDO();
        companyAuditHistoryDO.setCompanyId(memberId);
        companyAuditHistoryDO.setCompanyName(saveReqVO.getMemberName());
        companyAuditHistoryDO.setStatus(auditPendingCode);
        companyAuditHistoryMapper.insert(companyAuditHistoryDO);

        // 保存企业信息
        CompanyInfoDO companyInfoDO = new CompanyInfoDO();
        companyInfoDO.setCompanyId(memberId);
        companyInfoDO.setCompanyType(String.valueOf(CompanyTypeEnum.COMPANY.getCode()));
        companyInfoDO.setContactName(saveReqVO.getMemberName());
        companyInfoDO.setContactPhone(saveReqVO.getMemberPhone());
        companyInfoDO.setCompanyName(saveReqVO.getMemberName());
        companyInfoDO.setCompanyLicense(saveReqVO.getFrontCard());
        companyInfoDO.setCompanyLicense1(saveReqVO.getReverseCard());
        companyInfoDO.setStatus(auditPendingCode);
        companyInfoDO.setCreditCode(personalInfoRespVO.getCreditCode());
        companyInfoMapper.insert(companyInfoDO);
    }

    @Override
    public boolean updateContact(CompanyInfoRespVO companyInfoReqVO) {
        CompanyInfoDO infoDO = BeanUtil.toBean(companyInfoReqVO, CompanyInfoDO.class);
         companyInfoMapper.updateById(infoDO);
        return true;
    }
}
