package com.yunyi.express2b.module.crm.service.cardlimitedbrand;
import com.yunyi.express2b.module.crm.dal.dataobject.cardlimited.CardlimitedBrandDO;
import com.yunyi.express2b.module.crm.dal.mysql.cardlimited.CardlimitedBrandMapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.yunyi.express2b.module.crm.controller.admin.cardlimitedbrand.vo.*;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.crm.enums.ErrorCodeConstants.*;

/**
 * 卡种可使用品牌 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CardlimitedBrandServiceImpl implements CardlimitedBrandService {

    @Resource
    private CardlimitedBrandMapper cardlimitedBrandMapper;

    /**
     * 创建卡种可使用品牌
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Long createCardlimitedBrand(CardlimitedBrandSaveReqVO createReqVO) {
        // 插入
        CardlimitedBrandDO cardlimitedBrand = BeanUtils.toBean(createReqVO, CardlimitedBrandDO.class);
        cardlimitedBrandMapper.insert(cardlimitedBrand);
        // 返回
        return cardlimitedBrand.getId();
    }

    /**
     * 更新卡种可使用品牌
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateCardlimitedBrand(CardlimitedBrandSaveReqVO updateReqVO) {
        // 校验存在
        validateCardlimitedBrandExists(updateReqVO.getId());
        // 更新
        CardlimitedBrandDO updateObj = BeanUtils.toBean(updateReqVO, CardlimitedBrandDO.class);
        cardlimitedBrandMapper.updateById(updateObj);
    }

    /**
     * 删除卡种可使用品牌
     * @param id 编号
     */
    @Override
    public void deleteCardlimitedBrand(Long id) {
        // 校验存在
        validateCardlimitedBrandExists(id);
        // 删除
        cardlimitedBrandMapper.deleteById(id);
    }

    /**
     * 校验卡种可使用品牌是否存在
     * @param id
     */
    private void validateCardlimitedBrandExists(Long id) {
        if (cardlimitedBrandMapper.selectById(id) == null) {
            throw exception(CARDLIMITED_BRAND_NOT_EXISTS);
        }
    }

    /**
     * 获得卡种可使用品牌
     * @param id 编号
     * @return 卡种可使用品牌
     */

    @Override
    public CardlimitedBrandDO getCardlimitedBrand(Long id) {
        return cardlimitedBrandMapper.selectById(id);
    }

    /**
     * 获得卡种可使用品牌分页
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<CardlimitedBrandDO> getCardlimitedBrandPage(CardlimitedBrandPageReqVO pageReqVO) {

        return cardlimitedBrandMapper.selectPage(pageReqVO);
    }

}