package com.yunyi.express2b.module.wallet.controller.admin.wallets.vo;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 代理商钱包分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WalletsPageReqVO extends PageParam {

    @Schema(description = "代理商用户ID", example = "24147")
    private Long agentId;

    @Schema(description = "可用余额 (单位: 分)")
    private Integer availableBalance;

    @Schema(description = "冻结余额 (单位: 分)")
    private Integer frozenBalance;

    @Schema(description = "累计利润总额 (单位: 分)")
    private Integer totalProfitEarned;

    @Schema(description = "累计分润总额 (单位: 分)")
    private Integer totalCommissionEarned;

    @Schema(description = "累计已提现总额 (单位: 分)")
    private Integer totalWithdrawnAmount;

    @Schema(description = "钱包状态 (具体取值见字典说明)", example = "1")
    private Integer status;

    @Schema(description = "最晚提现日 (针对最早一笔可用资金)")
    private LocalDate lastWithdrawalDeadline;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "乐观锁版本号")
    private Integer version;

}