package com.yunyi.express2b.module.wallet.api.TransationsApi.dto;

import java.time.LocalDateTime;

/**
 * WalletDetailsDTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/26 10:20
 */

public class TransactionsDetailsDTO {
    // 主键ID
    private Long id;
    // 钱包ID
    private Long walletId;
    // 代理商用户ID
    private Long agentId;
    // 统一登陆平台的订单支付编号
    private String payOrderSn;
    // 平台唯一交易流水号
    private String transactionId;
    // 交易类型(具体取值见字典说明)
    private String transactionType;
    // 交易金额 (正为收入,负为支出) (单位: 分)
    private Integer amount;
    // 交易前账户总余额 (可用+冻结) (单位: 分)
    private Integer balanceBeforeTransaction;
    // 交易后账户总余额 (可用+冻结) (单位: 分)
    private Integer balanceAfterTransaction;
    // 交易后可用余额 (单位: 分)
    private Integer availableBalanceAfterTransaction;
    // 交易后冻结余额 (单位: 分)
    private Integer frozenBalanceAfterTransaction;
    // 关联订单号
    private String relatedOrderId;
    // 关联提现申请ID
    private Long relatedWithdrawalId;
    // 关联风险事件ID或其他业务ID
    private String relatedEventId;
    // 流水状态 (具体取值见字典说明)
    private String status;
    // 交易描述
    private String description;
    // 资金预计可用时间 (解冻时间)
    private LocalDateTime availableAt;
    // 备注信息 (如操作人,调账原因)
    private String remark;
    // 创建者ID
    private String creator;
    // 创建时间
    private LocalDateTime createTime;
    // 更新者ID
    private String updater;
    // 更新时间
    private LocalDateTime updateTime;
    // 逻辑删除标志
    private boolean deleted;
    // 租户编号
    private Long tenantId;
    // 乐观锁版本号
    private Integer version;


}
