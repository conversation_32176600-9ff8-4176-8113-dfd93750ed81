package com.yunyi.express2b.module.agent.api.agentprofile;


import com.yunyi.express2b.framework.common.exception.ErrorCode;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.agent.api.AgentApi;
import com.yunyi.express2b.module.agent.api.dto.AgentInfoDTO;
import com.yunyi.express2b.module.agent.api.dto.AgentProfileRespDTO;
import com.yunyi.express2b.module.agent.api.dto.RelationShipDTO;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.AgentProfileDO;
import com.yunyi.express2b.module.agent.dal.dataobject.levelconfig.LevelConfigDO;
import com.yunyi.express2b.module.agent.dal.mysql.agentprofile.AgentProfileMapper;
import com.yunyi.express2b.module.agent.dal.mysql.levelconfig.LevelConfigMapper;
import com.yunyi.express2b.module.agent.dal.mysql.relationship.RelationshipMapper;
import com.yunyi.express2b.module.agent.service.agentprofile.AgentProfileService;
import com.yunyi.express2b.module.agent.service.levelconfig.LevelConfigService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.AGENT_NOT_EXISTS;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 9:53
 */
@Service
public class AgentApiServiceImpl implements AgentApi {

    @Resource
    private AgentProfileService agentProfileService;
    @Resource
    private LevelConfigService levelConfigService;
    @Resource
    private AgentProfileMapper agentProfileMapper;
    @Resource
    private LevelConfigMapper levelConfigMapper;
    @Resource
    private RelationshipMapper relationshipMapper;


    /**
     * 查询代理商的所有上级（带层级距离)
     * @param agentId
     * @return
     */
    @Override
    public List<RelationShipDTO> getListByAgentId(Long agentId) {
        return BeanUtils.toBean(relationshipMapper.getlistByAgentId(agentId),  RelationShipDTO.class);
    }

    /**
     * 获取代理商信息+等级名称
     * @param agentId
     * @return
     */
    @Override
    public AgentProfileRespDTO getAgentProfile(Long agentId) {
        //通过代理商id查询代理商基本信息以及等级名称
        AgentProfileRespDTO agentProfileRespDTO = agentProfileMapper.selectAgentAndLevelNameById(agentId);
        //判断代理商存在
        if(agentProfileRespDTO==null){
            throw exception(AGENT_NOT_EXISTS);
        }
        return agentProfileRespDTO;

    }

    /**
     * 查询距离当前代理商最近的特定级别上级
     * @param agentId
     * @param levelId
     * @return
     */
    @Override
    public RelationShipDTO getAgentProfileByAgentIdAndLevelId(Long agentId, Long levelId) {
        RelationShipDTO relationShipDTO = relationshipMapper.selectAgentByAgentIdAndLevelId(agentId,levelId);
        relationShipDTO.setNearestSpecificLevelAncestorId(relationShipDTO.getNearestSpecificLevelAncestorId());
        if (relationShipDTO == null){
            throw exception(AGENT_NOT_EXISTS);
        }
        return relationShipDTO;
    }


    /**
     * 根据ID查询代理商信息
     * @param agentId
     * @return
     */
    @Override
    public AgentInfoDTO getAgentInfo(Long agentId) {
        AgentProfileDO agentProfileDO = agentProfileMapper.selectById(agentId);
        //校验agent是否存在
        if (agentProfileDO == null) {
            throw exception(AGENT_NOT_EXISTS);
        }
        return BeanUtils.toBean(agentProfileDO,  AgentInfoDTO.class);
    }


    /**
     * 获取代理商等级ID(testAccess)
     * @return
     */
    @Override
    public Long getAgentLevelId() {
        //根据用户id查询代理商等级
        AgentProfileDO agentProfileDO = agentProfileMapper.getLevelId(SecurityFrameworkUtils.getLoginUserId());
        //判断该联系人的存在
        if(agentProfileDO.getLevelId()==null){
            throw exception(AGENT_NOT_EXISTS);
        }
        //返回等级ID
        return agentProfileDO.getLevelId();
    }


}
