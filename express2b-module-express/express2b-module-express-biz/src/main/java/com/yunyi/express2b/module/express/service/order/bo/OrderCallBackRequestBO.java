package com.yunyi.express2b.module.express.service.order.bo;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/14 14:51
 */

@Data
@Builder
public class OrderCallBackRequestBO {
    public OrderCallbackData data;

    // 内层data对象
    @Data
    public static class OrderCallbackData {
        private String volume;
        private String courierName;
        private Integer originalPrice;
        private String courierMobile;
        private String ucmOrderSn;
        private Integer freight;
        private String thirdOrderSn;
        private Integer weight;
        private String pickupCode;
        private String expressSn;
        private Integer status;
        private String cancelMsg;
        private Integer feePrice;
        private Integer defPrice;
        // 新增字段：费用明细
        private List<FeeDetail> feeDetails;
    }
    @Data
    public static class FeeDetail{
        // 费用类型
        private String type;
        // 费用描述
        private String desc;
        // 费用价格
        private Integer price;
        // 支付状态
        private String payStatus;
    }

}
