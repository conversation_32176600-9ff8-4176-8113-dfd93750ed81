package com.yunyi.express2b.module.crm.service.cardlimited;
import java.time.LocalDateTime;
import java.util.*;
import com.alibaba.fastjson.JSONObject;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.module.crm.controller.app.v1.carflimited.vo.CardLimitedVO;
import com.yunyi.express2b.module.crm.controller.app.v1.carflimited.vo.CardUserVO;
import com.yunyi.express2b.module.crm.controller.app.v1.carflimited.vo.RedeemVO;
import com.yunyi.express2b.module.crm.dal.dataobject.cardlimited.CardlimitedBrandDO;
import com.yunyi.express2b.module.crm.dal.dataobject.cardlimited.CardlimitedIgnoreCitiesDO;
import jakarta.validation.*;
import com.yunyi.express2b.module.crm.controller.admin.cardlimited.vo.*;
import com.yunyi.express2b.module.crm.dal.dataobject.cardlimited.CardlimitedDO;

import com.yunyi.express2b.framework.common.pojo.PageResult;

/**
 * 卡种信息 Service 接口
 *
 * <AUTHOR>
 */
public interface CardlimitedService {

    /**
     * 创建卡种信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCardlimited(@Valid CardlimitedSaveReqVO createReqVO);

    /**
     * 更新卡种信息
     *
     * @param updateReqVO 更新信息
     */
    void updateCardlimited(@Valid CardlimitedSaveReqVO updateReqVO);

    /**
     * 删除卡种信息
     *
     * @param id 编号
     */
    void deleteCardlimited(Long id);

    /**
     * 获得卡种信息
     *
     * @param id 编号
     * @return 卡种信息
     */
    CardlimitedDO getCardlimited(Long id);

    /**
     * 获得卡种信息分页
     *
     * @param pageReqVO 分页查询
     * @return 卡种信息分页
     */
    PageResult<CardlimitedDO> getCardlimitedPage(CardlimitedPageReqVO pageReqVO);

    // ==================== 子表（卡种可使用品牌） ====================

    /**
     * 获得卡种可使用品牌列表
     *
     * @param cardlimitedId 卡种id
     * @return 卡种可使用品牌列表
     */
    List<CardlimitedBrandDO> getCardlimitedBrandListByCardlimitedId(Long cardlimitedId);

    // ==================== 子表（卡种禁用地区） ====================

    /**
     * 获得卡种禁用地区列表
     *
     * @param cardlimitedId 卡种id
     * @return 卡种禁用地区列表
     */
    List<CardlimitedIgnoreCitiesDO> getCardlimitedIgnoreCitiesListByCardlimitedId(Long cardlimitedId);

    /**
     * 获取店铺卡片信息列表
     *
     * 该方法用于获取一个包含多个CardShopVo对象的列表，每个对象代表一个店铺的卡片信息
     * 主要用于展示店铺卡片页面，提供给用户浏览和选择
     *
     * @return 包含店铺卡片信息的列表，如果无数据则返回空列表
     */
   List<CardShopVo> getShopCardPage();

    /**
     * 根据个人标识获取所有关联的卡用户信息
     *
     * @param personalId 个人标识，用于标识特定的个人用户
     * @return 返回一个包含CardUserVo对象的列表，每个对象代表个人的一张卡的用户信息
     */
    List<CardUserVO> getUserCardAll(Long personalId, Integer status, LocalDateTime time,Integer type);

    /**
     * 发送消息接口
     * 该方法用于发送与兑换相关的消息，具体逻辑由调用者提供
     *
     * @param redeemVo 兑换信息对象，包含发送消息所需的各种参数
     * @return 返回一个通用结果对象，包含消息发送结果的字符串
     */
    CommonResult<String> sendMessage(RedeemVO redeemVo);
    /**
     * 根据ID获取卡限制信息
     * @param id 卡限制的唯一标识符，用于标识特定的卡限制信息
     * @return 返回一个CardLimited对象，包含所请求的卡限制信息如果找不到对应的卡限制，可能返回null
     */
    CardLimitedVO getCardlimitedOne(Long id);
    /**
     * 用户购买卡片服务
     * @return 返回一个CommonResult对象，包含购买操作的布尔结果
     * true表示购买成功，false表示购买失败
     */
    CommonResult<JSONObject> userBuyCard(Long id);

    /**
     * 更新卡片用户的状态为已过期
     */
    void updateCardUserStatusToTwo();
}