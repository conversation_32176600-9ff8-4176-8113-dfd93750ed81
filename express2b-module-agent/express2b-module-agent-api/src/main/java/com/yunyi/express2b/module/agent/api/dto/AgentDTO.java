package com.yunyi.express2b.module.agent.api.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 代理商 DTO
 *
 * <AUTHOR>
 */
@Data
public class AgentDTO {
    
    /**
     * 代理商编号
     */
    private Long id;
    
    /**
     * 代理商名称
     */
    private String name;
    
    /**
     * 代理商联系人
     */
    private String contactName;
    
    /**
     * 代理商联系电话
     */
    private String contactMobile;
    
    /**
     * 代理商状态
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
} 