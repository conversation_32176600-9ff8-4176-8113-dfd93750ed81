package com.yunyi.express2b.module.express.controller.admin.subscribe.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 用户订阅信息表分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SubscribePageReqVO extends PageParam {

    @Schema(description = "模板类型", example = "2")
    private Integer tempType;

    @Schema(description = "账户id", example = "12093")
    private Long memberId;

    @Schema(description = "是否订阅  1-已订阅  0-未订阅")
    private Integer subscribe;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}