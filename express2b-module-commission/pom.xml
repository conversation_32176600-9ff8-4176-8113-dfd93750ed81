<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.yunyi.express2b</groupId>
        <artifactId>express2b</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <modules>
        <module>express2b-module-commission-api</module>
        <module>express2b-module-commission-biz</module>
    </modules>
    <artifactId>express2b-module-commission</artifactId>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>
    <description>
        分润计算模块，根据预设规则和订单数据，负责各级代理商分润的计算、记录和分配。
    </description>

</project> 