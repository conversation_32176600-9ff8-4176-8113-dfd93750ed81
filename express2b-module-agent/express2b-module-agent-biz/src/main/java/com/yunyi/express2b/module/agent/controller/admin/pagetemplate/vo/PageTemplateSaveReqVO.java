package com.yunyi.express2b.module.agent.controller.admin.pagetemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 页面模板新增/修改 Request VO")
@Data
public class PageTemplateSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6")
    private Integer id;

    @Schema(description = "模块名称，如轮播、功能区等", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "模块名称，如轮播、功能区等不能为空")
    private String moduleName;

    @Schema(description = "组件类型,按钮等", example = "2")
    private String componentType;

    @Schema(description = "展示名称", example = "赵六")
    private String displayName;

    @Schema(description = "0隐藏 1显示")
    private Integer state;

    @Schema(description = "对应dom名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "对应dom名不能为空")
    private String dom;

    @Schema(description = "父模块的dom名，用于标识子模块所属父模块，若为顶级模块则为null")
    private String parentDom;

    @Schema(description = "子模块ID，若无子模块则为NULL", example = "13756")
    private String subModuleId;

    @Schema(description = "模块或子模块展示顺序")
    private Integer sortOrder;

}