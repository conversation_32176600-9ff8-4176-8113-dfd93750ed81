package com.yunyi.express2b.module.express.controller.app.v1.products.vo;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 物品信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductsPageReqVO extends PageParam {

    @Schema(description = "订单ID", example = "14678")
    private Long orderId;

    @Schema(description = "物品名称", example = "张三")
    private String name;

    @Schema(description = "物品重量(克/g)")
    private Integer weight;

    @Schema(description = "物品分类")
    private String category;

    @Schema(description = "长度(cm)")
    private Integer length;

    @Schema(description = "宽度(cm)")
    private Integer width;

    @Schema(description = "高度(cm)")
    private Integer height;

    @Schema(description = "物品详细描述", example = "你猜")
    private String description;

    @Schema(description = "物品价格", example = "3710")
    private Integer price;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "物品数量", example = "3710")
    private Integer quantity;

}