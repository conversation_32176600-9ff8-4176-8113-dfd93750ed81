package com.yunyi.express2b.module.infra.service.file;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.yunyi.express2b.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.io.FileUtils;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.infra.controller.admin.file.vo.file.FileCreateReqVO;
import com.yunyi.express2b.module.infra.controller.admin.file.vo.file.FilePageReqVO;
import com.yunyi.express2b.module.infra.controller.admin.file.vo.file.FilePresignedUrlRespVO;
import com.yunyi.express2b.module.infra.dal.dataobject.file.FileDO;
import com.yunyi.express2b.module.infra.dal.mysql.file.FileMapper;
import com.yunyi.express2b.module.infra.framework.file.core.client.FileClient;
import com.yunyi.express2b.module.infra.framework.file.core.client.s3.FilePresignedUrlRespDTO;
import com.yunyi.express2b.module.infra.framework.file.core.utils.FileTypeUtils;
import com.yunyi.framework.api.login.api.image.ImageApi;
import com.yunyi.framework.api.login.api.image.vo.UploadImageRequest;
import com.yunyi.framework.api.login.api.image.vo.UploadImageResponse;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.infra.enums.ErrorCodeConstants.FILE_NOT_EXISTS;
import static com.yunyi.express2b.module.infra.enums.ErrorCodeConstants.FILE_RE_NULL;

/**
 * 文件 Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class FileServiceImpl implements FileService {

    @Resource
    private FileConfigService fileConfigService;

    @Resource
    private FileMapper fileMapper;
    @Resource
    private ImageApi imageApi;

    public static final Integer APPLICATIONID = 7;
    private static final Integer ISQRCODE = 0;

    @Override
    public PageResult<FileDO> getFilePage(FilePageReqVO pageReqVO) {
        return fileMapper.selectPage(pageReqVO);
    }

    @Override
    @SneakyThrows
    public String createFile(String name, String path, byte[] content) {
        // 计算默认的 path 名
        String type = FileTypeUtils.getMineType(content, name);
        if (StrUtil.isEmpty(path)) {
            path = FileUtils.generatePath(content, name);
        }
        // 如果 name 为空，则使用 path 填充
        if (StrUtil.isEmpty(name)) {
            name = path;
        }

        // 上传到文件存储器
        FileClient client = fileConfigService.getMasterFileClient();
        Assert.notNull(client, "客户端(master) 不能为空");
        String url = client.upload(content, path, type);

        // 保存到数据库
        FileDO file = new FileDO();
        file.setConfigId(client.getId());
        file.setName(name);
        file.setPath(path);
        file.setUrl(url);
        file.setType(type);
        file.setSize(content.length);
        fileMapper.insert(file);
        return url;
    }

    @Override
    public Long createFile(FileCreateReqVO createReqVO) {
        FileDO file = BeanUtils.toBean(createReqVO, FileDO.class);
        fileMapper.insert(file);
        return file.getId();
    }

    @Override
    public void deleteFile(Long id) throws Exception {
        // 校验存在
        FileDO file = validateFileExists(id);

        // 从文件存储器中删除
        FileClient client = fileConfigService.getFileClient(file.getConfigId());
        Assert.notNull(client, "客户端({}) 不能为空", file.getConfigId());
        client.delete(file.getPath());

        // 删除记录
        fileMapper.deleteById(id);
    }

    private FileDO validateFileExists(Long id) {
        FileDO fileDO = fileMapper.selectById(id);
        if (fileDO == null) {
            throw exception(FILE_NOT_EXISTS);
        }
        return fileDO;
    }

    @Override
    public byte[] getFileContent(Long configId, String path) throws Exception {
        FileClient client = fileConfigService.getFileClient(configId);
        Assert.notNull(client, "客户端({}) 不能为空", configId);
        return client.getContent(path);
    }

    @Override
    public FilePresignedUrlRespVO getFilePresignedUrl(String path) throws Exception {
        FileClient fileClient = fileConfigService.getMasterFileClient();
        FilePresignedUrlRespDTO presignedObjectUrl = fileClient.getPresignedObjectUrl(path);
        return BeanUtils.toBean(presignedObjectUrl, FilePresignedUrlRespVO.class,
                object -> object.setConfigId(fileClient.getId()));
    }

    /**
     * 统一上传图片并返回图片URL列表 并且插入数据
     * @param file
     * @return
     */
    @Override
    public List<String> imageUploadMsg(File file) {
        List<File> files = new ArrayList<>();
        files.add(file);
        List<String> strings = uploadImages(files);
        // 判断 strings 是否为空
        if (strings == null || strings.isEmpty()) {
            List<String> strings1 = new ArrayList<>();
            strings1.add("图片上传异常");
            return strings1;
        }
        return uploadImages(files);
    }

    /**
     * 上传图片方法
     */
    private List<String> uploadImages(List<File> files) {
        List<String> imageUrls = new ArrayList<>();
        UploadImageRequest request = new UploadImageRequest();
        request.setApplicationId(APPLICATIONID);
        request.setIsQrCode(ISQRCODE);
        try {
            for (File file : files) {
                List<File> fileList = new ArrayList<>();
                fileList.add(file);
                request.setFile(fileList);

                CommonResult<List<UploadImageResponse>> uploadImageResponses = imageApi.uploadImage(request);
                if (uploadImageResponses != null && uploadImageResponses.getData() != null) {
                    for (UploadImageResponse response : uploadImageResponses.getData()) {
                        imageUrls.add(response.getImgUrl());
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return imageUrls;
    }


}
