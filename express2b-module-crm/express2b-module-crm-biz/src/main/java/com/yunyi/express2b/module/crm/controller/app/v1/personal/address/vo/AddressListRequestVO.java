package com.yunyi.express2b.module.crm.controller.app.v1.personal.address.vo;


import com.yunyi.express2b.framework.common.pojo.PageParam;
import lombok.Data;

/**
 * 地址查询Vo
 */
@Data
public class AddressListRequestVO extends PageParam {
    /**
     * 地址类型，{@link com.yunyi.express2b.module.crm.enums.AddressTypeEnum}
     */
    private String addressType;

    /**
     * 是否是默认地址
     * {@link com.yunyi.express2b.module.crm.enums.AddressDefaultEnum}
     */
    private Integer isDefault;

    /**
     * 地址id
     */
    private Long id;
}
