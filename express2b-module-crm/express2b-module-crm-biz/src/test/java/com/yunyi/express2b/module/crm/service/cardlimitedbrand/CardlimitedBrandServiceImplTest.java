package com.yunyi.express2b.module.crm.service.cardlimitedbrand;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.module.crm.controller.admin.cardlimitedbrand.vo.CardlimitedBrandPageReqVO;
import com.yunyi.express2b.module.crm.controller.admin.cardlimitedbrand.vo.CardlimitedBrandSaveReqVO;
import com.yunyi.express2b.module.crm.dal.dataobject.cardlimited.CardlimitedBrandDO;
import com.yunyi.express2b.module.crm.dal.mysql.cardlimited.CardlimitedBrandMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import static com.yunyi.express2b.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertPojoEquals;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertServiceException;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomLongId;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomPojo;
import static com.yunyi.express2b.module.crm.enums.ErrorCodeConstants.CARDLIMITED_BRAND_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link CardlimitedBrandServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(CardlimitedBrandServiceImpl.class)
public class CardlimitedBrandServiceImplTest extends BaseDbUnitTest {

    @Resource
    private CardlimitedBrandServiceImpl cardlimitedBrandService;

    @Resource
    private CardlimitedBrandMapper cardlimitedBrandMapper;

    @Test
    public void testCreateCardlimitedBrand_success() {
        // 准备参数
        CardlimitedBrandSaveReqVO createReqVO = randomPojo(CardlimitedBrandSaveReqVO.class).setId(null);

        // 调用
        Long cardlimitedBrandId = cardlimitedBrandService.createCardlimitedBrand(createReqVO);
        // 断言
        assertNotNull(cardlimitedBrandId);
        // 校验记录的属性是否正确
        CardlimitedBrandDO cardlimitedBrand = cardlimitedBrandMapper.selectById(cardlimitedBrandId);
        assertPojoEquals(createReqVO, cardlimitedBrand, "id");
    }

    @Test
    public void testUpdateCardlimitedBrand_success() {
        // mock 数据
        CardlimitedBrandDO dbCardlimitedBrand = randomPojo(CardlimitedBrandDO.class);
        cardlimitedBrandMapper.insert(dbCardlimitedBrand);// @Sql: 先插入出一条存在的数据
        // 准备参数
        CardlimitedBrandSaveReqVO updateReqVO = randomPojo(CardlimitedBrandSaveReqVO.class, o -> {
            o.setId(dbCardlimitedBrand.getId()); // 设置更新的 ID
        });

        // 调用
        cardlimitedBrandService.updateCardlimitedBrand(updateReqVO);
        // 校验是否更新正确
        CardlimitedBrandDO cardlimitedBrand = cardlimitedBrandMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, cardlimitedBrand);
    }

    @Test
    public void testUpdateCardlimitedBrand_notExists() {
        // 准备参数
        CardlimitedBrandSaveReqVO updateReqVO = randomPojo(CardlimitedBrandSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> cardlimitedBrandService.updateCardlimitedBrand(updateReqVO), CARDLIMITED_BRAND_NOT_EXISTS);
    }

    @Test
    public void testDeleteCardlimitedBrand_success() {
        // mock 数据
        CardlimitedBrandDO dbCardlimitedBrand = randomPojo(CardlimitedBrandDO.class);
        cardlimitedBrandMapper.insert(dbCardlimitedBrand);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbCardlimitedBrand.getId();

        // 调用
        cardlimitedBrandService.deleteCardlimitedBrand(id);
        // 校验数据不存在了
        assertNull(cardlimitedBrandMapper.selectById(id));
    }

    @Test
    public void testDeleteCardlimitedBrand_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> cardlimitedBrandService.deleteCardlimitedBrand(id), CARDLIMITED_BRAND_NOT_EXISTS);
    }

}