package com.yunyi.express2b.module.express.dal.dataobject.brand;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 快递品牌信息表 DO
 *
 * <AUTHOR>
 */
@TableName("express2b_brand")
@KeySequence("express2b_brand_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrandDO extends BaseDO {

    /**
     * 品牌ID，唯一标识品牌
     */
    @TableId
    private Long id;
    /**
     * 品牌唯一标识字符串，不能为空
     */
    private String brandKey;
    /**
     * 品牌展示的名词
     */
    private String brandNameDisplay;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 品牌描述
     */
    private String description;
    /**
     * 品牌图标地址
     */
    private String brandIcon;

}