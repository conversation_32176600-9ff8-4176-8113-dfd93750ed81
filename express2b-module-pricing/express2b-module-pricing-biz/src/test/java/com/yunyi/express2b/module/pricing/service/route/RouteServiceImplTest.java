package com.yunyi.express2b.module.pricing.service.route;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.module.pricing.controller.admin.route.vo.RoutePageReqVO;
import com.yunyi.express2b.module.pricing.controller.admin.route.vo.RouteSaveReqVO;
import com.yunyi.express2b.module.pricing.dal.dataobject.route.RouteDO;
import com.yunyi.express2b.module.pricing.dal.mysql.route.RouteMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import static com.yunyi.express2b.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static com.yunyi.express2b.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertPojoEquals;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertServiceException;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomLongId;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomPojo;
import static com.yunyi.express2b.module.pricing.enums.ErrorCodeConstants.ROUTE_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link RouteServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(RouteServiceImpl.class)
public class RouteServiceImplTest extends BaseDbUnitTest {

    @Resource
    private RouteServiceImpl routeService;

    @Resource
    private RouteMapper routeMapper;

    @Test
    public void testCreateRoute_success() {
        // 准备参数
        RouteSaveReqVO createReqVO = randomPojo(RouteSaveReqVO.class).setId(null);

        // 调用
        Long routeId = routeService.createRoute(createReqVO);
        // 断言
        assertNotNull(routeId);
        // 校验记录的属性是否正确
        RouteDO route = routeMapper.selectById(routeId);
        assertPojoEquals(createReqVO, route, "id");
    }

    @Test
    public void testUpdateRoute_success() {
        // mock 数据
        RouteDO dbRoute = randomPojo(RouteDO.class);
        routeMapper.insert(dbRoute);// @Sql: 先插入出一条存在的数据
        // 准备参数
        RouteSaveReqVO updateReqVO = randomPojo(RouteSaveReqVO.class, o -> {
            o.setId(dbRoute.getId()); // 设置更新的 ID
        });

        // 调用
        routeService.updateRoute(updateReqVO);
        // 校验是否更新正确
        RouteDO route = routeMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, route);
    }

    @Test
    public void testUpdateRoute_notExists() {
        // 准备参数
        RouteSaveReqVO updateReqVO = randomPojo(RouteSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> routeService.updateRoute(updateReqVO), ROUTE_NOT_EXISTS);
    }

    @Test
    public void testDeleteRoute_success() {
        // mock 数据
        RouteDO dbRoute = randomPojo(RouteDO.class);
        routeMapper.insert(dbRoute);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbRoute.getId();

        // 调用
        routeService.deleteRoute(id);
        // 校验数据不存在了
        assertNull(routeMapper.selectById(id));
    }

    @Test
    public void testDeleteRoute_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> routeService.deleteRoute(id), ROUTE_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetRoutePage() {
        // mock 数据
        RouteDO dbRoute = randomPojo(RouteDO.class, o -> { // 等会查询到
            o.setBrandKey(null);
            o.setLineKey(null);
            o.setSendProvince(null);
            o.setReceiveProvince(null);
            o.setSendProvinceCode(null);
            o.setReceiveProvinceCode(null);
            o.setProductCode(null);
            o.setFirstWeight(null);
            o.setAdditionalWeight(null);
            o.setCostFirstPrice(null);
            o.setCostOverPrice(null);
            o.setOriginalFirstPrice(null);
            o.setOriginalOverPrice(null);
            o.setCreateTime(null);
        });
        routeMapper.insert(dbRoute);
        // 测试 brandKey 不匹配
        routeMapper.insert(cloneIgnoreId(dbRoute, o -> o.setBrandKey(null)));
        // 测试 lineKey 不匹配
        routeMapper.insert(cloneIgnoreId(dbRoute, o -> o.setLineKey(null)));
        // 测试 sendProvince 不匹配
        routeMapper.insert(cloneIgnoreId(dbRoute, o -> o.setSendProvince(null)));
        // 测试 receiveProvince 不匹配
        routeMapper.insert(cloneIgnoreId(dbRoute, o -> o.setReceiveProvince(null)));
        // 测试 sendProvinceCode 不匹配
        routeMapper.insert(cloneIgnoreId(dbRoute, o -> o.setSendProvinceCode(null)));
        // 测试 receiveProvinceCode 不匹配
        routeMapper.insert(cloneIgnoreId(dbRoute, o -> o.setReceiveProvinceCode(null)));
        // 测试 productCode 不匹配
        routeMapper.insert(cloneIgnoreId(dbRoute, o -> o.setProductCode(null)));
        // 测试 firstWeight 不匹配
        routeMapper.insert(cloneIgnoreId(dbRoute, o -> o.setFirstWeight(null)));
        // 测试 additionalWeight 不匹配
        routeMapper.insert(cloneIgnoreId(dbRoute, o -> o.setAdditionalWeight(null)));
        // 测试 costFirstPrice 不匹配
        routeMapper.insert(cloneIgnoreId(dbRoute, o -> o.setCostFirstPrice(null)));
        // 测试 costOverPrice 不匹配
        routeMapper.insert(cloneIgnoreId(dbRoute, o -> o.setCostOverPrice(null)));
        // 测试 originalFirstPrice 不匹配
        routeMapper.insert(cloneIgnoreId(dbRoute, o -> o.setOriginalFirstPrice(null)));
        // 测试 originalOverPrice 不匹配
        routeMapper.insert(cloneIgnoreId(dbRoute, o -> o.setOriginalOverPrice(null)));
        // 测试 createTime 不匹配
        routeMapper.insert(cloneIgnoreId(dbRoute, o -> o.setCreateTime(null)));
        // 准备参数
        RoutePageReqVO reqVO = new RoutePageReqVO();
        reqVO.setBrandKey(null);
        reqVO.setLineKey(null);
        reqVO.setSendProvince(null);
        reqVO.setReceiveProvince(null);
        reqVO.setSendProvinceCode(null);
        reqVO.setReceiveProvinceCode(null);
        reqVO.setProductCode(null);
        reqVO.setFirstWeight(null);
        reqVO.setAdditionalWeight(null);
        reqVO.setCostFirstPrice(null);
        reqVO.setCostOverPrice(null);
        reqVO.setOriginalFirstPrice(null);
        reqVO.setOriginalOverPrice(null);
        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

        // 调用
        PageResult<RouteDO> pageResult = routeService.getRoutePage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbRoute, pageResult.getList().get(0));
    }

}