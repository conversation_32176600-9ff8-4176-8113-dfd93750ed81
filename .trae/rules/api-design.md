---
description: 
globs: 
alwaysApply: true
---
# API设计规范

## Controller层设计

### 接口分类

- **前端接口**: 面向终端客户的接口，用于小程序等前端应用访问
  - **URL前缀**: `app-api` (由框架统一控制，Controller中无需声明)
  - **包路径**: `com.yunyi.express2b.module.{module-name}.controller.app`
  - **版本控制**: 在app目录下使用子目录表示版本，如`v1`、`v2`等，实现接口版本管理
  - **功能组织**: 按业务领域划分子包，保持与后端接口相似的结构
  - **前端接口VO对象**：
    - 存放位置：com.yunyi.express2b.module.{module-name}.controller.app.vo
    - 版本控制：可按版本进一步划分子目录，如app.v1.vo、app.v2.vo等
    - 适用场景：面向小程序等终端客户的接口返回对象
  
- **后端接口**: 面向管理Web系统的接口
  - **URL前缀**: `admin-api` (由框架统一控制，Controller中无需声明)
  - **包路径**: `com.yunyi.express2b.module.{module-name}.controller.admin`
  - **功能组织**: 按业务领域划分子包，保持与前端接口相似的结构
  - **后端接口VO对象**：
    - **存放位置**：com.yunyi.express2b.module.{module-name}.controller.admin.vo
    - **适用场景**：面向管理Web系统的接口返回对象

- **命名规范**
  - **请求参数VO**: 功能+ReqVO，如UserCreateReqVO、OrderQueryReqVO
  - **响应结果VO**: 功能+RespVO，如UserProfileRespVO、OrderDetailRespVO
  - **分页请求对象**：实体+PageReqVO，如UserPageReqVO

### 接口设计原则

- **RESTful风格**: 严格按照RESTful API设计规范实现
  - GET: 查询资源
  - POST: 创建资源
  - PUT: 更新资源（全量更新）
  - PATCH: 部分更新资源
  - DELETE: 删除资源
  
- **URL命名规范**:
  - 使用名词复数形式表示资源集合: `/users`, `/orders`
  - 使用名词单数+ID表示具体资源: `/user/{id}`, `/order/{id}`
  - 子资源使用子路径: `/user/{id}/addresses`
  - 查询参数使用query string: `/users?status=active`
  - **URL字符要求**: URL地址不允许出现大写字符，只能使用小写字符、横线和数字

- **统一响应格式**: 所有接口返回`CommonResult<T>`响应结构

- **版本兼容性**: 不同版本接口保持向后兼容性
  - 添加新字段时不应影响旧版本客户端
  - 修改字段含义时使用新的字段名称
  - 重大变更通过版本升级处理

- **安全控制**: 根据接口类型实施不同的安全认证机制
  - 前端接口: 用户Token认证
  - 后端接口: 管理员Token认证和权限控制
  - 敏感操作需要额外的权限验证

- **幂等性设计**: 关键操作实现接口幂等性
  - GET、PUT、DELETE方法本身应是幂等的
  - POST方法通过唯一标识符实现幂等性

### 接口文档

- 使用Swagger (Knife4j) 自动生成API文档
- 对每个接口添加详细的注释说明
- 包含请求参数说明、响应示例、错误码说明等
- 文档示例：

```java
@ApiOperation(value = "创建用户", notes = "创建新用户并返回用户ID")
@ApiImplicitParams({
    @ApiImplicitParam(name = "createUserVO", value = "用户创建信息", required = true, dataType = "CreateUserVO")
})
@ApiResponses({
    @ApiResponse(code = 200, message = "操作成功", response = CommonResult.class),
    @ApiResponse(code = 400, message = "参数校验失败"),
    @ApiResponse(code = 500, message = "系统内部错误")
})
@PostMapping("/users")
public CommonResult<Long> createUser(@RequestBody @Valid CreateUserVO createUserVO) {
    // 方法实现
}
```

## 统一返回对象

系统使用 `CommonResult<T>` 类作为所有接口的统一返回结构，该类位于 `com.yunyi.express2b.framework.common.pojo` 包中。

### CommonResult 主要属性

* `code`: Integer类型，表示响应状态码。成功时为预定义的成功代码，失败时为具体的错误代码。
* `data`: T类型，泛型数据字段，仅在请求成功时存在。
* `msg`: String类型，描述错误信息，成功时不包含此字段。

### 使用规范

* **成功响应**: 使用 `CommonResult.success(T data)` 方法返回成功结果
* **错误响应**: 使用多种重载的 `CommonResult.error(...)` 方法返回错误结果
* **异常处理**: Controller层应统一捕获异常并通过CommonResult返回标准化错误

## 错误码管理

### 错误码命名规范

- 错误码格式：A-BB-CCC，其中：
  - A: 分类（1-系统错误，2-业务错误）
  - BB: 模块编号（00-通用，01-用户，02-订单等）
  - CCC: 具体错误编号

### 两级错误码体系

- **全局错误码**: 所有模块共享的错误码，定义在 `com.yunyi.express2b.framework.common.exception.GlobalErrorCodeConstants`
- **模块错误码**: 特定模块的错误码，定义在 `com.yunyi.express2b.module.{module-name}.api.enums.{ModuleName}ErrorCodeConstants`

### 错误码使用原则

- 在抛出业务异常时，使用错误码枚举，避免直接使用错误码数字值
- 模块内部错误使用模块错误码，跨模块错误使用全局错误码
- 错误码定义必须包含错误码、错误信息与错误详细描述
- 错误信息应当简洁明了，便于前端显示

## 模块间接口设计

- 模块间接口定义在`api`子模块中
- 方法命名应清晰表达功能
- 参数和返回值应使用DTO而非DO
- 明确异常处理策略，区分业务异常和系统异常

## 接口契约稳定性

- API接口一旦发布，应保持稳定
- 接口变更应通过版本管理
- 避免频繁修改已发布的接口参数和返回值结构
- 弃用旧接口时应提供充分的时间和明确的迁移路径




