package com.yunyi.express2b.module.crm.controller.admin.cardlimitedbrand;

import com.yunyi.express2b.module.crm.dal.dataobject.cardlimited.CardlimitedBrandDO;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;

import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.*;

import com.yunyi.express2b.module.crm.controller.admin.cardlimitedbrand.vo.*;
import com.yunyi.express2b.module.crm.service.cardlimitedbrand.CardlimitedBrandService;

@Tag(name = "管理后台 - 卡种可使用品牌")
@RestController
@RequestMapping("/crm/cardlimited-brand")
@Validated
public class CardlimitedBrandController {

    @Resource
    private CardlimitedBrandService cardlimitedBrandService;

    @PostMapping("/create")
    @Operation(summary = "创建卡种可使用品牌")
    @PreAuthorize("@ss.hasPermission('crm:cardlimited-brand:create')")
    public CommonResult<Long> createCardlimitedBrand(@Valid @RequestBody CardlimitedBrandSaveReqVO createReqVO) {
        return success(cardlimitedBrandService.createCardlimitedBrand(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新卡种可使用品牌")
    @PreAuthorize("@ss.hasPermission('crm:cardlimited-brand:update')")
    public CommonResult<Boolean> updateCardlimitedBrand(@Valid @RequestBody CardlimitedBrandSaveReqVO updateReqVO) {
        cardlimitedBrandService.updateCardlimitedBrand(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除卡种可使用品牌")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('crm:cardlimited-brand:delete')")
    public CommonResult<Boolean> deleteCardlimitedBrand(@RequestParam("id") Long id) {
        cardlimitedBrandService.deleteCardlimitedBrand(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得卡种可使用品牌")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('crm:cardlimited-brand:query')")
    public CommonResult<CardlimitedBrandRespVO> getCardlimitedBrand(@RequestParam("id") Long id) {
        CardlimitedBrandDO cardlimitedBrand = cardlimitedBrandService.getCardlimitedBrand(id);
        return success(BeanUtils.toBean(cardlimitedBrand, CardlimitedBrandRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得卡种可使用品牌分页")
    @PreAuthorize("@ss.hasPermission('crm:cardlimited-brand:query')")
    public CommonResult<PageResult<CardlimitedBrandRespVO>> getCardlimitedBrandPage(@Valid CardlimitedBrandPageReqVO pageReqVO) {
        PageResult<CardlimitedBrandDO> pageResult = cardlimitedBrandService.getCardlimitedBrandPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CardlimitedBrandRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出卡种可使用品牌 Excel")
    @PreAuthorize("@ss.hasPermission('crm:cardlimited-brand:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCardlimitedBrandExcel(@Valid CardlimitedBrandPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CardlimitedBrandDO> list = cardlimitedBrandService.getCardlimitedBrandPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "卡种可使用品牌.xls", "数据", CardlimitedBrandRespVO.class,
                        BeanUtils.toBean(list, CardlimitedBrandRespVO.class));
    }

}