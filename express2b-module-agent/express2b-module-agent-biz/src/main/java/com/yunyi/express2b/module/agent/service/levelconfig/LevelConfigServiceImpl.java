package com.yunyi.express2b.module.agent.service.levelconfig;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.yunyi.express2b.module.agent.controller.admin.levelconfig.vo.*;
import com.yunyi.express2b.module.agent.dal.dataobject.levelconfig.LevelConfigDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;

import com.yunyi.express2b.module.agent.dal.mysql.levelconfig.LevelConfigMapper;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.*;

/**
 * 代理商等级配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LevelConfigServiceImpl implements LevelConfigService {

    @Resource
    private LevelConfigMapper levelConfigMapper;

    @Override
    public Long createLevelConfig(LevelConfigSaveReqVO createReqVO) {
        // 插入
        LevelConfigDO levelConfig = BeanUtils.toBean(createReqVO, LevelConfigDO.class);
        levelConfigMapper.insert(levelConfig);
        // 返回
        return levelConfig.getId();
    }

    @Override
    public void updateLevelConfig(LevelConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateLevelConfigExists(updateReqVO.getId());
        // 更新
        LevelConfigDO updateObj = BeanUtils.toBean(updateReqVO, LevelConfigDO.class);
        levelConfigMapper.updateById(updateObj);
    }

    @Override
    public void deleteLevelConfig(Long id) {
        // 校验存在
        validateLevelConfigExists(id);
        // 删除
        levelConfigMapper.deleteById(id);
    }

    private void validateLevelConfigExists(Long id) {
        if (levelConfigMapper.selectById(id) == null) {
            throw exception(LEVEL_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public LevelConfigDO getLevelConfig(Long id) {
        return levelConfigMapper.selectById(id);
    }

    @Override
    public PageResult<LevelConfigDO> getLevelConfigPage(LevelConfigPageReqVO pageReqVO) {
        return levelConfigMapper.selectPage(pageReqVO);
    }

}