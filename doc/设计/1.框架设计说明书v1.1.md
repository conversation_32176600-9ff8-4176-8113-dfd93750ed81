# 1.框架设计说明书v1.1

# 框架设计文档v1.1

## 1\. 技术选型

Spring Boot: 使用现有的Spring Boot 3.4.1版本，利用其自动配置和起步依赖特性。 MyBatis Plus: 使用MyBatis Plus 3.5.9，结合dynamic-datasource实现多数据源支持。 Spring Security: 利用现有的spring-boot-starter-security实现权限控制。 Redis: 使用Redisson 3.41.0作为Redis客户端，实现缓存和会话管理。 Swagger: 使用knife4j-openapi3-jakarta-spring-boot-starter 4.6.0生成API文档。 MapStruct: 使用mapstruct 1.6.3实现DO、DTO、VO对象之间的转换。 Lombok: 使用lombok 1.18.36减少样板代码。

## 2\. 模块划分设计

根据投流商业模式V3.4的需求，我们将核心业务划分为以下几个模块：

### 1) express2b-module-agent (代理商模块)

- **功能**: 代理商管理模块，负责代理商注册、认证、等级升级、定价策略、账户管理等核心功能。
- **子模块**:
   - `express2b-module-agent-api`:
      - 包结构:
         - `com.yunyi.express2b.module.agent.api.dto`: 代理商相关数据传输对象 (DTOs)。其内部可根据业务功能进一步划分子包 (例如 `dto.profile`, `dto.account`)，以保持结构清晰，其组织方式应参考对应业务功能在 `controller.vo` 下的结构。
         - `com.yunyi.express2b.module.agent.api.enums`: 代理商模块对外暴露的枚举，可包含模块特定的 `AgentErrorCodeConstants.java`。
         - `com.yunyi.express2b.module.agent.api`: **代理商服务接口定义** (例如 `AgentApi.java`)。
   - `express2b-module-agent-biz`:
      - 包结构:
         - `com.yunyi.express2b.module.agent.biz.api`: **对 **`agent-api`** 模块中接口的具体实现** (例如 `AgentApiImpl.java`)。
         - `com.yunyi.express2b.module.agent.biz.controller`: 代理商相关HTTP接口实现 (REST Controllers)。
            - `vo`: 代理商视图对象 (VOs)，其内部可根据业务功能进一步划分子包。
         - `com.yunyi.express2b.module.agent.biz.service`: 代理商模块内部业务逻辑实现。
         - `com.yunyi.express2b.module.agent.biz.convert`: 对象转换层 (MapStruct Converters)。
         - `com.yunyi.express2b.module.agent.biz.dal`: 数据访问层。
            - `dataobject`: 代理商数据对象 (DOs)。
            - `mapper`: MyBatis Mapper接口。
### 2) express2b-module-order (订单模块--先使用已有的express模块后期再拆分)

- **功能**: 订单管理模块，处理订单创建、状态流转、物流跟踪、异常处理（如坏账）、订单查询等核心功能。
- **子模块**:
   - `express2b-module-order-api`:
      - 包结构:
         - `com.yunyi.express2b.module.order.api.dto`: 订单相关数据传输对象 (DTOs)。其内部可根据业务功能进一步划分子包，其组织方式应参考对应业务功能在 `controller.vo` 下的结构。
         - `com.yunyi.express2b.module.order.api.enums`: 订单模块对外暴露的枚举，可包含模块特定的 `OrderErrorCodeConstants.java`。
         - `com.yunyi.express2b.module.order.api`: **订单服务接口定义** (例如 `OrderApi.java`)。
   - `express2b-module-order-biz`:
      - 包结构:
         - `com.yunyi.express2b.module.order.biz.api`: **对 **`order-api`** 模块中接口的具体实现** (例如 `OrderApiImpl.java`)。
         - `com.yunyi.express2b.module.order.biz.controller`: 订单相关HTTP接口实现。
            - `vo`: 订单视图对象 (VOs)，其内部可根据业务功能进一步划分子包。
         - `com.yunyi.express2b.module.order.biz.service`: 订单模块内部业务逻辑实现。
         - `com.yunyi.express2b.module.order.biz.convert`: 对象转换层。
         - `com.yunyi.express2b.module.order.biz.dal`: 数据访问层。
            - `dataobject`: 订单数据对象 (DOs)。
            - `mapper`: MyBatis Mapper接口。
         - `com.yunyi.express2b.module.order.biz.handler`: 订单相关的事件处理器或特定业务处理器。
### 3) express2b-module-commission (分润模块)

- **功能**: 分润计算模块，根据预设规则和订单数据，负责各级代理商分润的计算、记录和分配。
- **子模块**:
   - `express2b-module-commission-api`:
      - 包结构:
         - `com.yunyi.express2b.module.commission.api.dto`: 分润相关数据传输对象 (DTOs)。其内部可根据业务功能进一步划分子包，其组织方式应参考对应业务功能在 `controller.vo` 下的结构。
         - `com.yunyi.express2b.module.commission.api.enums`: 分润模块对外暴露的枚举，可包含模块特定的 `CommissionErrorCodeConstants.java`。
         - `com.yunyi.express2b.module.commission.api`: **分润服务接口定义** (例如 `CommissionApi.java`)。
   - `express2b-module-commission-biz`:
      - 包结构:
         - `com.yunyi.express2b.module.commission.biz.api`: **对 **`commission-api`** 模块中接口的具体实现** (例如 `CommissionApiImpl.java`)。
         - `com.yunyi.express2b.module.commission.biz.controller`: (可选) 如果有后台管理分润规则或查看分润报表的HTTP接口。
            - `vo`: (可选) 相关的视图对象，其内部可根据业务功能进一步划分子包。
         - `com.yunyi.express2b.module.commission.biz.service`: 分润计算模块内部业务逻辑。
         - `com.yunyi.express2b.module.commission.biz.strategy`: 分润计算策略模式实现 (应对不同分润规则)。
         - `com.yunyi.express2b.module.commission.biz.convert`: 对象转换层。
         - `com.yunyi.express2b.module.commission.biz.dal`: (可选) 如果分润规则或结果需要持久化。
            - `dataobject`: 分润相关数据对象 (DOs)。
            - `mapper`: MyBatis Mapper接口。
         - `com.yunyi.express2b.module.commission.biz.event`: 分润相关的事件监听和处理 (例如，订单完成事件触发分润计算)。
### 4) express2b-module-wallet (钱包模块)

- **功能**: 钱包管理模块，负责代理商钱包的创建、余额管理（充值、扣款、冻结、解冻）、交易流水记录、提现申请与处理、结算管理等。
- **子模块**:
   - `express2b-module-wallet-api`:
      - 包结构:
         - `com.yunyi.express2b.module.wallet.api.dto`: 钱包操作相关数据传输对象 (DTOs)。其内部可根据业务功能进一步划分子包，其组织方式应参考对应业务功能在 `controller.vo` 下的结构。
         - `com.yunyi.express2b.module.wallet.api.enums`: 钱包模块对外暴露的枚举，可包含模块特定的 `WalletErrorCodeConstants.java`。
         - `com.yunyi.express2b.module.wallet.api`: **钱包服务接口定义** (例如 `WalletApi.java`)。
   - `express2b-module-wallet-biz`:
      - 包结构:
         - `com.yunyi.express2b.module.wallet.biz.api`: **对 **`wallet-api`** 模块中接口的具体实现** (例如 `WalletApiImpl.java`)。
         - `com.yunyi.express2b.module.wallet.biz.controller`: 钱包相关HTTP接口实现 (例如，代理商查询余额、发起提现等)。
            - `vo`: 钱包视图对象 (VOs)，其内部可根据业务功能进一步划分子包。
         - `com.yunyi.express2b.module.wallet.biz.service`: 钱包模块内部业务逻辑。
         - `com.yunyi.express2b.module.wallet.biz.convert`: 对象转换层。
         - `com.yunyi.express2b.module.wallet.biz.dal`: 数据访问层。
            - `dataobject`: 钱包、交易流水等数据对象 (DOs)。
            - `mapper`: MyBatis Mapper接口。
         - `com.yunyi.express2b.module.wallet.biz.event`: 钱包相关的事件监听和处理 (例如，支付成功事件更新钱包余额)。
### 5) express2b-module-pricing (定价模块)

- **功能**: 定价管理模块，负责平台各渠道（如不同快递公司）、各区域、各客户类型的零售价模板、成本价模板、以及代理商的个性化定价策略的管理和应用。
- **子模块**:
   - `express2b-module-pricing-api`:
      - 包结构:
         - `com.yunyi.express2b.module.pricing.api.dto`: 定价相关数据传输对象 (DTOs)。其内部可根据业务功能进一步划分子包，其组织方式应参考对应业务功能在 `controller.vo` 下的结构。
         - `com.yunyi.express2b.module.pricing.api.enums`: 定价模块对外暴露的枚举，可包含模块特定的 `PricingErrorCodeConstants.java`。
         - `com.yunyi.express2b.module.pricing.api`: **定价服务接口定义** (例如 `PricingApi.java`)。
   - `express2b-module-pricing-biz`:
      - 包结构:
         - `com.yunyi.express2b.module.pricing.biz.api`: **对 **`pricing-api`** 模块中接口的具体实现** (例如 `PricingApiImpl.java`)。
         - `com.yunyi.express2b.module.pricing.biz.controller`: (主要为后台管理) 定价模板、策略管理HTTP接口。
            - `vo`: 定价相关的视图对象，其内部可根据业务功能进一步划分子包。
         - `com.yunyi.express2b.module.pricing.biz.service`: 定价模块内部业务逻辑，如定价策略的实现与价格计算。
         - `com.yunyi.express2b.module.pricing.biz.convert`: 对象转换层。
         - `com.yunyi.express2b.module.pricing.biz.dal`: 数据访问层。
            - `dataobject`: 定价模板、价格规则等数据对象 (DOs)。
            - `mapper`: MyBatis Mapper接口。
         - `com.yunyi.express2b.module.pricing.biz.config`: (可选) 与定价相关的特定配置加载或管理。
## 3\. 通用规范

### 3.1 错误码管理

- **统一错误码定义**: 为了确保整个项目错误码的统一性和规范性，对错误码进行集中管理。
   - **项目级/共享错误码**: 建议定义在 `express2b-framework` 下的某个公共 `api` 组件中（例如，`express2b-common` 或 `express2b-spring-boot-starter-web` 提供的 `api` 部分），路径示例：`com.yunyi.express2b.framework.common.exception.GlobalErrorCodeConstants.java`。此类错误码为所有模块通用。
   - **模块特有错误码**: 定义在各业务模块的 `-api` 子模块下的 `enums` 包中，例如 `com.yunyi.express2b.module.agent.api.enums.AgentErrorCodeConstants.java`。这些错误码特定于该模块的业务。
- **目的**: 确保错误码的唯一性、规范性和易维护性，便于全局的错误处理机制和问题排查。
- **命名规范**: 错误码常量名建议采用 `模块标识_业务场景_错误描述` 的全大写下划线风格（例如 `AGENT_REGISTRATION_CREDENTIALS_INVALID`），并关联清晰、面向用户友好的错误提示信息。

### 3.2 接口设计规范

#### Controller层设计

- **接口分类**:
   - **前端接口**: 面向终端客户的接口，用于小程序等前端应用访问。
      - **URL前缀**: `app-api` (由框架统一控制，Controller中无需声明)
      - **包路径**: `com.yunyi.express2b.module.<module-name>.controller.app`
      - **版本控制**: 在app目录下使用子目录表示版本，如`v1`、`v2`等，实现接口版本管理
      - **功能组织**: 按业务领域划分子包，保持与后端接口相似的结构
   - **后端接口**: 面向管理Web系统的接口。
      - **URL前缀**: `admin-api` (由框架统一控制，Controller中无需声明)
      - **包路径**: `com.yunyi.express2b.module.<module-name>.controller.admin`
      - **功能组织**: 按业务领域划分子包，保持与前端接口相似的结构
- **接口设计原则**:
   - **RESTful风格**: 严格按照RESTful API设计规范实现
   - **URL字符要求**: URL地址不允许出现大写字符，只能使用小写字符、横线和数字
   - **统一响应格式**: 所有接口返回统一的 `CommonResult<T>` 响应结构
   - **版本兼容性**: 不同版本接口保持向后兼容性
   - **安全控制**: 根据接口类型实施不同的安全认证机制
   - **幂等性设计**: 关键操作实现接口幂等性
- **接口文档**:
   - 使用Swagger自动生成API文档
   - 对每个接口添加详细的注释说明
   - 包含请求参数说明、响应示例、错误码说明等
### 3.3 统一返回对象

系统使用 `CommonResult<T>` 类作为所有接口的统一返回结构，该类位于 `com.yunyi.express2b.framework.common.pojo` 包中。

#### CommonResult 主要属性

- `code`: Integer类型，表示响应状态码。成功时为预定义的成功代码，失败时为具体的错误代码。
- `data`: T类型，泛型数据字段，仅在请求成功时存在。
- `msg`: String类型，描述错误信息，成功时不包含此字段。

#### 使用规范

- **成功响应**: 使用 `CommonResult.success(T data)` 方法返回成功结果
- **错误响应**: 使用多种重载的 `CommonResult.error(...)` 方法返回错误结果
- **异常处理**: Controller层应统一捕获异常并通过CommonResult返回标准化错误


