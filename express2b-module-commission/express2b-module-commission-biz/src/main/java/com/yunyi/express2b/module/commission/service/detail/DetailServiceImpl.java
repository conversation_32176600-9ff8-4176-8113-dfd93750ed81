package com.yunyi.express2b.module.commission.service.detail;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.yunyi.express2b.module.commission.controller.admin.detail.vo.*;
import com.yunyi.express2b.module.commission.dal.dataobject.detail.DetailDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;

import com.yunyi.express2b.module.commission.dal.mysql.detail.DetailMapper;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.commission.enums.ErrorCodeConstants.*;

/**
 * 分润明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DetailServiceImpl implements DetailService {

    @Resource
    private DetailMapper detailMapper;

    @Override
    public Long createDetail(DetailSaveReqVO createReqVO) {
        // 插入
        DetailDO detail = BeanUtils.toBean(createReqVO, DetailDO.class);
        detailMapper.insert(detail);
        // 返回
        return detail.getId();
    }

    @Override
    public void updateDetail(DetailSaveReqVO updateReqVO) {
        // 校验存在
        validateDetailExists(updateReqVO.getId());
        // 更新
        DetailDO updateObj = BeanUtils.toBean(updateReqVO, DetailDO.class);
        detailMapper.updateById(updateObj);
    }

    @Override
    public void deleteDetail(Long id) {
        // 校验存在
        validateDetailExists(id);
        // 删除
        detailMapper.deleteById(id);
    }

    private void validateDetailExists(Long id) {
        if (detailMapper.selectById(id) == null) {
            throw exception(DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public DetailDO getDetail(Long id) {
        return detailMapper.selectById(id);
    }

    @Override
    public PageResult<DetailDO> getDetailPage(DetailPageReqVO pageReqVO) {
        return detailMapper.selectPage(pageReqVO);
    }

    /**
     * 我可以查询分润明细，按时间、来源、下级等维度统计
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<DetailDO> getcommissiondetails(DetailPageReqVO pageReqVO) {

        return  detailMapper.selectPage(pageReqVO);
    }

}