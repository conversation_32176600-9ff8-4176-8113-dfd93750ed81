package com.yunyi.express2b.module.express.service.invoice.invoiceorderrelation;

import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorderrelation.InvoiceOrderRelationPageDTO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorderrelation.InvoiceOrderRelationDTO;
import com.yunyi.express2b.module.express.dal.dataobject.invoice.InvoiceOrderRelationDO;
import jakarta.validation.*;

import com.yunyi.express2b.framework.common.pojo.PageResult;

/**
 * 发票订单关联 Service 接口
 *
 * <AUTHOR>
 */
public interface InvoiceOrderRelationService {

    /**
     * 创建发票订单关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInvoiceOrderRelation(@Valid InvoiceOrderRelationDTO createReqVO);

    /**
     * 更新发票订单关联
     *
     * @param updateReqVO 更新信息
     */
    void updateInvoiceOrderRelation(@Valid InvoiceOrderRelationDTO updateReqVO);

    /**
     * 删除发票订单关联
     *
     * @param id 编号
     */
    void deleteInvoiceOrderRelation(Long id);

    /**
     * 获得发票订单关联
     *
     * @param id 编号
     * @return 发票订单关联
     */
    InvoiceOrderRelationDO getInvoiceOrderRelation(Long id);

    /**
     * 获得发票订单关联分页
     *
     * @param pageReqVO 分页查询
     * @return 发票订单关联分页
     */
    PageResult<InvoiceOrderRelationDO> getInvoiceOrderRelationPage(InvoiceOrderRelationPageDTO pageReqVO);

}