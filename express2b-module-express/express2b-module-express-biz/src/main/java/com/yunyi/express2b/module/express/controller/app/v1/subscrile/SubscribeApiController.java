package com.yunyi.express2b.module.express.controller.app.v1.subscrile;

import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.module.express.controller.admin.subscribe.vo.SubscribeSaveReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.subscribe.SubscribeDO;
import com.yunyi.express2b.module.express.service.subscribe.SubscribeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 用户订阅信息表")
@RestController
@RequestMapping("/v1/express/subscribe")
@Validated
public class SubscribeApiController {

    @Resource
    private SubscribeService subscribeService;

    @PostMapping("/create")
    @Operation(summary = "创建用户订阅信息表")
    public CommonResult<List<SubscribeDO>> createSubscribe(@Valid @RequestBody List<SubscribeSaveReqVO> createReqVOList) {
        return success(subscribeService.createSubscribelist(createReqVOList));
    }


}