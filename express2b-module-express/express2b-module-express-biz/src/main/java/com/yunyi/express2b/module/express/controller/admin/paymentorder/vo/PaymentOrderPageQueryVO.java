package com.yunyi.express2b.module.express.controller.admin.paymentorder.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 支付订单查询对象
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/28 14:46
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PaymentOrderPageQueryVO extends PageParam {
    @Schema(description = "订单表ID", example = "29884")
    private Long orderId;

    @Schema(description = "快递运单号")
    private String trackingNumber;

    @Schema(description = "支付状态（支付成功，转入退款，未支付，已关闭，已撤销，用户支付中，支付失败）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String status;

    @Schema(description = "支付方式（支付宝，微信）")
    private String paymentMethod;

    @Schema(description = "支付提交日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] paymentSubmitTime;

    @Schema(description = "最后退款日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] lastRefundTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}
