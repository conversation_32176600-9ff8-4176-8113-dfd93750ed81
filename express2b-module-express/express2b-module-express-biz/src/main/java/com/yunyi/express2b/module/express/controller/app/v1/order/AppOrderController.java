package com.yunyi.express2b.module.express.controller.app.v1.order;

import com.alibaba.fastjson.JSONObject;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.express.controller.admin.order.vo.*;
import com.yunyi.express2b.module.express.controller.admin.paymentorder.vo.UnifiedOrderResponseNew;
import com.yunyi.express2b.module.express.controller.app.v1.order.vo.*;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.service.order.OrderService;
import com.yunyi.framework.api.login.api.cs.vo.CustomerServicesRequest;
import com.yunyi.framework.api.login.api.cs.vo.CustomerServicesResponse;
import com.yunyi.framework.api.logistics.api.express.vo.AddressListRequest;
import com.yunyi.framework.api.logistics.api.express.vo.ExpressTrackingData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

/**
 * 对App：订单管理 controller层
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 订单管理")
@RestController
@RequestMapping("/v1/express/order")
@Validated
@Slf4j
public class AppOrderController {

    @Resource
    private OrderService orderService;

    @PostMapping("/download-poster")
    @Operation(summary = "下载海报")
    @PermitAll
    public CommonResult<List<PosterTemplateVO>> downloadPoster() {
        return success(orderService.downloadPoster());
    }

    @PostMapping("/generate-applets")
    @Operation(summary = "生成小程序")
    @PermitAll
    public CommonResult<JSONObject> generateApplets() {
        return success(orderService.generateApplets());
    }
    @PostMapping("/open-platform-agent")
    @Operation(summary = "开通平台代理")
    @PermitAll
    public CommonResult<JSONObject> openPlatformAgent() {
        return success(orderService.openPlatformAgent());
    }
    @PostMapping("/create-qr-code")
    @Operation(summary = "创建二维码")
    @PermitAll
    public CommonResult<JSONObject> createQrCode() {
        return success(orderService.createQrCode());
    }

    @PostMapping("/official-account-link")
    @Operation(summary = "获取微信关注公众号链接")
    @PermitAll
    public CommonResult<OfficialAccountResponseVO> officialAccountLink() {
        return success(orderService.officialAccountLink());
    }

    @PostMapping("/online-customer")
    @Operation(summary = "在线客服")
    @PermitAll
    public CommonResult<CustomerServicesResponse> onlineCustomer(@Valid @RequestBody CustomerServicesRequest request) {
        return success(orderService.getOnlineCustomerData(request));
    }
    @PostMapping("/update-personal")
    @Operation(summary = "修改用户信息")
    @PermitAll
    public CommonResult<JSONObject> updatePersonal(@RequestBody UpdateUserRequest updateUserRequest) {
        log.info("开始执行修改用户信息方法,接收参数：{}", updateUserRequest);
        return success(orderService.updatePersonal(updateUserRequest));
    }

    /**
     * 统一支付 再次付款 还有订单回调的时候 加续重的时候付款
     * @param agreePayVo
     * @return
     */
    @PostMapping("/pay")
    @Operation(summary = "统一支付")
    public CommonResult<UnifiedOrderResponseNew> paycreateOrder(@Valid @RequestBody AgreePayVo agreePayVo) {
        List<String> orderNos = agreePayVo.getOrderNos();
        return success(orderService.paycreateOrder(agreePayVo, orderNos));
    }

    /**
     * 统一退款 支持部分退款
     * @param orderCancelVO
     * @return
     */
    @PostMapping("/refund")
    @Operation(summary = "统一退款")
    public CommonResult<OrderCancelVO> refundcancel(@Valid @RequestBody OrderCancelVO orderCancelVO) {
        return success(orderService.refundcancelOrder(orderCancelVO));
    }


    @PostMapping("/create")
    @Operation(summary = "创建订单")
    public CommonResult<UnifiedOrderResponseNew> createOrder(@Valid @RequestBody RequestVO requestVO) {
        OrderSaveReqVO createReqVO = requestVO.getCreateReqVO();
        List<RecipientVO> recipientVOs = requestVO.getRecipientVOs();
        return success(orderService.createOrder(createReqVO, recipientVOs));
    }

    @PostMapping("/create-orders")
    @Operation(summary = "创建订单")
    public CommonResult<UnifiedOrderResponseNew> createOrders(@Valid @RequestBody List<OrderWithRecipientVO> requestVO) {
        return success(orderService.createOrders(requestVO));
    }
    /**
     * 在创建订单的时候订阅列表
     * @param
     * @return
     */
    @PostMapping("/creat-sendtemplete")
    @Operation(summary = "在创建订单的时候订阅列表")
    public CommonResult sendtemplete() {
        return success(orderService.sendtemplete());
    }


    @PutMapping("/update")
    @Operation(summary = "更新订单")
    public CommonResult<Boolean> updateOrder(@Valid @RequestBody OrderSaveReqVO updateReqVO) {
        orderService.updateOrder(updateReqVO);
        return success(true);
    }

    @PostMapping("/cancel-order")
    @Operation(summary = "取消订单")
    public CommonResult<OrderCancelVO> cancel(@Valid @RequestBody OrderCancelVO orderCancelVO) {
        return success(orderService.cancelOrder(orderCancelVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<OrderResponseVO> getOrder(@RequestParam("id") Long id) {
        return success(orderService.getOrder(id));
    }

    @GetMapping("/orders")
    @Operation(summary = "根据当前用户姓名手机号查询订单")
    public CommonResult<PageResult<OrderRespVO>> getOrderPageOrders(OrderResUserVO orderResUserVO) {
        PageResult<OrderDO> pageResult = orderService.getOrderPageOrders(orderResUserVO);
        return success(BeanUtils.toBean(pageResult, OrderRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得订单管理分页")
    public CommonResult<PageResult<OrderRespVO>> getOrderPage(@Valid OrderPageReqVO pageReqVO) {
        PageResult<OrderDO> pageResult = orderService.getOrderPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OrderRespVO.class));
    }

    @GetMapping("/status-pages")
    @Operation(summary = "根据订单状态获得订单管理分页")
    public CommonResult<PageResult<OrderRespVO>> getOrderPages(@Valid @RequestParam("status") String status) {
        PageResult<OrderDO> pageResult = orderService.getOrderPages(status);
        return success(BeanUtils.toBean(pageResult, OrderRespVO.class));
    }


    @PostMapping("/content-parse-excel")
    @Operation(summary = "Excel上传并同步SSO服务器")
    public CommonResult<AddressResponse> uploadParse(@RequestParam("file") MultipartFile file) throws Exception {
        AddressResponse addressResponse = orderService.processExcelAndSyncToSSO(file);
        return success(addressResponse);
    }

    @PostMapping("/content-parse")
    @Operation(summary = "手动添加地址进行识别")
    public CommonResult<AddressResponse> manualParseAddress(@Valid @RequestBody AddressListRequest content) {
        return success(orderService.manualParseAddress(content));
    }

    @GetMapping("/tracking")
    @Operation(summary = "物流跟踪信息")
    public CommonResult<List<ExpressTrackingData>> getOrderLogistics(@RequestParam("trackingNumber") String trackingNumber,
                                                                     @RequestParam("id") Long id) {
        return success(orderService.getOrderLogistics(trackingNumber, id));
    }

    @GetMapping("/order-status-pay")
    @Operation(summary = "根据已支付/待支付状态以及时间段查询订单金额")
    public CommonResult<PaymentStatusAmountReqVO> getStatusPay(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {
        return success(orderService.getStatusPay(startTime, endTime));
    }


    @GetMapping("/orders-list")
    @Operation(summary = "根据已支付/待支付状态以及时间段查询订单列表")
    public CommonResult<OrderListRespVO> getOrderList(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {
        return success(orderService.getOrderList(startTime, endTime));
    }

    @GetMapping("/status-pay")
    @Operation(summary = "查询未支付订单列表")
    public CommonResult<PageResult<OrderRespVO>> unpaidOrders(@Valid OrderPageReqVO pageReqVO) {
        PageResult<OrderDO> pageResult = orderService.unpaidOrders(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OrderRespVO.class));
    }

    @GetMapping("/fee-detail")
    @Operation(summary = "查询订单费用详情")
    public CommonResult<List<FeeDetailReqVO>> getFeeDetail(@Valid OrderSaveReqVO orderSaveReqVO) {
        List<FeeDetailReqVO> feeDetail = orderService.getFeeDetail(orderSaveReqVO);
        return success(feeDetail);
    }

    @GetMapping("/fastTrack-logistics")
    @Operation(summary = "查询快递公司列表")
    public CommonResult<ExpressListResponseVO> getFastTrackLogistics(@Valid ExpressListRequestVO request) {
        ExpressListResponseVO fastTrackLogistics = orderService.getFastTrackLogistics(request);
        return success(fastTrackLogistics);
    }

    @GetMapping("/orders-count")
    @Operation(summary = "服务统计数据（发单量、已到达、待付款、进行中、已取消）订单数量")
    public CommonResult<OrdersCountQeqVO> ordersCount() {
        return success(orderService.getOrdersCount());
    }

    @GetMapping("/today-count")
    @Operation(summary = "今日累计订单量")
    public CommonResult<Long> todayCountOrdersCount() {
        return success(orderService.todayCountOrdersCount());
    }

    @GetMapping("/unpaid-order-count")
    @Operation(summary = "查询未付款的订单数量")
    public CommonResult<Long> unpaidOrderCount() {
        return success(orderService.counts());
    }

}