package com.yunyi.express2b.module.express.service.message;

import com.yunyi.express2b.module.crm.api.config.CrmConfigApi;
import com.yunyi.express2b.module.express.api.message.dto.OrderChangeDTO;
import com.yunyi.express2b.module.express.api.message.dto.PaymentOrderDTO;
import com.yunyi.express2b.module.express.dal.dataobject.subscribe.SubscribeDO;
import com.yunyi.express2b.module.express.dal.mysql.subscribe.SubscribeMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.HashMap;
import java.util.Map;

/**
 * 消息通知service实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/26 下午2:25
 */
@Service
@Validated
@RequiredArgsConstructor
public class SmsServiceImpl implements SmsService {
    private final SubscribeMapper subscribeMapper;

    private final CrmConfigApi crmConfigApi;

    @Override
    public Integer sendOrderChangeMessage(OrderChangeDTO orderChangeDTO) {
        //查询用户是否订阅订单变更通知
        Integer subscribe = getSubscribe(orderChangeDTO.getMemberId(),orderChangeDTO.getTempType() );
        return subscribe;

    }



    /**
     * 获取用户是否订阅指定模板
     */
    public Integer getSubscribe(Long memberId, Integer tempType) {
        // 查询用户用户订阅记录
        SubscribeDO subscribeDO = subscribeMapper.selectSubscribe(memberId, tempType);
        //返回订阅状态
        if (subscribeDO != null) {
            return subscribeDO.getSubscribe();
        } else {
            return 0;
        }
    }







    //TODO:待开发-新支付单发送通知
    @Override
    public void sendPaymentOrderMessage(PaymentOrderDTO paymentOrderDTO) {
        //查询用户是否订阅通知
        //调用crm模块api查询用户是否关注
        //封装消息通知内容
        //调用接口发送消息通知
    }
}
