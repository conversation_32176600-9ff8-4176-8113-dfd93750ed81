package com.yunyi.express2b.module.express.enums;

import lombok.Getter;

/**
 * 订单支付联系类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/17 下午7:04
 */
@Getter
public enum RelOrderPaymentEnum {
    ORDER(1, "订单"),
    CARD(2, "次卡");

    private final int code;
    private final String desc;

    RelOrderPaymentEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
