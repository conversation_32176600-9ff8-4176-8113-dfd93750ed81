package com.yunyi.express2b.module.crm.api.address.vo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * AddressSaveReqApiVO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/11 下午2:31
 */

@Data
public class AddressSaveReqApiVO {

    private Long id;

    @NotNull(message = "用户ID不能为空")
    private Long memberId;

    @NotEmpty(message = "姓名不能为空")
    private String name;

    @NotEmpty(message = "电话不能为空")
    private String phone;

    @NotEmpty(message = "详细地址不能为空")
    private String address;

    @NotEmpty(message = "地址类型（RECIPIENT: 收件地址, SHIPPER: 寄件地址，NULL：可以出现在任意标签中）不能为空")
    private String addressType;

    private Integer isDefault;

    @NotNull(message = "省不能为空")
    private Long provinceCode;

    private String adccode;

    @NotNull(message = "市不能为空")
    private Long cityCode;

    private Long districtCode;

}