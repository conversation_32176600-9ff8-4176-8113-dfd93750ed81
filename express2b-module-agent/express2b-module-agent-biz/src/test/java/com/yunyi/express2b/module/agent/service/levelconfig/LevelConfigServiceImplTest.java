package com.yunyi.express2b.module.agent.service.levelconfig;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.module.agent.controller.admin.levelconfig.vo.LevelConfigPageReqVO;
import com.yunyi.express2b.module.agent.controller.admin.levelconfig.vo.LevelConfigSaveReqVO;
import com.yunyi.express2b.module.agent.dal.dataobject.levelconfig.LevelConfigDO;
import com.yunyi.express2b.module.agent.dal.mysql.levelconfig.LevelConfigMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import static com.yunyi.express2b.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static com.yunyi.express2b.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertPojoEquals;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertServiceException;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomLongId;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomPojo;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.LEVEL_CONFIG_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link LevelConfigServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(LevelConfigServiceImpl.class)
public class LevelConfigServiceImplTest extends BaseDbUnitTest {

    @Resource
    private LevelConfigServiceImpl levelConfigService;

    @Resource
    private LevelConfigMapper levelConfigMapper;

    @Test
    void testInsert() {

        LevelConfigSaveReqVO levelConfigSaveReqVO = new LevelConfigSaveReqVO()
                .setLevelName("测试等级")
                .setDefaultPlatformPricingTemplateId(2L)
                .setUpgradePersonalDailyOrders(100)
                .setUpgradeTeamDailyOrders(500)
                .setUpgradeObservationDays(5);
        System.out.println(levelConfigService.createLevelConfig(levelConfigSaveReqVO));

    }


    @Test
    public void testCreateLevelConfig_success() {
        // 准备参数
        LevelConfigSaveReqVO createReqVO = randomPojo(LevelConfigSaveReqVO.class).setId(null);

        // 调用
        Long levelConfigId = levelConfigService.createLevelConfig(createReqVO);
        // 断言
        assertNotNull(levelConfigId);
        // 校验记录的属性是否正确
        LevelConfigDO levelConfig = levelConfigMapper.selectById(levelConfigId);
        assertPojoEquals(createReqVO, levelConfig, "id");
    }

    @Test
    public void testUpdateLevelConfig_success() {
        // mock 数据
        LevelConfigDO dbLevelConfig = randomPojo(LevelConfigDO.class);
        levelConfigMapper.insert(dbLevelConfig);// @Sql: 先插入出一条存在的数据
        // 准备参数
        LevelConfigSaveReqVO updateReqVO = randomPojo(LevelConfigSaveReqVO.class, o -> {
            o.setId(dbLevelConfig.getId()); // 设置更新的 ID
        });

        // 调用
        levelConfigService.updateLevelConfig(updateReqVO);
        // 校验是否更新正确
        LevelConfigDO levelConfig = levelConfigMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, levelConfig);
    }

    @Test
    public void testUpdateLevelConfig_notExists() {
        // 准备参数
        LevelConfigSaveReqVO updateReqVO = randomPojo(LevelConfigSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> levelConfigService.updateLevelConfig(updateReqVO), LEVEL_CONFIG_NOT_EXISTS);
    }

    @Test
    public void testDeleteLevelConfig_success() {
        // mock 数据
        LevelConfigDO dbLevelConfig = randomPojo(LevelConfigDO.class);
        levelConfigMapper.insert(dbLevelConfig);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbLevelConfig.getId();

        // 调用
        levelConfigService.deleteLevelConfig(id);
        // 校验数据不存在了
        assertNull(levelConfigMapper.selectById(id));
    }

    @Test
    public void testDeleteLevelConfig_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> levelConfigService.deleteLevelConfig(id), LEVEL_CONFIG_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetLevelConfigPage() {
        // mock 数据
        LevelConfigDO dbLevelConfig = randomPojo(LevelConfigDO.class, o -> { // 等会查询到
            o.setLevelName(null);
            o.setDefaultPlatformPricingTemplateId(null);
            o.setUpgradePersonalDailyOrders(null);
            o.setUpgradeTeamDailyOrders(null);
            o.setUpgradeObservationDays(null);
            o.setCreateTime(null);
        });
        levelConfigMapper.insert(dbLevelConfig);
        // 测试 levelName 不匹配
        levelConfigMapper.insert(cloneIgnoreId(dbLevelConfig, o -> o.setLevelName(null)));
        // 测试 defaultPlatformPricingTemplateId 不匹配
        levelConfigMapper.insert(cloneIgnoreId(dbLevelConfig, o -> o.setDefaultPlatformPricingTemplateId(null)));
        // 测试 upgradePersonalDailyOrders 不匹配
        levelConfigMapper.insert(cloneIgnoreId(dbLevelConfig, o -> o.setUpgradePersonalDailyOrders(null)));
        // 测试 upgradeTeamDailyOrders 不匹配
        levelConfigMapper.insert(cloneIgnoreId(dbLevelConfig, o -> o.setUpgradeTeamDailyOrders(null)));
        // 测试 upgradeObservationDays 不匹配
        levelConfigMapper.insert(cloneIgnoreId(dbLevelConfig, o -> o.setUpgradeObservationDays(null)));
        // 测试 createTime 不匹配
        levelConfigMapper.insert(cloneIgnoreId(dbLevelConfig, o -> o.setCreateTime(null)));
        // 准备参数
        LevelConfigPageReqVO reqVO = new LevelConfigPageReqVO();
        reqVO.setLevelName(null);
        reqVO.setDefaultPlatformPricingTemplateId(null);
        reqVO.setUpgradePersonalDailyOrders(null);
        reqVO.setUpgradeTeamDailyOrders(null);
        reqVO.setUpgradeObservationDays(null);
        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

        // 调用
        PageResult<LevelConfigDO> pageResult = levelConfigService.getLevelConfigPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbLevelConfig, pageResult.getList().get(0));
    }

}