package com.yunyi.express2b.module.system.controller.app.news;

import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.system.controller.admin.news.vo.NewsRespVO;
import com.yunyi.express2b.module.system.dal.dataobject.news.NewsDO;
import com.yunyi.express2b.module.system.service.news.NewsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

;

/**
 * app文章展示controller
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/28 上午9:33
 */
@Tag(name = "用户APP - 文章管理")
@RestController
@RequestMapping("/v1/system/news")
@Validated
public class AppNewsController {

    @Resource
    private NewsService newsService;

    @GetMapping("/type/{type}")
    @Operation(summary = "获取静态页面文章内容(暂有用户协议/计费规则...)")
    @Parameter(name = "type", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<NewsRespVO> getStaticNews(@PathVariable int type) {
        NewsDO news = newsService.getNewsByType(type);
        return success(BeanUtils.toBean(news, NewsRespVO.class));
    }

}
