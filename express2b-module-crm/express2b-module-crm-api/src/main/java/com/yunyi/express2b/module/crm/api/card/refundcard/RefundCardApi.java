package com.yunyi.express2b.module.crm.api.card.refundcard;

import com.yunyi.express2b.module.crm.api.card.paycard.vo.PayCardVo;

/**
 * RefundCardApi
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/12 下午5:58
 */

public interface RefundCardApi {
    /**
     * 退还卡片给用户

     * @param cardUserVo 包含卡片用户必要信息的对象
     * 该对象应包括退还卡片所需的所有数据，
     * @return 返回一个布尔值，表示卡片是否成功退还
     * 如果返回true，表示卡片成功退还；如果返回false，表示退还失败
     */
    Boolean refundCard(PayCardVo cardUserVo);
}
