package com.yunyi.express2b.module.express.controller.admin.brandscheduledconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalTime;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
/**
 * 管理后台 快递品牌预约配置 Response VO
 *
 * <AUTHOR>
 *
 */
@Schema(description = "管理后台 - 快递品牌预约配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class BrandScheduledConfigRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26409")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "快递品牌标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("快递品牌标识")
    private String brandKey;

    @Schema(description = "预约天数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预约天数")
    private Integer scheduledDays;

    @Schema(description = "预约开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预约开始时间")
    private LocalTime scheduledTimeStart;

    @Schema(description = "预约结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预约结束时间")
    private LocalTime scheduledTimeEnd;

    @Schema(description = "预约间隔分钟数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预约间隔分钟数")
    private Integer intervalMinutes;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}