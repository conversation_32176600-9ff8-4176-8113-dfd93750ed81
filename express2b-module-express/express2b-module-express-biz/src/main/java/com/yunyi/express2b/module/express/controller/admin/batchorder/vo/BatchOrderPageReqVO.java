package com.yunyi.express2b.module.express.controller.admin.batchorder.vo;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 批量订单管理分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BatchOrderPageReqVO extends PageParam {

    @Schema(description = "用户ID", example = "4882")
    private Long personalId;

    @Schema(description = "类型（手动，excel）", example = "1")
    private Integer type;

    @Schema(description = "批次编号")
    private String batchNumber;

    @Schema(description = "数据内容或文件存储路径")
    private String data;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "租户编号")
    private Integer tenantId;

    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "更新者")
    private String updater;

    @Schema(description = "是否删除")
    private Integer deleted;
}