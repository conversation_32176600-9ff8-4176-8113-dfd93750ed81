package com.yunyi.express2b.module.express.service.brand;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.express.controller.admin.brand.vo.BrandPageReqVO;
import com.yunyi.express2b.module.express.controller.admin.brand.vo.BrandSaveReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.brand.BrandDO;
import com.yunyi.express2b.module.express.dal.mysql.brand.BrandMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.BRAND_NOT_EXISTS;

/**
 * 快递品牌信息表 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BrandServiceImpl implements BrandService {

    @Resource
    private BrandMapper brandMapper;

    /**
     * 创建快递品牌信息表
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Long createBrand(BrandSaveReqVO createReqVO) {
        // 插入
        BrandDO brand = BeanUtils.toBean(createReqVO, BrandDO.class);
        brandMapper.insert(brand);
        // 返回
        return brand.getId();
    }

    /**
     * 更新快递品牌信息表
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateBrand(BrandSaveReqVO updateReqVO) {
        // 校验存在
        validateBrandExists(updateReqVO.getId());
        // 更新
        BrandDO updateObj = BeanUtils.toBean(updateReqVO, BrandDO.class);
        brandMapper.updateById(updateObj);
    }

    /**
     * 删除快递品牌信息表
     * @param id 编号
     */
    @Override
    public void deleteBrand(Long id) {
        // 校验存在
        validateBrandExists(id);
        // 删除
        brandMapper.deleteById(id);
    }

    /**
     * 校验快递品牌信息表是否存在
     * @param id
     */
    private void validateBrandExists(Long id) {
        if (brandMapper.selectById(id) == null) {
            throw exception(BRAND_NOT_EXISTS);
        }
    }

    /**
     * 获得快递品牌
     * @param id 编号
     * @return
     */
    @Override
    public BrandDO getBrand(Long id) {
        return brandMapper.selectById(id);
    }

    /**
     * 获得快递品牌信息表分页
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<BrandDO> getBrandPage(BrandPageReqVO pageReqVO) {
        return brandMapper.selectPage(pageReqVO);
    }

    /**
     * 获得
     * @return
     */
    @Override
    public List<BrandDO> getAllBrand() {
        return brandMapper.getAllBrand() ;

    }

}