-- 菜单 SQL
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status, component_name
)
VALUES (
    '代理商定价（终端零售价）规则管理', '', 2, 0, 3218,
    'pricing-rule', '', 'agent/pricingrule/index', 0, 'PricingRule'
);

-- 按钮父菜单ID
-- 暂时只支持 MySQL。如果你是 Oracle、PostgreSQL、SQLServer 的话，需要手动修改 @parentId 的部分的代码
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES (
    '代理商定价（终端零售价）规则查询', 'agent:pricing-rule:query', 3, 1, @parentId,
    '', '', '', 0
);
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES (
    '代理商定价（终端零售价）规则创建', 'agent:pricing-rule:create', 3, 2, @parentId,
    '', '', '', 0
);
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES (
    '代理商定价（终端零售价）规则更新', 'agent:pricing-rule:update', 3, 3, @parentId,
    '', '', '', 0
);
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES (
    '代理商定价（终端零售价）规则删除', 'agent:pricing-rule:delete', 3, 4, @parentId,
    '', '', '', 0
);
INSERT INTO system_menu(
    name, permission, type, sort, parent_id,
    path, icon, component, status
)
VALUES (
    '代理商定价（终端零售价）规则导出', 'agent:pricing-rule:export', 3, 5, @parentId,
    '', '', '', 0
);