package com.yunyi.express2b.module.agent.controller.admin.relationship.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 9:53
 */
@Schema(description = "管理后台 - 代理商团队关系分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RelationshipPageReqVO extends PageParam {

    @Schema(description = "上级代理商ID", example = "25381")
    private Long ancestorId;

    @Schema(description = "下级代理商ID", example = "8554")
    private Long descendantId;

    @Schema(description = "层级深度")
    private Integer depth;

}