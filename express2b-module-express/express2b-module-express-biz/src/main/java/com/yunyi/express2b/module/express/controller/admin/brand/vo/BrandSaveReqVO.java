package com.yunyi.express2b.module.express.controller.admin.brand.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
/**
 * 快递品牌信息表新增/修改 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 快递品牌信息表新增/修改 Request VO")
@Data
public class BrandSaveReqVO {

    @Schema(description = "品牌ID，唯一标识品牌", requiredMode = Schema.RequiredMode.REQUIRED, example = "1410")
    private Long id;

    @Schema(description = "品牌唯一标识字符串，不能为空", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "品牌唯一标识字符串，不能为空不能为空")
    private String brandKey;

    @Schema(description = "品牌展示的名词")
    private String brandNameDisplay;

    @Schema(description = "品牌名称", example = "王五")
    private String brandName;

    @Schema(description = "品牌描述", example = "你说的对")
    private String description;

    @Schema(description = "品牌图标地址")
    private String brandIcon;

}