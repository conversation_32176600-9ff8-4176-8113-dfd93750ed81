package com.yunyi.express2b.module.pricing.api.vo;


import jakarta.validation.constraints.NotNull;
import lombok.Data;


@Data
public class PricingRuleSaveReqVO {


    private Long id;


    @NotNull(message = "代理商ID不能为空")
    private Long agentId;


    @NotNull(message = "加价类型 (1-比例加价，2-固定金额加价-默认)不能为空")
    private Integer markupType;


    @NotNull(message = "首重加价值 (比例或固定金额，单位是分)不能为空")
    private Integer firstWeightMarkup;


    @NotNull(message = "续重加价值 (比例或固定金额，单位是分)不能为空")
    private Integer additionalWeightMarkup;


    private String brandKey;


    private String fromAdcode;


    private String toAdcode;


    @NotNull(message = "状态 (0-禁用，1-启用)不能为空")
    private Integer status;


    @NotNull(message = "优先级 (数字越大优先级越高)不能为空")
    private Integer priority;


    @NotNull(message = "租户编号不能为空")
    private Long tenantId;

}