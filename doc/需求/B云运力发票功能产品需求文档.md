# B云运力发票功能产品需求文档

## 1. 修订记录

| 版本  | 修订日期    | 修订人 | 修订内容         |
| :---- | :---------- | :----- | :--------------- |
| 1.0   | 2025-06-10 | 解蕾 | 创建初始文档     |
| 1.1   | 2025-06-10 | 解蕾 | 修正入口名称和邮件发送描述 |
| 1.2   | 2025-06-10 | 解蕾 | 更新开票信息修改流程 |
| 1.3   | 2025-06-10 | 解蕾 | 增加审核功能和开票失败处理流程 |
| 1.4   | 2025-06-10 | 解蕾 | 增加功能优先级标识 |

## 2. 项目概述

### 2.1. 背景

随着"小B云运力"业务量的增长，越来越多的企业用户提出了开具发票用于财务报销的需求。为了提升用户满意度、完善产品服务并满足企业财务合规性要求，现计划为产品增加在线申请发票的功能。

### 2.2. 目标

- **用户端**：为用户提供一个便捷的在线流程，允许他们选择已完成的订单，提交开票申请，并能随时跟踪开票状态、下载电子发票。
- **管理后台**：为运营人员提供一套高效的后台管理工具，支持批量审核、导出开票申请、批量更新开票结果，并通过系统自动向用户发送通知。
- **客服**：建立客服介入机制，处理用户修改开票信息等特殊请求，确保流程的灵活性和安全性。

### 2.3. 功能优先级说明

本文档中使用以下emoji标识功能的优先级：

| 优先级 | 标识 | 说明 |
| :---- | :-- | :--- |
| 极高 | 5️⃣ | 核心功能，必须优先实现，项目上线的必要条件 |
| 高 | 4️⃣ | 重要功能，应在首个版本中实现 |
| 普通 | 3️⃣ | 常规功能，计划在正式版本中实现 |
| 低 | 2️⃣ | 辅助功能，可在后续版本中实现 |
| 极低 | 1️⃣ | 锦上添花的功能，可视资源情况安排 |

## 3. 功能详述

### 3.1. 用户端功能

#### 3.1.1. 查看可开票订单 3️⃣

- **入口**：在小程序"我的"页面增加"发票管理"入口。
- **列表展示**：用户进入后，系统将展示所有**已完成**且**未被任何（状态为处理中或已开具）的发票申请所关联**的订单列表。
- **订单信息**：列表中应清晰展示每个订单的订单号、完成时间、寄件/收件方信息摘要、订单金额。
- **选择功能**：用户可以勾选一个或多个订单，用于合并开票。页面底部应实时显示已选订单数量及总金额。

#### 3.1.2. 开票信息管理 3️⃣

- **首次创建**：用户首次申请开票时，系统会引导其填写开票信息（抬头）。
- **信息字段**：
  - **发票抬头**：企业名称（必填）
  - **纳税人识别号**：统一社会信用代码（必填）
  - **接收邮箱**：用于接收电子发票（必填，默认可带入用户注册邮箱）
- **保存机制**：用户填写并确认后，该开票信息将作为其默认信息保存。
- **信息锁定**：一旦确认保存，开票信息将变为只读状态，用户不可自行修改。界面上需要有明确提示："开票信息一经确认无法修改，如需变更请联系客服。"

#### 3.1.3. 提交开票申请 3️⃣

- **确认信息**：用户选择完订单后，点击"下一步"，进入开票信息确认页面。页面会展示将要开票的总金额和已保存的发票抬头信息。
- **提交申请**：用户确认无误后，点击"提交申请"。
- **消息订阅**：提交成功后，系统会弹出微信小程序的消息订阅授权请求，提示用户"授权后您将收到发票开具进度的通知"。用户可以选择同意或拒绝。

#### 3.1.4. 查看开票历史与状态 3️⃣

- **入口**：在"发票管理"页面内，提供"开票历史"页签。
- **列表展示**：展示用户所有的开票申请记录。
- **状态跟踪**：每条记录都应有明确的状态，包括：
  - **处理中**：用户已提交申请，等待后台处理。
  - **已开具**：后台已完成开票并上传了电子发票。
  - **已驳回**：申请因某种原因被后台驳回（需注明原因）。
- **记录详情**：点击单条记录可查看详情，包括关联的订单列表、开票金额、发票抬头信息、申请时间、完成时间等。

#### 3.1.5. 下载电子发票 2️⃣

- **下载入口**：对于状态为"已开具"的开票记录，在详情页提供"下载电子发票"的按钮。
- **下载方式**：点击按钮后，系统提供电子发票的下载链接，用户可以下载PDF格式的电子发票。

### 3.2. 后台管理功能

#### 3.2.1. 开票申请审核列表 3️⃣

- **功能入口**：在管理后台提供"财务管理" -> "发票申请管理"功能。
- **列表视图**：以列表形式展示所有用户的开票申请，默认按申请时间倒序排列。
- **筛选与搜索**：支持按申请状态（待处理、已开具、已驳回）、申请时间范围、用户ID/手机号等条件进行筛选和搜索。
- **批量操作**：运营人员可以勾选多条"待处理"的申请记录。

#### 3.2.2. 开票申请审核功能 2️⃣

- **单条审核**：运营人员可以点击单条申请记录进行详细审核。
- **审核内容**：
  - **开票信息审核**：检查发票抬头、纳税人识别号等信息是否准确、合规。
  - **金额审核**：核对申请开票的订单金额总和是否与系统计算一致。
  - **订单关联审核**：确认所有关联订单是否符合开票条件。
- **审核结果**：
  - **通过**：信息无误，可以进入下一步导出流程。
  - **驳回**：发现问题，需要驳回申请。驳回时必须选择或填写驳回原因，如"发票信息不准确"、"金额有误"、"订单不符合开票条件"等。
- **批量审核**：支持批量审核通过或批量驳回功能，提高操作效率。
- **驳回通知**：当申请被驳回时，系统自动向用户发送驳回通知，包含驳回原因，引导用户修改信息后重新提交。

#### 3.2.3. 批量导出开票信息 3️⃣

- **操作**：运营人员在选择多条申请记录后，点击"批量导出"按钮。
- **数据校验**：系统在生成Excel前，会对所选订单的总金额进行校验，确保数据准确性。
- **Excel内容**：导出的Excel文件应包含开具发票所需的全部信息，如申请ID、企业名称、纳税人识别号、开票总金额、关联订单号明细等，格式需适配税务系统要求。

#### 3.2.4. 批量导入开票结果 3️⃣

- **操作**：运营人员在线下税务系统完成开票后，整理一份ZIP压缩包，然后通过后台的"导入开票结果"功能上传。
- **ZIP包结构要求**：
  - 压缩包内需包含一个Excel文件和多个PDF格式的电子发票。
  - **Excel文件**：用于记录本次开具的发票申请信息。需包含以下字段：
    - **申请ID**（必填，用于匹配系统中的申请记录）
    - **开票状态**（必填，"成功"或"失败"）
    - **失败原因**（当状态为"失败"时必填）
  - **PDF文件**：每张成功开具的电子发票为一个独立的PDF文件，**文件名应与对应的"申请ID"保持一致**（例如：申请ID为`12345`的发票，其文件名应为`12345.pdf`），以便系统自动匹配。
- **系统处理流程**：
  1. **解压文件**：系统后台接收到ZIP包后，首先进行解压。
  2. **上传PDF至OSS**：系统遍历所有PDF文件，将其上传至公司配置的OSS（对象存储服务）中，并为每个文件生成一个唯一的、可公开访问的下载地址。
  3. **解析Excel并匹配**：系统读取Excel文件，根据"申请ID"与数据库中的开票申请记录进行匹配。
  4. **更新申请记录**：对于成功匹配的每一条记录，系统会：
     - 根据Excel中的"开票状态"字段，将申请状态更新为"已开具"或"已驳回"。
     - 对于"已开具"状态的申请，将该申请ID对应的PDF文件在OSS中的下载地址，保存到该条申请记录中。
     - 对于"已驳回"状态的申请，记录失败原因。
     - 记录操作时间。

#### 3.2.5. 发送通知 2️⃣

- **触发机制**：在批量导入开票结果并成功更新状态后，系统自动触发通知机制。
- **开票成功通知**：
  - **短信通知** 2️⃣：向申请用户的手机号发送短信，内容示例："【小B云运力】尊敬的用户，您申请的发票已开具，可直接点击链接下载：[发票下载地址]"
  - **小程序消息** 3️⃣：如果用户此前已授权，系统会通过小程序消息推送同样内容的通知，并附带可直接跳转或复制的下载链接，引导用户在小程序内或浏览器中查看。
- **开票失败通知**：
  - **短信通知** 2️⃣：向申请用户的手机号发送短信，内容示例："【小B云运力】尊敬的用户，您申请的发票开具失败，原因：[失败原因]。请登录小程序修改信息后重新申请。"
  - **小程序消息** 3️⃣：如果用户此前已授权，系统会通过小程序消息推送同样内容的通知，引导用户查看详情并修改信息。

### 3.3. 客服流程

#### 3.3.1. 修改开票信息审核 5️⃣

- **用户发起**：用户通过小程序内的"我的" -> "客服中心"联系在线客服，提出修改开票信息的请求。
- **客服核实与信息获取**：客服人员需与用户确认身份（如核对注册手机号、历史订单等信息），并要求用户提供完整、准确的新开票信息（发票抬头、税号等）。
- **客服后台修改**：身份核实无误后，客服人员在后台管理系统中找到对应用户的资料，直接为用户输入并更新其开票信息。
- **用户确认**：修改完成后，建议客服引导用户再次进入开票流程，以核对新信息是否正确。

## 4. 业务流程图

### 4.1. 用户申请开票流程图

```mermaid
graph TD
    A[用户进入'发票管理'] --> B{是否存在未开票订单}
    B -- 是 --> C[展示可开票订单列表]
    C --> D[用户选择一个或多个订单]
    D --> E{是否首次申请}
    E -- 是 --> F[引导填写发票抬头信息]
    F --> G[保存并锁定信息]
    G --> H[确认开票信息及金额]
    E -- 否 --> H
    H --> I[提交申请]
    I --> J[提示用户订阅小程序消息]
    J --> K[申请进入'待审核'状态]
    K --> L[在'开票历史'中可查]
    B -- 否 --> M[提示'暂无可开票订单']
```

### 4.2. 后台处理开票流程图

```mermaid
graph TD
    subgraph 后台管理系统
        A[运营人员进入'发票申请管理'] --> B[查看'待处理'申请列表]
        B --> C1[审核申请信息]
        C1 --> C2{审核通过?}
        C2 -- 否 --> C3[驳回申请并填写原因]
        C3 --> C4[系统通知用户申请被驳回]
        C2 -- 是 --> C[选择多条申请]
        C --> D[批量导出为Excel]
    end

    subgraph 线下操作
        D --> E[在税务系统进行开票]
        E --> E1{开票是否成功?}
        E1 -- 成功 --> F1[整理成功的电子发票]
        E1 -- 失败 --> F2[记录失败原因]
        F1 --> F[整理开票结果ZIP包]
        F2 --> F
        F --> G["包含Excel(含状态和失败原因)和以申请ID命名的PDFs"]
    end

    subgraph 后台管理系统
        G --> H[上传开票结果ZIP包]
        H --> I[系统解压,上传PDF到OSS,读取Excel,匹配并更新状态]
        I --> J{状态更新}
        J -- 成功开票 --> K1[触发成功通知]
        J -- 开票失败 --> K2[触发失败通知]
        K1 --> L1[向用户发送带发票链接的短信/小程序消息]
        K2 --> L2[向用户发送失败原因的短信/小程序消息]
    end
```

### 4.3. 开票信息修改流程图

```mermaid
graph TD
    A[用户在'客服中心'联系客服] --> B[提出修改开票信息请求]
    B --> C[用户提供新的开票信息]
    C --> D[客服核实用户身份]
    D --> E{核实通过}
    E -- 是 --> F[客服在后台直接修改用户开票信息]
    F --> G[修改完成]
    E -- 否 --> H[客服拒绝请求]
```

## 5. 页面原型/线框图 (文字描述)

### 5.1. 可开票订单列表
- **页面标题**：选择开票订单
- **内容区**：
    - 订单列表，每行包含：[ ] 复选框、订单金额（¥XX.XX）、订单摘要（如：上海 -> 北京）、完成日期。
- **底部操作栏**：
    - [ ] 全选
    - 已选：X个订单，合计：¥YYYY.YY
    - [下一步] 按钮（选择订单后高亮）

### 5.2. 开票信息维护页（首次）
- **页面标题**：填写发票信息
- **表单项**：
    - 发票抬头：[输入框]
    - 税号：[输入框]
    - 接收邮箱：[输入框]
- **提示信息**："请仔细核对，信息确认后不可修改。"
- **操作按钮**：[确认保存]

### 5.3. 确认开票信息页
- **页面标题**：确认开票
- **信息展示区**：
    - **开票金额**：¥YYYY.YY
    - **发票类型**：电子普通发票
    - **发票抬头**：XXXX有限公司
    - **税号**：XXXXXXXXXXXXXXXXXX
- **提示信息**："发票开具后将发送至您的预留邮箱 a***<EMAIL>"
- **操作按钮**：[提交申请]

### 5.4. 开票历史页
- **页面标题**：发票管理
- **Tab切换**：[申请开票] | [开票历史]
- **列表项**：
    - 抬头：XXXX有限公司
    - 金额：¥YYYY.YY
    - 申请日期：YYYY-MM-DD
    - 状态：[待审核/处理中/已开具/已驳回]（不同状态用不同颜色区分）
    - 操作：(对于'已开具'状态) [下载发票] 按钮
    - 备注：(对于'已驳回'状态) 显示驳回原因

### 5.5. 后台开票申请管理页
- **页面标题**：发票申请管理
- **筛选/搜索区**：状态下拉框（全部/待审核/已通过/已驳回/已开具）、日期范围选择器、用户ID输入框、[搜索]按钮、[重置]按钮
- **表格列**：[ ] 复选框、申请ID、用户ID、发票抬头、税号、开票金额、状态、申请时间、操作
- **操作按钮**：[审核通过]、[驳回]、[查看详情]
- **顶部操作按钮**：[批量通过]、[批量驳回]、[批量导出]、[导入开票结果]
- **驳回弹窗**：包含驳回原因下拉选择（可选择"其他"并填写自定义原因）
