package com.yunyi.express2b.module.crm.controller.admin.companyaudithistory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 企业账户审核记录新增/修改 Request VO")
@Data
public class CompanyAuditHistorySaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "8089")
    private Long id;

    @Schema(description = "企业id", example = "26593")
    private Long companyId;

    @Schema(description = "企业名称", example = "李四")
    private String companyName;

    @Schema(description = "审核人")
    private String auditUser;

    @Schema(description = "审核结果", example = "1")
    private Integer status;

    @Schema(description = "备注 审核失败原因")
    private String remarks;

}