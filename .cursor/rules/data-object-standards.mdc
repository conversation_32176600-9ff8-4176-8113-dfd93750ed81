---
description: 
globs: 
alwaysApply: true
---
# 数据对象规范 (VO/DTO/DO)

## 数据对象分类

### DO (Data Object)
- 数据库实体对象，与数据库表结构一一对应
- 位置：通常位于 `*.module.*.biz.dal.dataobject` 包下
- 命名规范：实体名+DO，如 `ExpressDO`, `OrderDO`
- 示例：[ExpressDO.java](mdc:express2b-module-express/express2b-module-express-biz/src/main/java/com/yunyi/express/dal/dataobject/ExpressDO.java)

### DTO (Data Transfer Object)
- 数据传输对象，用于服务层与控制层之间的数据传递
- 分为 Request 和 Response 两类
- 位置：通常位于 `*.module.*.api.dto` 包下
- 命名规范：
  - 请求：实体名+Request，如 `ExpressCreateRequest`
  - 响应：实体名+Response，如 `ExpressDetailResponse`
- 示例：[ExpressDTO.java](mdc:express2b-module-express/express2b-module-express-api/src/main/java/com/yunyi/express/api/dto/ExpressDTO.java)

### VO (View Object)
- 视图对象，用于展示层与前端的数据交互
- 位置：通常位于 `*.module.*.controller.vo` 包下
- 命名规范：实体名+VO，如 `ExpressVO`, `OrderVO`
- 示例：[ExpressVO.java](mdc:express2b-module-express/express2b-module-express-biz/src/main/java/com/yunyi/express/controller/vo/ExpressVO.java)

## 对象转换规范

### 转换工具类
- 使用 MapStruct 进行对象间的自动转换
- 位置：通常位于 `*.module.*.convert` 包下
- 命名规范：实体名+Convert，如 `ExpressConvert`, `OrderConvert`
- 示例：[ExpressConvert.java](mdc:express2b-module-express/express2b-module-express-biz/src/main/java/com/yunyi/express/convert/ExpressConvert.java)

### 转换原则
1. **单向转换**：DO → DTO → VO 的单向转换，避免逆向转换造成的安全问题
2. **字段校验**：DTO 负责请求数据的校验，使用 JSR-303 注解
3. **敏感数据**：在 VO 层处理敏感数据的脱敏操作
4. **按需转换**：只转换必要的字段，避免不必要的数据暴露

## 使用场景
- **Controller层**：接收和返回VO对象，与前端直接交互
- **Service层**：接收和返回DTO对象，处理业务逻辑
- **Mapper层**：操作DO对象，与数据库交互

## 注意事项
1. 避免各类对象的混用，保持层次清晰
2. DTO对象应当包含完整的数据校验逻辑
3. DO对象应与数据库表结构保持一致
4. VO对象应关注展示需求，处理数据展示格式化

