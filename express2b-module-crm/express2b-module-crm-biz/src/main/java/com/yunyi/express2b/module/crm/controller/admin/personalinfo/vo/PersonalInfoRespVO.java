package com.yunyi.express2b.module.crm.controller.admin.personalinfo.vo;

import com.yunyi.express2b.framework.common.validation.InEnum;
import com.yunyi.express2b.framework.excel.core.annotations.DictFormat;
import com.yunyi.express2b.module.crm.enums.AuditStateEnum;
import com.yunyi.express2b.module.infra.enums.DictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

import java.io.File;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PersonalInfoRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "13997")
    @ExcelProperty("主键")

    private Long id;

    @Schema(description = "企业账户信息", example = "3053")
    @ExcelProperty("企业账户信息")
    private Long companyId;

    @Schema(description = "账户id", example = "3203")
    @ExcelProperty("账户id")
    @NotBlank(message = "账户不能为空")
    private Long memberId;

    @Schema(description = "姓名", example = "李四")
    @ExcelProperty("姓名")
    private String memberName;

    @Schema(description = "联系方式")
    @ExcelProperty("联系方式")
    private String memberPhone;

    @Schema(description = "身份证号")
    @ExcelProperty("身份证号")
    private String memberIdCard;

    @Schema(description = "身份证正面")
    @ExcelProperty("身份证正面")
    private String frontCard;

    @Schema(description = "身份证反面")
    @ExcelProperty("身份证反面")
    private String reverseCard;

    @Schema(description = "状态 0-待审核 1- 审核成功 2- 审核失败")
    @ExcelProperty("状态 0-待审核 1- 审核成功 2- 审核失败")
    @InEnum(value = AuditStateEnum.class)
    private Integer state;

    @Schema(description = "审核备注（失败原因）")
    @ExcelProperty("审核备注（失败原因）")
    private String remarks;

    @Schema(description = "审核时间")
    @ExcelProperty("审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "（企业/组织唯一标识）")
    @ExcelProperty("（企业/组织唯一标识）")
    private String creditCode;

    @Schema(description = "微信头像")
    @ExcelProperty("微信头像")
    private String avatar;


    private List<MultipartFile> file;

    private List<String> imageUrls;

}