package com.yunyi.express2b.module.crm.dal.dataobject.cardlimited;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 卡种信息 DO
 *
 * <AUTHOR>
 */
@TableName("express2b_cardlimited")
@KeySequence("express2b_cardlimited_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CardlimitedDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 名称
     */
    private String cardlimitedName;
    /**
     * 零售价格,单位：分
     */
    private Integer price;
    /**
     * 可使用次数
     */
    private Integer number;
    /**
     * 优惠信息描述，html富文本
     */
    private String description;
    /**
     * 有效天数
     */
    private Integer validityPeriod;

}