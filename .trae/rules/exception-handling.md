---
description: 
globs: 
alwaysApply: true
---
# 异常处理规范

## 异常分类

系统中的异常分为以下几类：

1. **系统异常**: 由系统底层抛出的非预期异常，如数据库连接异常、NullPointerException等
2. **业务异常**: 由业务逻辑抛出的可预期异常，通常是基于业务规则的校验结果
3. **参数校验异常**: 由参数校验框架(如JSR-303)抛出的校验失败异常

## 异常基类设计

```java
/**
 * 业务异常基类
 */
public class BusinessException extends RuntimeException {
    /**
     * 错误码
     */
    private final Integer code;
    
    /**
     * 错误消息
     */
    private final String message;
    
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    public BusinessException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
    }
    
    // Getters...
}
```

## 模块异常设计

- 每个业务模块应定义自己的异常类，继承自BusinessException
- 每个模块的异常类名应遵循`{模块名}Exception`的命名规则
- 模块异常应与模块错误码配合使用

```java
/**
 * 用户模块异常
 */
public class UserException extends BusinessException {
    public UserException(Integer code, String message) {
        super(code, message);
    }
    
    public UserException(UserErrorCode errorCode) {
        super(errorCode.getCode(), errorCode.getMessage());
    }
}
```

## 错误码规范

- 错误码应当具有语义化，便于排查问题
- 错误码格式：ABBCCC，其中：
  - A: 错误类型(1-系统错误, 2-业务错误)
  - BB: 模块编号(00-通用, 01-用户, 02-订单等)
  - CCC: 具体错误编号

```java
/**
 * 用户模块错误码
 */
public enum UserErrorCode implements ErrorCode {
    /**
     * 用户不存在
     */
    USER_NOT_FOUND(201001, "用户不存在"),
    
    /**
     * 用户已存在
     */
    USER_ALREADY_EXISTS(201002, "用户已存在"),
    
    /**
     * 密码错误
     */
    PASSWORD_INCORRECT(201003, "密码错误");
    
    private final Integer code;
    private final String message;
    
    UserErrorCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    
    @Override
    public Integer getCode() {
        return code;
    }
    
    @Override
    public String getMessage() {
        return message;
    }
}
```

## 全局异常处理

- 使用Spring MVC的`@ControllerAdvice`和`@ExceptionHandler`进行全局异常处理
- 针对不同类型的异常返回统一的响应格式
- 对系统异常隐藏具体错误信息，避免信息泄露

```java
/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public CommonResult<Void> handleBusinessException(BusinessException ex) {
        log.warn("[业务异常] {}", ex.getMessage());
        return CommonResult.error(ex.getCode(), ex.getMessage());
    }
    
    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public CommonResult<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        BindingResult bindingResult = ex.getBindingResult();
        FieldError fieldError = bindingResult.getFieldError();
        String message = fieldError != null 
            ? fieldError.getDefaultMessage() 
            : "参数校验错误";
        log.warn("[参数校验异常] {}", message);
        return CommonResult.error(100001, message);
    }
    
    /**
     * 处理系统异常
     */
    @ExceptionHandler(Exception.class)
    public CommonResult<Void> handleException(Exception ex) {
        log.error("[系统异常]", ex);
        return CommonResult.error(100000, "系统繁忙，请稍后再试");
    }
}
```

## 异常使用最佳实践

1. **在适当的层次抛出异常**:
   - 数据访问层(DAO)应当只捕获并转换底层异常为业务异常
   - 业务层(Service)应当进行业务验证，抛出具体业务异常
   - 控制层(Controller)通常不应直接捕获异常，而是由全局异常处理器处理

2. **提供有用的异常信息**:
   - 异常信息应当清晰表述问题
   - 包含关键参数便于定位问题
   - 避免在异常信息中包含敏感数据

3. **避免异常堆栈**:
   - 异常传递时保留原始异常作为cause
   - 避免捕获异常后丢弃堆栈信息
   - 使用`@Slf4j`注解和日志框架记录完整异常信息

4. **谨慎使用异常流程控制**:
   - 异常不应当用于正常流程控制
   - 对于可预期的情况，应当使用条件判断而非异常处理
   - 异常应当保留给真正的异常情况使用

