package com.yunyi.express2b.module.commission.controller.admin.summary.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 分润汇总新增/修改 Request VO")
@Data
public class SummarySaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "16728")
    private Long id;

    @Schema(description = "代理商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5384")
    @NotNull(message = "代理商ID不能为空")
    private Long targetId;

    @Schema(description = "汇总日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "汇总日期不能为空")
    private LocalDate summaryDate;

    @Schema(description = "汇总金额（单位：分）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "汇总金额（单位：分）不能为空")
    private Integer totalAmount;

    @Schema(description = "明细数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "26394")
    @NotNull(message = "明细数量不能为空")
    private Integer detailCount;

}