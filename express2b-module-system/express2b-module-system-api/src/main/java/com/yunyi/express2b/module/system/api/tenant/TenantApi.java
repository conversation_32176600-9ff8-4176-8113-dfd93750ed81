package com.yunyi.express2b.module.system.api.tenant;

import com.yunyi.express2b.module.system.api.tenant.dto.TenantPackageRespDTO;
import com.yunyi.express2b.module.system.api.tenant.dto.TenantReqDTO;
import com.yunyi.express2b.module.system.api.tenant.dto.TenantSaveReqDTO;

import java.util.List;

/**
 * 多租户的 API 接口
 *
 * <AUTHOR>
 */
public interface TenantApi {

    /**
     * 获得所有租户
     *
     * @return 租户编号数组
     */
    List<Long> getTenantIdList();

    /**
     * 校验租户是否合法
     *
     * @param id 租户编号
     */
    void validateTenant(Long id);

    /**
     * 根据租户套餐名称获得租户套餐
     *
     * @param name 租户套餐名称
     * @return 租户套餐
     */
    TenantPackageRespDTO getTenantPackage(String name);
    /**
     * 获得租户
     *
     * @param id 租户编号
     * @return 租户
     */
    TenantReqDTO getTenant(Long id);

    /**
     * 创建租户
     *
     * @param createReqVO 创建信息
     * @return 租户编号
     */
    Long createTenant(TenantSaveReqDTO createReqVO);
}
