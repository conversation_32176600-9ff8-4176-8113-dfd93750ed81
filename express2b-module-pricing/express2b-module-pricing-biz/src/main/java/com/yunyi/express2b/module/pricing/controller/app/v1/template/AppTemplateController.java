package com.yunyi.express2b.module.pricing.controller.app.v1.template;

import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.pricing.controller.app.v1.template.vo.TemplateRespVO;
import com.yunyi.express2b.module.pricing.dal.dataobject.template.PricingTemplateDetailDTO;
import com.yunyi.express2b.module.pricing.service.template.TemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序 - 定价模板")
@RestController
@RequestMapping("/v1/pricing/template")
@Validated
public class AppTemplateController {

    @Resource
    private TemplateService templateService;

    /**
     * 获取定价模板详细信息，包括关联的代理商和基础价格信息
     * @param id
     * @return
     */
    @GetMapping("/get-templatedetail")
    @Operation(summary = " 获取定价模板详细信息，包括关联的代理商和基础价格信息")
    public CommonResult<TemplateRespVO> getTemplate(@RequestParam("id") Long id) {
        PricingTemplateDetailDTO template = templateService.getTemplateid(id);
        return success(BeanUtils.toBean(template, TemplateRespVO.class));
    }


    /**
     * 作为代理商我可以查询平台提供的定价模板，了解首重/续重价格规则
     * @param
     * @return
     */
    @GetMapping("/get-template-pricing-rules")
    @Operation(summary = "作为代理商我可以查询平台提供的定价模板，了解首重/续重价格规则。")
    public CommonResult<List<TemplateRespVO>> getTemplatePricingRules(@RequestParam("templateId") Long templateId) {
        List<PricingTemplateDetailDTO> template = templateService.getTemplatePricingRules(templateId);
        return success(BeanUtils.toBean(template, TemplateRespVO.class));
    }




}