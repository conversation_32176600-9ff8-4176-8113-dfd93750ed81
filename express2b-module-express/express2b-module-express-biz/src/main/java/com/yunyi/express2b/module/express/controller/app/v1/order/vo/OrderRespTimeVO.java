package com.yunyi.express2b.module.express.controller.app.v1.order.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yunyi.express2b.framework.common.validation.InEnum;
import com.yunyi.express2b.framework.desensitize.core.slider.annotation.ChineseNameDesensitize;
import com.yunyi.express2b.framework.desensitize.core.slider.annotation.FixedPhoneDesensitize;
import com.yunyi.express2b.framework.excel.core.convert.DictConvert;
import com.yunyi.express2b.module.express.controller.app.v1.products.vo.ProductsRespVO;
import com.yunyi.express2b.module.express.enums.RefundStatusTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/24 22:11
 */

public class OrderRespTimeVO {
    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18150")
    @ExcelProperty("订单ID")
    private Long id;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单编号")
    private String orderNo;

    @Schema(description = "批量订单主键", example = "3570")
    @ExcelProperty("批量订单主键")
    private Long batchId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13615")
    @ExcelProperty("用户ID")
    private Long personalId;

    @Schema(description = "快递品牌ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19367")
    @ExcelProperty("快递品牌ID")
    private Long brandId;

    @Schema(description = "阶梯价策略标志", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("阶梯价策略标志")
    private String pricingKey;

    @Schema(description = "分润策略标志")
    @ExcelProperty("分润策略标志")
    private String profitKey;


    @InEnum(value = RefundStatusTypeEnum.class, message = "订单状态不能为空")
    @Schema(description = "订单状态,参见 OrderStatusTypeEnum 枚举", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "订单状态", converter = DictConvert.class)
    private String status;

    @Schema(description = "寄件人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("寄件人姓名")
    @ChineseNameDesensitize(prefixKeep = 1, suffixKeep = 0, replacer = "*")
    private String senderName;

    @FixedPhoneDesensitize(prefixKeep = 3, suffixKeep = 2, replacer = "*")
    @Schema(description = "寄件人电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("寄件人电话")
    private String senderPhone;

    @Schema(description = "寄件人地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("寄件人地址")
    private String senderAddress;

    @Schema(description = "寄件人地址ID", example = "29668")
    @ExcelProperty("寄件人地址ID")
    private Long senderAddressId;

    @Schema(description = "收件人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("收件人姓名")
    @ChineseNameDesensitize(prefixKeep = 1, replacer = "*")
    private String receiverName;

    @Schema(description = "收件人电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收件人电话")
    @FixedPhoneDesensitize(prefixKeep = 3, suffixKeep = 2, replacer = "*")
    private String receiverPhone;

    @Schema(description = "收件人地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收件人地址")
    private String receiverAddress;

    @Schema(description = "收件人地址ID", example = "19854")
    @ExcelProperty("收件人地址ID")
    private Long receiverAddressId;

    @Schema(description = "成本金额")
    @ExcelProperty("成本金额")
    private Integer costAmount;

    @Schema(description = "零售金额")
    @ExcelProperty("零售金额")
    private Integer retailAmount;

    @Schema(description = "实收金额|订单金额")
    @ExcelProperty("实收金额|订单金额")
    private Integer receivedAmount;

    @Schema(description = "优惠总金额")
    @ExcelProperty("优惠总金额")
    private Integer discountAmount;

    @Schema(description = "代理商佣金")
    @ExcelProperty("代理商佣金")
    private Integer agentCommission;

    @Schema(description = "团长佣金")
    @ExcelProperty("团长佣金")
    private Integer groupLeaderCommission;

    @Schema(description = "快递运单号")
    @ExcelProperty("快递运单号")
    private String trackingNumber;

    @Schema(description = "物品名称", example = "芋艿")
    @ExcelProperty("物品名称")
    private String itemName;

    @Schema(description = "物品分类")
    @ExcelProperty("物品分类")
    private String itemCategory;

    @Schema(description = "物品数量")
    @ExcelProperty("物品数量")
    private Integer itemQuantity;

    @Schema(description = "物品重量（kg）")
    @ExcelProperty("物品重量（kg）")
    private BigDecimal itemWeight;

    @Schema(description = "物品价值（元）")
    @ExcelProperty("物品价值（元）")
    private BigDecimal itemValue;

    @Schema(description = "包装详情")
    @ExcelProperty("包装详情")
    private String packagingDetails;

    @Schema(description = "是否保价", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否保价")
    private Boolean isInsured;

    @Schema(description = "保价金额（元）")
    @ExcelProperty("保价金额（元）")
    private BigDecimal insuredAmount;

    @Schema(description = "预约日期")
    @ExcelProperty("预约日期")
    private LocalDate scheduledDate;

    @Schema(description = "收件员姓名", example = "李四")
    @ExcelProperty("收件员姓名")
    private String receiverStaffName;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "支付类型", example = "2")
    private String payType;

    @Schema(description = "用户地址")
    private String memberIp;

    @Schema(description = "预约取件起止时间")
    private LocalTime scheduledStartTime;

    @Schema(description = "预约取件结束时间")
    private LocalTime scheduledEndTime;

    @Schema(description = "省份名称")
    private String provinceName;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "区名称")
    private String districtName;

    @Schema(description = "统一运力订单的唯一标识")
    private String ucmOrderSn;

    @Schema(description = "取件的预约日期")
    private String appointmentTime;

    @Schema(description = "快递公司code，和统一运力对应（expressCode），对应brand表中brand_key")
    private String expressCode;

    @Schema(description = "产品类型")
    private String productCode;

    @Schema(description = "次卡编号")
    private String cardNumber;

    @Schema(description = "物品集合")
    List<ProductsRespVO> productsRespVOList;
}
