package com.yunyi.express2b.module.crm.controller.admin.cardlimited.vo;

import cn.hutool.core.date.DateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 次卡购买
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/22 10:54
 */
/**
 *
 * <AUTHOR>
 */
@Schema(description = "次卡购买 VO")
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
public class CardlimitedShopVo {
    @Schema(description = "卡种id")
    private Long cardlimitedId;

    @Schema(description = "用户id")
    private Long personalId;

    @Schema(description = "卡种名称")
    private String cardName;

    @Schema(description = "使用次数")
    private Integer total;

    @Schema(description = "剩余次数")
    private Integer remaining;

    @Schema(description = "购买时间")
    private DateTime createTime;

    @Schema(description = "卡开始时间")
    private DateTime cardStartTime;

    @Schema(description = "卡结束时间")
    private DateTime cardEndTime;

    @Schema(description = "卡状态")
    private Integer status;



}
