package com.yunyi.express2b.module.crm.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/**
 * RSA 密钥配置类
 * <p>
 * 用于从 application.yml 配置文件中加载 RSA 的公钥和私钥。
 *
 * <AUTHOR> Assistant
 */
@ConfigurationProperties(prefix = "express2b.crm.rsa")
@Component
@Validated
@Data
public class RsaKeyProperties {

    /**
     * RSA 公钥 (Base64-encoded)
     */
    private String publicKey;

    /**
     * RSA 私钥 (Base64-encoded)
     */
    private String privateKey;
} 