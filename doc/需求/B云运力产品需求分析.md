# B云运力产品需求分析

更新记录

- 2025-3-13 @解蕾 ：增加次卡及小程序定制需求
- 2025-3-14 @解蕾 ：补充场景业务流程图
- 2024-3-16 @解蕾 ：增加开具发票需求
- 2024-3-17 @解蕾 ：
   - 更新物流跟踪需求，不需要自己实现物流跟踪了，由运力项目提供数据。
   - 更新项目工期计划安全。
- 2024-3-18 @解蕾 
   - 增加资金风险需求。
---

# 一、项目背景与目标

## 1.1 背景

2024年，公司成功研发"发一件"微信小程序快递寄件应用，凭借其高效的运营模式，在市场中获得了广泛好评，迅速积累了大量用户流量。在当今互联网时代，流量资源是互联网企业发展的关键要素，而公司已掌握了一套独特的免费获取流量的方法。"发一件"小程序主要服务于C端用户，为满足更广泛的市场需求，公司决定于2025年立项"小B云运力"微信小程序，专注于为B端企业提供快递寄件服务，进一步扩大流量效应，提升市场竞争力。

公司名称：上海允屹信息科技有限公司

## 1.2 产品定位

"小B云运力"致力于为日发单量2-10单的中小企业提供高效、低成本的快递解决方案，满足电商卖家、微商、直播卖家、小微企业及供应链/仓库的多样化需求。通过微信、支付宝小程序以及API对接，实现多平台无缝连接，助力企业提升发货效率，优化资金结算流程，为企业用户提供更加便捷、高效的快递寄件服务。

## 1.3 核心优势

- **低成本高效发货**：专为企业用户设计，提供极具竞争力的价格，帮助企业降低物流成本。
- **多平台对接**：支持API接口，轻松对接电商平台、ERP系统，实现订单自动同步与批量处理。
- **便捷资金结算**：提供灵活的资金结算方式，支持企业对账、分账等功能，简化财务管理。
- **企业级功能**：涵盖批量发件、地址管理、物流跟踪、数据报表等，满足企业多样化需求。
- **无缝体验**：基于微信和支付宝小程序，操作简单，随时随地管理发货。

## 1.4 目标用户

- **电商卖家**：需要高效处理订单、降低物流成本。
- **微商/直播卖家**：追求快速发货与便捷管理。
- **小微企业**：需要低成本、灵活的物流解决方案。
- **供应链/仓库**：注重多平台对接与批量处理能力。

## 1.5 用户界面

- **小程序端**：基于微信和支付宝，操作简单，支持快速发件、订单管理、物流跟踪等功能。
- **管理后台**：提供企业级管理工具，支持批量操作、数据分析和资金结算。

## 1.6 核心目标

￮  **低成本** ：通过规模优势或合作谈判降低物流企业结算价。

￮  **高效率** ：优化发件流程，支持单件 /批量操作及API集成。

￮**可扩展性** ：模块化设计支持未来功能迭代及流量增长。考虑后期微服务改造，尽量送耦合。

￮**合规性** ：后期过渡至 APP实现预存款功能（政策要求）。

## 1.7 参考资料

-  [小B云运力价格方案](https://ones.cn/wiki/#/team/HMBf25vo/page/2xoQAZmD)

# 二、产品说明

## 2.1 产品用户

![image](assets/resources/JbqC4erSkko86Xo_Rd60Cjh-ZtpmO1wvsdNjBfFiLKg.png)

### 2.1.1外部用户

#### 2.1.1.1 运营用户

运营用户不在本系统中维护，由外部系统提供能力。

| 角色   | 权限描述                                                                                                                    |
|--------|-----------------------------------------------------------------------------------------------------------------------------|
| 代理商 | 1. 需与公司商务协商并获得平台授权，由超管或运营人员建立账户。  2. 可发展终端客户，终端用户订单利润通过价格方案分配给代理商。    |
| 团长   | 1. 无身份限制，所有注册用户均可成为团长，应用内提供晋升团长功能。 2. 成为团长后，可推荐他人使用应用，获取被推荐人发件的利润分佣。 |

#### 2.1.1.2 终端用户

| 角色     | 权限描述                                                                                                                                                                                 |
|----------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 企业用户 | 针对企业账户下的真实使用用户，一个企业账户可以注册多个企业用户，企业用户具有实际操作权限，可进行订单管理、发货操作、查询物流信息等。                                                           |
| 企业账户 | 支持个人和企业自主进行企业账户的注册，个人通过身份证信息进行注册，企业用户通过营业执照进行注册。企业账户是一个机构的概念，单独的企业账户并不能登录系统，需要在企业账户下建立企业用户才能使用。 |

### 2.1.2 内部用户

| 角色 | 权限描述                     |
|------|------------------------------|
| 超管 | 具备所有权限                 |
| 运营 | 可查看运营有关数据           |
| 财务 | 可查看财务有关数据           |
| 客服 | 可查看订单、工单、客服记录信息 |

### 2.1.2 整体业务流程

```mermaid
sequenceDiagram
    participant 客户
    participant 小B云运力
    participant 运力中心
    participant 登录/支付中心
    participant 快递员

    客户->>客户: 注册账号
    客户->>客户: 录入收发件地址信息
    客户->>小B云运力: 获取所有品牌的运价
    小B云运力->>运力中心: 查询线路的所以品牌运价信心
    小B云运力->>小B云运力: 根据运价计算模型计算运价
    运力中心->>小B云运力: 返回运价信息
    小B云运力->>客户: 返回运价信息
    客户->>小B云运力: 确认快递品牌并下单
    小B云运力->>小B云运力: 校验收发件地址信息
    小B云运力->>运力中心: 查询品牌和线路的运价
    运力中心->>小B云运力: 返回运价信息
    小B云运力->>小B云运力: 计算运价
    小B云运力->>登录/支付中心: 发起收费
    客户->>登录/支付中心: 进行付款
    登录/支付中心-->>小B云运力: 付款成功通知
    小B云运力->>运力中心: 确认下单
    运力中心->>运力中心:寻找运力资源下单
    运力中心-->>快递员: 派出快递员取件
    快递员->>快递员: 判断是否为禁运物品
    alt 是禁运物品
        快递员-->>运力中心: 通知禁运
        运力中心->>小B云运力: 通知禁运
        小B云运力->>小B云运力: 生成退款单，更新订单状态
        小B云运力-->>登录/支付中心: 通知用户
        小B云运力->>登录/支付中心: 通知退款
    else 非禁运物品
        快递员->>快递员: 判断重量和体积是否符合
        alt 符合
            快递员-->>运力中心: 通知取件完成
            运力中心->>小B云运力: 通知取件完成
            小B云运力->>小B云运力: 更新订单状态
        else 不符
            快递员-->>运力中心: 通知重量变更
            运力中心->>小B云运力: 通知运价更新
            小B云运力->>小B云运力: 更新订单状态
            alt 金额减少
                小B云运力->>登录/支付中心: 发送微信通知
                小B云运力->>小B云运力: 生成退款单
                小B云运力->>登录/支付中心: 退款操作
                登录/支付中心-->>小B云运力: 返回退款结果
                小B云运力->>小B云运力: 更新订单
            else 金额增加
                小B云运力->>小B云运力: 生成新的支付单
                小B云运力->>登录/支付中心: 发送微信通知
                客户->>登录/支付中心: 支付新增金额
                登录/支付中心-->>小B云运力: 支付成功通知
                小B云运力->>运力中心: 更新订单
            end
            
        end
        运力中心-->>小B云运力: 通知派送完成
        小B云运力->>小B云运力: 更新订单状态
    end
```
# 三、核心功能模块

## 3.1 企业管理中心

企业管理是指本系统的终端用户的管理，用户在首次使用系统时即成为企业用户，通过微信或手机号登录即可，可使用系统小程序的所以功能，没有限制。在升级企业账户后，可获得专属后台，可以管理子账号（即之前的企业用户，升级使用的自动成为企业管理员账户）

子账号采用邀请链接的方式加入企业。

```mermaid
graph LR
    A[生成邀请链接] -->|线下渠道发给用户| B[点击链接]
    B -->|拉起微信| C{判断是否登录}
    C -->|未登录| D[[登录流程]]
    D -->|登录成功| E
    C -->|已登录| E[申请加入]
    E --> F[后台审核]
    F -->|审核通过| G[建立联系]
    F -->|审核未通过| H[发送通知]
```
- 功能对比

| 功能维度     | 企业用户           | 企业管理员账户          |
|--------------|--------------------|-------------------------|
| **功能权限** | 全部发件/查询功能1 | +子账号管理+分润设置    |
| **数据范围** | 仅个人操作记录     | 机构维度全局数据3       |
| **账号数量** | 单一登录凭证       | 自动获机构主账号资格    |
| **典型场景** | 快递员日常发件     | 企业财务对账/多员工管理 |

### 3.1.1 整体业务流程图

```mermaid
flowchart LR

A[普通用户] -->|微信/手机号注册| B(企业用户)
B --> C[使用小程序功能]
B --> D[申请升级企业账户]
D --> E[提交资质认证]
E --> F[超管审核]
F --> G[审核通过]
G --> H(企业管理员)
H --> I[管理企业信息]
H --> J[管理子账号]
H --> K[查看经营数据分析]
H --> L[定制小程序]
```
## 3.2 发件核心功能

### 3.2.1 单独发件

1. **手动填单**

   - **收寄信息填写**：用户需要手动输入寄件人和收件人的详细信息，包括姓名、联系电话、地址等。系统应提供清晰的输入框和提示信息，引导用户准确填写。
   - **物流公司选择**：系统列出多家可选的物流公司，用户根据自身需求（如价格、速度、服务质量等）进行选择。同时，系统可提供各物流公司的简单介绍和评价，帮助用户决策。
2. **自动填充历史地址/模板**

   - **历史地址记录**：系统自动保存用户之前使用过的收寄地址，当用户再次填写时，可快速从历史记录中选择，避免重复输入，提高效率。
   - **模板功能**：用户可以创建常用的地址模板，如家庭地址、公司地址等。在发件时，直接调用模板即可快速填充地址信息，适合经常给固定地址寄送快递的用户。
3. **地址识别填写**

   - **智能地址识别**：用户只需输入关键词（如公司名称、地标建筑等），系统通过与地图API或地址数据库的连接，自动识别并补全详细的地址信息，减少用户输入负担，提高地址填写的准确性和速度。
   - **一键地址填写识别**：用户可以讲一段包含了姓名，电话，地址的文字粘贴到输入框中，系统自动识别出各项属性，登记到地址信息中。
### 3.2.2 批量发件

1. **Excel模板导入**

   - **模板提供与下载**：系统提供标准的Excel模板，用户可下载后按照模板格式填写批量订单的收寄信息和相关数据。
   - **数据导入与校验**：用户将填写好的Excel文件上传，系统自动读取并导入数据。同时，对导入的数据进行严格校验，检查格式是否正确、信息是否完整等。对于不符合要求的数据，系统应明确指出错误位置和原因，方便用户修改。
   - **自动标准化处理**：对导入的地址等信息进行自动标准化处理，统一格式和规范，确保数据的一致性和准确性，便于后续的发件操作和物流跟踪。
2. **API对接外部系统**

   - **对接电商平台**：与各大电商平台（如淘宝、京东等）的API进行对接，用户可以直接获取电商平台上的订单数据，无需手动复制粘贴，实现订单信息的自动同步，提高发件效率，尤其适合电商卖家批量发货的场景。
   - **对接进销存工具**：与企业的进销存管理系统进行API对接，将销售订单数据直接转化为发件订单，实现业务流程的无缝衔接，提高企业的整体运营效率，减少人工干预和数据错误的风险。
   - **数据安全与稳定性**：在API对接过程中，要确保数据传输的安全性和稳定性，采用加密传输、数据校验等技术手段，防止数据泄露和丢失。同时，要与外部系统保持良好的沟通和协调，及时处理可能出现的接口问题。
3. **批量订单操作**

   - **单条记录修改/删除**：在批量导入的订单中，用户可以方便地对单条记录进行修改或删除操作，以纠正错误或调整发件计划。系统应提供直观的操作界面和简单的操作步骤，确保用户能够快速完成修改。
   - **批量操作功能**：除了单条记录操作外，系统还应支持批量修改、批量删除等操作，方便用户对大量订单进行统一处理，提高工作效率。例如，用户可以一次性修改多个订单的物流方式或发货日期等。
#### 接口系统

 由运力项目提供发件能力，待补充运力项目接口。

#### 业务流程图

```mermaid
flowchart LR
    %% 单独发件流程
    B[进入发件界面]
    B --> C{选择发件方式}
    C --> D[单独发件]
    D --> E[手动填写收寄信息]
    D --> E1[一键地址填写]
    D --> E2[智能地址识别]
    E --> F[选择物流公司]
    E1 --> F
    E2 --> F
    F --> G[确认发件]
    G --> G1[[付款流程]]
    G1 -- 付款成功 --> H[发件完成]

    %% 批量发件流程
    C --> I[批量发件]
    I --> L[下载Excel模板并填写]
    L --> M[上传Excel文件]
    M --> N{系统校验数据}
    N --校验失败--> L
    N --> P[确认批量发件]
    P --> P1[[付款流程]]
    P1 --付款成功--- H

    C --> Q[API对接导入]
    Q --> R[与外部系统API对接]
    R --> S[接收订单数据]
    S --> T[数据处理与整合]
    T --> U[确认批量发件]
    U --> U1[[付款流程]]
    U1 --付款成功--> H

    %% 公共操作
    H --> V[查看发件记录]
    V --> W[结束]
```
### 3.2.3 物流跟踪

#### 主动模式

当用户有查询物流信息的需求时，系统想运力项目获取物流跟踪信息，采用主动拉取方案，做本地redis缓存，时间30分钟。

1. **异常处理机制**

   - **网络异常处理**：在向物流公司API拉取数据过程中，若遇到网络连接失败、超时等异常情况，系统会自动记录错误信息，并在设定的时间间隔后重试拉取操作，重试次数可配置，如最多重试3次。若仍无法成功获取数据，则向用户发送拉取失败通知，告知用户具体错误原因，如"网络连接超时，请检查网络设置后重试"。
   - **数据解析错误处理**：当接收到物流公司的API响应数据后，若解析过程中出现错误，如数据格式不符合预期、关键字段缺失等，系统会记录详细的解析错误日志，同时将原始数据和错误信息保存，方便后续排查问题。
- 业务流程图

```mermaid
flowchart LR
    b1[用户查看物流跟踪信息] --> a1[Redis缓存]
    a1 -->|未匹配| b
    a1 -->|匹配| b1
    subgraph 内部流程
    a1 --> b3[数据校验]
    b3 --> b4[Redis缓存]
    b4 --> b5[数据展示]
    end

    subgraph 外部项目-运力项目
    a[A运力资源]-->b[物流状态更新]
    b-->b3
    end
```
## 发票开具

```mermaid
graph TD
    查看可开发票订单 --> 选择订单开发票
    选择订单开发票 --> 确认开票信息
    确认开票信息 --> 提交开票申请
    提交开票申请 --> 系统审核开票申请
    系统审核开票申请 --> 审核通过
    审核通过 --> 发票生成
    发票生成 --> 发票记录到开票历史
    发票记录到开票历史 --> 用户查看开票历史
    用户查看开票历史 --> 下载发票

    确认开票信息 --> 设置开票信息

    设置开票信息 --> 修改发票抬头
    修改发票抬头 --> 保存发票抬头设置
    保存发票抬头设置 --> 返回设置开票信息

    设置开票信息 --> 修改接收邮箱
    修改接收邮箱 --> 保存接收邮箱
    保存接收邮箱 --> 返回设置开票信息
```


- **查看可开发票订单**：用户可以查看所有已完成支付且满足开票条件的订单。
- **开票申请**：用户可以选择一个或多个订单提交开票申请，并填写发票信息。
- **发票历史记录**：用户可以查看所有已开具的发票记录，包括发票状态、开票日期、下载链接等。
- **发票抬头和邮箱设置**：用户可以设置默认的发票抬头信息，方便后续开票操作。

## 3.3 电子支付与结算

### 3.3.1 微信支付

**微信支付流程**， [点击查看官方文档](%5B%E5%BC%80%E5%8F%91%E6%8C%87%E5%BC%95_%E5%B0%8F%E7%A8%8B%E5%BA%8F%E6%94%AF%E4%BB%98%7C%E5%BE%AE%E4%BF%A1%E6%94%AF%E4%BB%98%E5%95%86%E6%88%B7%E6%96%87%E6%A1%A3%E4%B8%AD%E5%BF%83%5D(https://pay.weixin.qq.com/doc/v3/merchant/4012791911))

![image](assets/resources/UiUohjgllBF1gdNVOkGxM1GMA_nWonwOdZl75jRNS8o.png)

---

**系统业务流程图**

```mermaid
graph TD
    A[用户选择微信支付] --> B[系统生成支付订单]
    B --> C[调用微信支付统一下单接口]
    C --> E[用户跳转至微信支付页面]
    E --> F{用户完成支付?}
    F -->|是| G[微信支付平台异步通知系统]
    F -->|否| H[支付超时/取消]
    F -->|否| H1[用户取消支付]
    H1 --> A
    G --> I{签名验证通过?}
    I -->|是| L[[执行业务处理-给运力资源下单-更新订单状态-更新支付单状态]]
    L --> M[返回处理结果给客户]
    I -->|否| K[记录异常通知]
    H --> N[订单状态标记为作废]
    K --> A
```
---

```mermaid
flowchart LR
    A[开始] --> B[用户下单]
    B --> C[选择支付方式]
    C --> D{微信支付}
    C --> E{支付宝支付}

    D --> F[调用微信支付API]
    F --> G[用户支付]
    G --> H[支付结果通知系统]
    H --> I[更新订单支付状态]

    E --> J[调用支付宝支付API]
    J --> K[用户支付]
    K --> L[支付结果通知系统]
    L --> I

    I --> M[快递人员揽收]
    M --> N[核算实际重量]
    N --> O[重新计算运价]
    O --> P{运价是否变化}

    P --> Q[否]
    P --> R[是]
    R --> S[生成新的支付单]
    S --> T[通知用户支付]
    R --> U[生成退款单]
    U --> V[系统自动退款]

    I --> W[每日用户侧对账]
    W --> X[应收 vs 实收对比]
    X --> Y[输出差异账单]
    Y --> Z[处理退款/调整状态]

    I --> AA[每周物流侧对账]
    AA --> AB[应付 vs 成本价对比]
    AB --> AC[差异>5%标记审计]

    I --> AD[营销对账]
    AD --> AE[积分/广告补贴发放量对比]
    AE --> AF[异常发放自动冻结账户]

    I --> AG[企业预存款支付]
    AG --> AH[扣除预存款余额]
    AG --> AI[享受预存款折扣]

    I --> AJ[第三方支付管理]
    AJ --> AK[支付手续费计算]
    AJ --> AL[证书加密存储]
```
- **集成微信支付API**：系统与微信支付平台进行对接，开发人员按照微信官方提供的API文档，集成支付功能到系统中。在用户支付时，系统生成微信支付所需的参数，调用微信支付接口，引导用户进行支付操作。
- **支付流程处理**：用户选择微信支付后，系统生成唯一的支付订单号，并将支付信息传递给微信支付平台。用户在微信支付页面完成支付后，微信支付平台将支付结果实时通知系统，系统根据支付结果更新订单状态，并进行相应的业务处理，如发货等。
- **个人账户支付**：与支付宝平台对接，集成支付宝支付接口。用户选择支付宝支付时，系统生成支付宝支付所需的参数，跳转到支付宝支付页面，用户使用个人支付宝账户完成支付，支付成功后支付宝平台通知系统更新订单状态。
- **企业账户支付调研**：对于企业账户支付，需要进行详细调研。了解支付宝企业账户支付的流程、费用标准、认证要求等。与支付宝商务团队沟通，获取企业支付接口的接入条件和技术文档，评估系统接入的可行性和成本。同时，研究企业支付在安全性、合规性方面的要求，确保系统能够满足相关规定。

#### 支付记录与价格策略

- **价格组成记录**：系统详细记录每单支付的基础价、阶梯折扣、等级折扣、积分/广告补贴等价格组成信息。在生成支付订单时，将这些信息存储在数据库中，方便后续查询和对账。同时，在支付结果页面和订单详情页面向用户展示价格明细，增加支付透明度。
- **历史价格策略保存**：保存历史价格策略，包括不同日期、不同快递公司、不同线路的基准价等信息。在价格策略变更时，系统自动记录变更时间和新旧策略内容，确保在对账时能够准确追溯每个订单所适用的价格策略，避免因策略变更导致的对账混乱。

### 3.3.2 运价确认

1. **揽收后运价核算**

   - **重量信息录入**：快递人员在揽收快递后，通过运力资源界面录入快递的实际重量等信息。
   - **运价重新计算**：根据录入的重量信息，运力资源按照运价规则重新计算运价。运价规则可能包括基础运费、重量阶梯收费、区域差异收费等。
   - **运价变化通知**：若运价发生变化，运力资源会立即通知本系统，本系统生成新的支付单，并通过微信通知的方式通知用户。通知内容包括新的运价金额、变化原因、支付截止时间等信息，确保用户及时了解并处理支付事宜。
2. **支付与退款处理**

   - **新支付单生成**：在运价变化后，系统为用户生成新的支付单，支付单包含新的运价金额、订单号、支付方式等信息。用户可以在系统中查看新的支付单详情，并选择合适的支付方式进行支付。
   - **退款单生成与处理**：如果是收多了费用，系统自动生成退款单，并等待退款处理。退款单记录了多收的金额、原支付订单号、退款原因等信息。系统与支付平台对接，自动发起退款申请，跟踪退款进度，并在退款完成后更新订单的支付状态和退款记录。
### 3.3.3 用户侧对账

1. **每日对账流程**

   - **对账数据准备**：系统在每日设定的时间自动生成用户侧对账数据，包括应收金额（根据订单价格策略计算的应收款总额）、实收金额（实际通过支付平台收到的金额）、支付手续费等信息。对账数据按用户、订单等维度进行分类汇总，方便对账操作。
   - **差异账单输出**：系统自动对比应收金额和实收金额，若存在差异，输出详细的差异账单。差异账单列出每笔差异订单的订单号、应收金额、实收金额、差异金额、支付方式、支付时间等信息，同时标注差异原因，如重复收款、多收款、漏收款、少收款等。
   - **退款与状态调整**：对于重复收款、多收款的情况，系统提供便捷的退款功能，用户或管理员可以一键发起退款申请，系统自动处理退款流程。对于漏收款、少收款的订单，系统自动调整订单的付款状态，并记录调整原因和时间，确保对账结果的准确性和完整性。
2. **对账结果查询与报表生成**

   - **对账结果查询界面**：系统提供直观的对账结果查询界面，用户可以按日期范围、用户ID、订单号等条件筛选查询对账结果。查询结果以表格形式展示，包含对账日期、应收金额、实收金额、差异金额、差异订单数等关键信息。
   - **对账报表生成与导出**：系统支持生成每日对账报表，报表格式包括Excel、PDF等。报表内容除了对账结果数据外，还可以包含支付手续费明细、退款记录、对账操作日志等。用户可以将报表导出保存到本地，方便进一步分析和存档。
### 3.3.4 物流侧对账

1. **每周对账流程**

   - **对账数据汇总**：系统在每周设定的时间自动汇总物流侧对账数据，包括应付物流公司费用（根据合同价和单量计算的总费用）、系统计算成本价（系统根据实际运价、折扣等计算的成本总价）等信息。对账数据按物流公司、线路、时间等维度进行分类汇总，确保对账的全面性和准确性。
   - **差异标记与审计**：系统自动对比应付物流公司费用和系统计算成本价，若差异超过5%，系统自动标记该对账记录为审计状态。审计状态的记录需要经过专门的审计流程，由相关人员进行详细核查，找出差异原因，如价格计算错误、订单遗漏、数据同步问题等，并记录审计结果和处理措施。
2. **对账结果展示与分析**

   - **对账结果展示界面**：系统为管理人员提供专门的对账结果展示界面，界面以图表和表格相结合的方式展示每周对账情况。图表可以直观展示应付费用和成本价的趋势变化、差异分布等信息，表格则详细列出每条对账记录的具体数据，包括物流公司名称、对账周期、应付费用、成本价、差异金额、差异百分比、对账状态等。
   - **对账报表生成与导出**：系统支持生成每日对账报表，报表格式包括Excel、PDF等。报表内容除了对账结果数据外，还可以包含支付手续费明细、退款记录、对账操作日志等。用户可以将报表导出保存到本地，方便进一步分析和存档。
### 3.3.5 营销对账

1. **积分/广告补贴对账**

   - **发放量与预算消耗量对比**：系统每日自动对比积分/广告补贴的实际发放量与预算消耗量。实际发放量根据用户在支付过程中使用的积分和广告补贴金额统计，预算消耗量则是根据营销活动计划预先设定的额度。系统记录每天的对比结果，生成相应的对账数据。
   - **异常发放检测与处理**：系统实时监测积分/广告补贴的发放情况，对于超过设定阈值（如单笔发放金额过大、单日发放次数过多等）或发放频率异常（如短时间内集中发放给特定用户群体）的情况，自动触发异常检测机制。一旦检测到异常发放，系统立即冻结相关账户，并通知营销管理人员进行调查处理。冻结的账户在问题解决后，经过审核可以解除冻结，恢复正常使用。
2. **营销对账报表与活动效果评估**

   - **营销对账报表生成**：系统定期生成积分/广告补贴营销对账报表，报表内容包括活动名称、活动时间、预算总额、已发放金额、剩余预算、异常发放记录等信息。报表以直观的图表和表格形式展示，帮助营销人员全面了解营销活动的执行情况和资金使用情况。
   - **活动效果评估与优化**：基于营销对账报表和相关数据，系统提供活动效果评估功能。通过分析积分/广告补贴的使用率、转化率、用户参与度等指标，评估营销活动的实际效果。根据评估结果，营销人员可以及时调整活动策略，如优化补贴规则、扩大活动范围、调整预算分配等，以提高营销活动的投资回报率和用户满意度。
### 3.3.6 预存款模式

1. **企业预充值流程**

   - **预存款账户开通**：企业用户在系统中申请开通预存款账户，填写企业信息、银行账户信息等必要资料。系统审核通过后，为企业用户创建唯一的预存款账户，并分配相应的账户权限。
   - **预充值操作**：企业用户可以通过多种支付方式（如银行转账、支付宝企业支付、微信支付等）向预存款账户进行充值。在充值时，系统生成充值申请单，记录充值金额、支付方式、充值时间等信息。企业用户完成支付后，系统自动更新预存款账户余额，并发送充值成功通知。
   - **预存款折扣应用**：企业用户在支付快递费用时，系统优先使用预存款账户余额进行支付，并根据预存款折扣规则自动享受相应的折扣优惠。折扣规则可以在企业用户开通预存款账户时进行个性化设置，如根据不同充值金额设置不同的折扣率，或针对特定快递线路、时间段设置专属折扣。
2. **预存款管理与监控**

   - **预存款余额查询与提醒**：系统为企业的预存款账户提供实时余额查询功能，企业用户可以在系统中随时查看当前余额、充值记录、消费记录等详细信息。同时，系统设置余额提醒功能，当余额低于设定的预警值时，自动向企业用户发送余额不足提醒，提醒企业及时充值，避免影响正常业务开展。
   - **预存款使用报表生成**：系统定期生成预存款使用报表，报表内容包括充值时间、充值金额、消费时间、消费金额、剩余余额、折扣优惠金额等信息。报表以清晰的表格和图表形式展示，帮助企业用户了解预存款的使用情况和资金流向，便于进行财务管理和成本控制。
### 3.3.7 第三方支付机构管理

1. **支付手续费管理**

   - **手续费配置与计算**：系统与各第三方支付机构协商确定支付手续费率，并在系统中进行配置。根据不同支付方式（如微信支付、支付宝支付等）、不同交易类型（如普通支付、退款等）设置相应的手续费率。在每笔支付交易完成后，系统根据实际支付金额和手续费率自动计算支付手续费，并记录在支付交易记录中。
   - **手续费结算与对账**：系统定期与第三方支付机构进行手续费结算，根据支付交易记录汇总手续费金额，生成结算报表。同时，与第三方支付机构提供的对账单进行核对，确保手续费计算的准确性和一致性。若发现对账差异，及时与支付机构沟通解决，保证双方账目清晰。
2. **证书加密存储与安全管理**

   - **证书加密存储**：第三方支付机构提供的支付证书是支付安全的关键，系统采用高级加密算法对证书进行加密存储。证书文件存储在安全的服务器环境中，限制访问权限，只有经过授权的系统模块和人员才能解密和使用证书，防止证书泄露导致的安全风险
## 3.4 **订单中心**

```mermaid
flowchart TB
    A[开始] --> B[用户下单]
    B --> C[录入收发件地址收发人信息]

C --> D[选择快递品牌]
    D --> E[系统向小B云运力获取各品牌运价]
E --> F[用户确认快递品牌]
    F --> G[向小B云运力发件]
G --> H[小B云运力校验收发件信息]
H --> I[信息无误向客户收费]
I --> J[客户通过微信支付付款]
    J --> K{付款是否成功}
    K -->|失败| L[客户再次付款]
    K -->|成功| M[微信支付通知小B云运力]
    M --> N[小B云运力向运力资源询价]
    N --> O[运力资源返回询价结果]
    O --> P[小B云运力确认最便宜运力资源]
    P --> Q[向运力资源下单]
    Q --> R[运力资源派出快递员取件]
    R --> S[快递员判断是否禁运物品]
    S -->|是| T[通知运力资源-取消订单-生成退款单-通知用户]
    S -->|否| U[判断重量和体积是否符合]
    U -->|符合| V[取件完成]
    U -->|不符| W[通知运力资源-计算更新运价]
    W --> X[运力资源通知小B云运力]
    X --> Y[小B云运力更新订单信息-记录变更]
    Y --> Z{金额是增加还是减少}
    Z -->|减少| AA[生成退款单-退款操作-返回结果给运力资源]
    Z -->|增加| AB[生成新的支付单-用户支付成功后通知运力资源更新订单状态]
```
### 3.4.1 订单信息记录

1. **订单基本信息**：记录订单编号、创建时间、状态等。
2. **地址与收发人信息**：详细记录发货地、收货地及对应联系人信息。
3. **运力资源关联**：与实际承担运输任务的运力资源绑定，记录其ID、所属公司等。
4. **支付单和退款单关联**：支持与多笔支付订单和退款订单关联，便于资金流向跟踪。

### 3.4.2 业务流程说明

1. **用户下单**：用户在系统中填写运输需求，包括收发件地址、收发人信息等，系统生成订单，状态为"创建"。
2. **用户支付**：用户完成支付后，订单状态更新为"已支付"，系统通知运力资源进行后续处理。
3. **运力资源确认**：运力资源确认订单后，订单状态更新为"待取件"，等待快递员取件。
4. **快递员取件**：快递员取件完成后，订单状态更新为"运输中"，物品进入运输状态。
5. **运价变更处理**：如果运价增加，系统生成新的支付单，订单状态更新为"部分支付待完成"，用户支付剩余金额后，订单状态更新为"运输中"。
6. **订单取消与退款**：如果用户申请退款或系统生成退款单，订单状态更新为"已取消"，进入退款流程，状态更新为"退款中"，退款完成后，订单状态更新为"已退款"。
7. **订单完成**：物品送达收件人后，订单状态更新为"已完成"，流程结束。

#### 订单状态

| 订单状态 | 数据库保存值            | 说明                                                   |
|----------|-------------------------|--------------------------------------------------------|
| 创建     | CREATED                 | 用户下单成功，系统生成订单，等待用户支付。                |
| 已支付   | PAID                    | 用户已完成支付，系统通知运力资源进行后续处理。           |
| 待接单   | WAIT                    | 寻找最低运价，下单后等待运力资源接单                    |
| 已作废   | CANCELED                | 订单在未支付或支付成功后因某些原因被取消并作废。        |
| 待取件   | PICKUP_PENDING          | 运力资源已确认订单，等待快递员取件。                     |
| 运输中   | IN_TRANSIT              | 快递员已取件，物品在运输途中。                           |
| 已结算   | PARTIAL_PAYMENT_PENDING | 运价增加，用户已完成部分支付，等待支付剩余金额。          |
| 已取消   | CANCELED_AFTER_PAYMENT  | 订单在支付成功后，因用户申请退款或系统生成退款单而取消。 |
| 退款中   | REFUND_PROCESSING       | 系统已生成退款单，等待退款完成。                         |
| 已完成   | COMPLETED               | 物品成功送达收件人，订单流程结束。                       |
| 已退款   | REFUNDED                | 退款已完成，资金已退还给用户。                           |

#### 状态流转说明

1. 创建状态

- **创建**：用户下单成功，系统生成订单，等待用户支付。
   - 用户支付成功后，订单状态更新为"已支付"。
   - 用户或系统取消订单后，订单状态更新为"已作废"。
2. 已支付状态

- **已支付**：用户已完成支付，系统通知运力资源进行后续处理。
   - 运力资源确认订单后，订单状态更新为"待取件"。
   - 用户申请退款或系统生成退款单后，订单状态更新为"已取消"。
3. 待接单
   1. 正在寻找最低价运力，向运力下单后，等待运力接单。
4. 待取件状态

- **待取件**：运力资源已确认订单，等待快递员取件。
   - 快递员取件完成后，订单状态更新为"运输中"。
   - 运价增加，系统生成新的支付单后，订单状态更新为"部分支付待完成"。
   - 用户申请退款或系统生成退款单后，订单状态更新为"已取消"。
5. 运输中状态

- **运输中**：快递员已取件，物品在运输途中。
   - 物品送达收件人后，订单状态更新为"已完成"。
6. 已结算

- 已结算：运价增加，用户已完成部分支付，等待支付剩余金额。
   - 用户支付剩余金额后，订单状态更新为"运输中"。
   - 用户申请退款或系统生成退款单后，订单状态更新为"已取消"。
7. 已取消状态

- **已取消**：订单在支付成功后，因用户申请退款或系统生成退款单而取消。
   - 所有退款均需要进行取消业务流程，订单状态更新为"退款中"。
   - 如果是未收款的订单，可直接作废，订单状态更新为"已作废"。
8. 退款中状态

- **退款中**：系统已生成退款单，等待退款完成。
   - 退款完成后，订单状态更新为"已退款"。
9. 已完成状态

- **已完成**：物品成功送达收件人，订单流程结束。

10. 已作废状态

- **已作废**：订单在未支付或支付成功后因某些原因被取消并作废。

11. 已退款状态

- **已退款**：退款已完成，资金已退还给用户。

12. 在待接单，待取件，已结算，运输中4个状态中，如需退款必须进行拦截订单操作，在拦截成功后才能进行取消订单。
13. 在订单已完成后将不做可以进行取消操作。

#### 订单状态机

```mermaid
stateDiagram-v2
    [*] --> 创建
    创建 --> 已支付 : 用户支付成功
    创建 --> 已作废 : 用户或系统取消订单
    已支付 --> 待接单 : 寻找合适的运力，并向运力下单，等待运力接单
    待接单 --> 待取件 : 运力资源确认订单
    待接单 --> 已取消 : 拦截订单成功
    已支付 --> 已取消 : 用户申请退款,系统生成退款单
    待取件 --> 运输中 : 快递员取件完成
    待取件 --> 已结算 : 运价增加，系统生成新的支付单
    待取件 --> 已取消 : 拦截订单成功
    运输中 --> 已完成 : 物品送达收件人
    运输中 --> 已取消 : 拦截订单成功
    已取消 --> 退款中 : 所以退款均需要进行取消业务流程
    已取消 --> 已作废 : 未收款的订单可作废
    已结算 --> 运输中 : 用户支付剩余金额
    已结算 --> 已取消 : 拦截订单成功
    退款中 --> 已退款 : 退款完成
    已完成 --> [*]
    已作废 --> [*]
    已退款 --> [*]
```
### 3.4.3 资金交易

1. **交易基本信息**：记录交易编号、时间、类型（支付/退款）、状态。
2. **金额详情**：记录交易金额，包括原始金额、实际到账金额、手续费等。
3. **支付方式信息**：如微信支付、支付宝等，记录支付平台相关数据。
4. **关联订单信息**：与对应运输订单绑定，便于业务追溯。
5. **运价策略留存**：记录支付时适用的运价策略基本数据，方便后续对账与分析。

#### 付款订单状态

| 状态名称   | 数据值     | 说明                                                            |
|------------|------------|-----------------------------------------------------------------|
| 支付成功   | SUCCESS    | 支付成功，资金已到账。                                            |
| 转入退款   | REFUND     | 支付成功后，因订单取消或其他原因，资金已退还给用户。               |
| 未支付     | NOTPAY     | 订单已创建，但用户尚未支付。                                      |
| 已关闭     | CLOSED     | 订单已关闭，通常是因为支付超时未完成。                            |
| 已撤销     | REVOKED    | 订单已撤销，仅付款码支付会返回此状态。                            |
| 用户支付中 | USERPAYING | 用户正在支付过程中，仅付款码支付会返回此状态。                    |
| 支付失败   | PAYERROR   | 支付失败，可能是因为网络问题或其他原因，仅付款码支付会返回此状态。 |

#### 付款订单流转图

参考微信支付实现

![image](assets/resources/Dhj4NPMpowo3cMeEyAeto9W0B34TrJibwxrRmWRaVcc.png)

## 3.5 工单与客服

本系统旨在提供 comprehensive 的工单与客服管理功能，确保用户在遇到发货异常（如丢件、破损）时能够及时提交工单并追踪处理进度，同时通过与各物流企业工单系统的对接实现信息同步，利用自动化响应机制提高问题处理效率，并通过客服跟踪记录确保服务质量。

### 3.5.1 业务流程说明

1. **用户提交工单**：用户在遇到发货异常时，通过系统界面提交工单，详细描述问题。
2. **工单系统处理**：系统接收工单后，自动对接物流企业的工单系统，同步工单信息。
3. **物流企业处理**：物流企业的工单系统收到工单后，安排专人处理，并将处理结果反馈回本系统。
4. **更新与通知**：本系统根据反馈更新工单状态，并通知用户处理进度。
5. **自动化响应**：系统实时监测物流状态，当出现异常时，自动触发工单提醒并通知客服介入。
6. **客服处理**：客服人员查看工单详情，联系用户进行处理，并记录处理过程和结果。

### 3.5.2 工单类型

| 工单类型         | 说明                                                                                                                             |
|------------------|----------------------------------------------------------------------------------------------------------------------------------|
| **业务工单**     | 满足客户正常业务需求的工单，如揽收、自取、催投、查签收以及在正常投递时限内客户提出的特殊要求等。                                      |
| **信息工单**     | 客户维护自身权益的投诉工单，如超时限未投递、未亲投导致客户未收到邮件、投递员服务态度差、邮件无下段信息、无进出口、错发错转、要求赔偿等。 |
| **投诉工单**     | 客户对快递服务不满而提交的投诉，如服务态度差、包裹丢失或损坏未得到妥善处理等。                                                      |
| **咨询工单**     | 客户对快递服务、价格、网点等信息的咨询。                                                                                            |
| **售后服务工单** | 客户提出的关于包裹丢失、破损、延误等问题的售后处理请求。                                                                            |
| **支付问题工单** | 客户提交的与支付相关的问题，如订单付款失败、退款问题等。                                                                            |
| **技术问题工单** | 客户遇到的与快递服务相关的技术问题，如快递跟踪系统故障等。                                                                         |

### 3.5.3 工单系统

1. **用户提交工单**：当用户遇到发货异常（如丢件、破损）时，能够在系统内提交工单，记录问题详情。
2. **工单状态追踪**：用户可以实时查看工单的处理进度和状态变化。
3. **工单分类与优先级**：根据问题的严重程度和类型，对工单进行分类和优先级排序，便于客服团队优先处理紧急问题。

### 3.5.4 工单对接

1. **对接物流企业工单系统**：与各物流企业的工单系统进行对接，实现数据互通。
2. **自动同步工单信息**：当用户在本系统提交工单时，系统能够自动将工单信息同步到对应物流企业的工单系统。
3. **反馈处理结果**：物流企业的工单系统处理完成后，能够将处理结果自动反馈回本系统。

### 3.5.5 客服跟踪记录

1. **记录客服活动**：详细记录客服人员与用户的沟通内容、处理措施和时间戳。
2. **工单处理历史**：保存每个工单的完整处理历史，包括状态变化、处理人员、处理时间等信息。
3. **服务质量评估**：基于客服活动记录，评估客服团队的服务质量，为后续培训和优化提供依据。

## 3.6 运力资源管理

运力资源管理是物流业务的核心模块之一，负责整合和管理不同的运力资源，确保快递服务的高效和稳定。本系统通过企业入驻管理、运价配置中心、接口适配体系、权限控制系统和数据监控看板等功能，实现对运力资源的全面管理。

```mermaid
graph TB
    M[运力资源管理模块]-->A[企业入驻管理]
    M-->B[运价配置中心]
    M-->C[接口适配体系]
    M-->D[权限控制系统]
    M-->E[数据监控看板]
```
运力企业资源管理：运力企业的加入均需得到本公司商务浅谈后接入，由后端超管进行信息的录入，包括企业的名称、地址、联系人、合同有效期、轻浮物体积计算比例等基本信息，对于同一个快递品牌会有可能出现多家运力资源，结构如如下：

```mermaid
graph LR
  A[本公司] --> B[运力代理商A]
  A[本公司] --> C[运力代理商C]
  A[本公司] --> D[申通快递]
B --> E[运力代理商B]
E --> D
C --> D
D --> F[物流实体]
```
- **运力资源与品牌的关系**：

参考此图，本公司将会对接3个运力资源，分别为运力代理商A，运力代理商C，申通快递，虽然运力资源是3个，系统对外提供能力时，仅体现"申通快递"一个资源，这就是运力资源和快递品牌的区别。

### 3.6.1 企业入驻管理

1. **信息录入**：由后端超管录入运力企业的基本信息，包括企业名称、地址、联系人、合同有效期、轻浮物体积计算比例等。
2. **资质审核**：对运力企业的资质进行审核，确保其符合合作要求。
3. **合同管理**：管理与运力企业的合同，包括合同的签订、续签、终止等操作。

### 3.6.2 运价配置中心

1. **价格清单管理**：每个运力资源都有自己的价格清单，系统根据该清单确认运力的运价。
2. **零售价更新**：价格清单更新时，同步更新零售价。
3. **变更留痕与认证**：所有价格变更需进行短信认证，并留痕以便追溯。

### 3.6.3 接口适配体系

1. **价格计算适配器**：为同一品牌下的不同运力资源配置价格计算适配器，确保系统对外提供统一的运价能力。
2. **接口更新支持**：适配器设计需考虑后续价格接口的更新需求。

### 3.6.4 权限控制系统

1. **角色管理**：定义不同角色的权限，如运力企业管理员、普通用户等。
2. **访问控制**：控制不同角色对系统功能和数据的访问权限。

## 3.7**定价管理**

参考： [小B云运力价格方案](https://ones.cn/wiki/#/team/HMBf25vo/page/2xoQAZmD)

## 3.8 次卡管理

**电子次卡**是用户通过预付形式获得的快递寄件服务凭证，支持3次/5次/10次的阶梯次数组合选择。该产品采用全数字化管理模式，支持在线购买、核销、监控全流程。

- **使用范围**：
   - **全品牌使用**：适用于所有快递品牌。
   - **指定品牌使用**：仅限于特定快递品牌使用。
- **核销逻辑**
   - 每次寄件成功自动扣除1次
   - 不可与其他营销活动同时使用。
   - 混用场景：优先消耗即将过期的次卡
- **抵扣规则**
   - 次卡仅可抵扣寄件的首重价格，不可用于续重。
   - 每笔订单仅限使用一次次卡。
- **有效期**
   - 默认有效期为30天，支持自定义配置。
- **退款逻辑**
   - 卡券在有效期内若一次未使用可全额退款。
- **节假日应对机制**
   - **暂停使用**：在特殊节假日期间（如运费上涨时），可按日期范围暂停次卡使用。
   - **有效期延长**：暂停结束后，次卡的有效期自动延长，延长时间与暂停时间相同。例如，次卡暂停使用3天，则在原有效期基础上增加3天。
- **地区限制规则**
   - **可配置地区限制**：针对某些地区作为寄件地或到达地时，次卡不可使用。
   - **配置示例**：若设定辽宁和云南为限制地区，则寄件地或到达地为辽宁、云南的订单无法使用次卡。
### 3.8.1 业务规则

1. **购买规则**：用户可以在线购买多种次数组合的电子次卡，支付成功后次卡立即发放到用户账户。
2. **核销规则**：每次寄件成功自动扣除一次，系统记录核销时间和订单信息。
3. **使用范围规则**：根据配置，次卡可以适用于所有快递品牌或仅限特定品牌。
4. **互斥使用规则**：电子次卡不可与其他营销活动同时使用。
5. **优先消耗规则**：在混用场景下，优先消耗即将过期的次卡。
6. **抵扣规则**：次卡仅可抵扣寄件的首重价格，不可用于续重，每笔订单仅限使用一次次卡。
7. **有效期规则**：默认有效期为30天，支持自定义配置。在特殊节假日期间暂停使用后，次卡的有效期自动延长。
8. **退款规则**：卡券在有效期内若一次未使用可全额退款。
9. **地区限制规则**：根据配置的地区限制，判断寄件地或到达地是否在限制范围内，若在限制范围内则不允许使用次卡。

## 3.9 消息通知

### 3.9.1 短信通知

1. **核心配置变更验证**：在更新核心配置（如价格清单变更）时，系统向超管发送短信验证码，超管需验证通过后才能完成变更操作。
2. **安全性**：通过短信验证码确保核心配置变更操作的安全性，防止未授权操作。

### 3.9.2 小程序通知

1. **物流状态变更通知**：当物流状态发生变更（如发货、揽收、运输中、已送达等）时，系统通过小程序向用户发送状态更新通知。
2. **其他通知**：除物流状态变更外，系统还可以通过小程序发送其他类型的通知，如促销活动提醒、系统维护通知等。
3. **成本效益**：小程序通知无成本，可广泛应用在多个业务场景中，提高用户参与度和体验。

### 3.9.3 业务流程图

```mermaid
graph TB
    A[核心配置变更] --> B[触发短信验证码发送]
    B --> C[超管接收短信验证码]
    C --> D[输入验证码进行验证]
    D --> E[验证通过-完成配置变更]

    F[物流状态变更] --> G[触发小程序通知]
    G --> H[用户接收小程序通知]
    H --> I[用户查看物流详情]

    J[其他通知事件] --> K[触发小程序通知]
    K --> L[用户接收小程序通知]
    L --> M[用户查看相关信息]
```
## 3.10 小程序定制

系统提供一个基础的模板，客户提供小程序的appid以及相关的授权，通过系统功能在模板的基础上进行一定的定制，如：设置小程序的图标，小程序的名称，配置首页的banner。在确认好后自动发布小程序到自己的小程序账户中，完成一个自己的小程序部署工作。

### 3.10.1 模板中心管理

1. **模板上传与存储**：客户可以上传打包后的模板文件，系统进行合理的物理存放。
2. **模板调用**：在可视化配置时，系统能够调用已上传的模板。
3. **版本管理**：保存所有历史版本，以便在出现事故时进行版本回滚操作。

### 3.10.2 微信生态授权对接

1. **授权参数调研**：明确与微信生态对接所需的必要参数和授权信息，并整理成文档。
2. **授权管理**：客户通过系统绑定微信小程序 AppID 并进行授权，系统获取必要的权限以进行小程序的配置和发布。

### 3.10.3 可视化配置系统

1. **基础信息配置**：客户可以在模板基础上设置小程序的图标、名称和首页 Banner 等可配置信息。
2. **实时预览**：配置过程中，客户提供实时预览功能，方便客户查看配置效果。
3. **确认与回退**：客户确认配置后，系统进行发布操作；如客户不满意，可以返回修改。

### 3.10.4 自动化发布引擎

通过miniprogram-ci进行自动发布，参考文档实现： [开发辅助 / miniprogram-ci | 知识库管理](https://ones.cn/wiki/#/team/HMBf25vo/space/4N2zNWnP/page/Gq2RwaQZ)

### 3.10.5 业务流程图

```mermaid
graph LR
  A[用户登录SaaS平台] -->  C[绑定微信小程序AppID并授权]
  C --> D[配置基础信息:图标/名称/Banner]
  D --> E[实时预览效果]
  E --> F{确认配置}
  F --> |是| G[自动化发布至微信平台]
  F --> |否| D
```
## 3.11 数据报表

### 3.11.1 订单报表

- **订单概览报表**：展示订单总数、已完成订单数、待处理订单数、已取消订单数等。
- **订单状态分布报表**：按订单状态（创建、已支付、待取件、运输中、已完成、已取消、已作废、退款中、已退款）分类统计订单数量和比例。
- **订单趋势分析报表**：按日、周、月、季、年展示订单数量和金额的变化趋势。

### 3.11.2 运力资源报表

- **运力资源概览报表**：展示已接入的运力资源总数、各运力资源的订单处理量、活跃度等。
- **运力资源订单分配报表**：展示每个运力资源接收到的订单数量和分配情况。

### 3.11.3 支付与结算报表

- **支付概览报表**：展示支付订单总数、支付成功数、支付失败数、支付金额等。
- **支付趋势分析报表**：按日、周、月、季、年展示支付金额和订单数量的变化趋势。
- **支付方式分布报表**：分析不同支付方式（微信支付、支付宝等）的使用情况和占比。
- **退款报表**：展示退款订单数、退款金额、退款原因等。
- **资金流水报表**：记录所有支付和退款的交易流水，包括交易时间、金额、支付方式、订单编号等。

### 3.11.4 工单与客服报表

- **工单概览报表**：展示工单总数、已处理工单数、未处理工单数、工单类型分布等。
- **工单处理效率报表**：分析工单的平均处理时间、处理率、解决率等。
- **客服绩效报表**：评估客服人员的工作绩效，包括响应时间、解决问题的数量等。
- **客户满意度报表**：收集和分析客户对工单处理的满意度评价。

### 3.11.5 电子次卡管理报表

- **次卡销售报表**：展示次卡的销售数量、销售金额、不同次数组合的销售情况等。
- **次卡使用报表**：分析次卡的使用情况，包括使用次数、剩余次数、使用频率等。
- **次卡有效期报表**：监控次卡的有效期，展示即将过期的次卡数量和占比。
- **次卡退款报表**：记录次卡的退款情况，包括退款数量、退款金额等。

### 3.11.6 消息通知报表

- **通知发送报表**：展示短信和小程序通知的发送数量、发送成功率等。
- **通知阅读报表**：分析小程序通知的阅读情况，包括阅读率、未读数量等。

### 3.11.7 系统性能报表

- **系统负载报表**：监控系统的服务器负载、响应时间、吞吐量等性能指标。
- **错误与异常报表**：记录系统运行中的错误和异常情况，包括错误类型、发生时间等。
- **API 调用报表**：展示系统中各个 API 的调用情况，包括调用次数、成功率等。

## 3.12 **配置中心**

小程序证书密钥配置

单日积分获取阈值，产生积分的间隔频率。

广告单日收益阈值，请求广告的间隔频率。

物流状态字典，状态值，是否发送退出。

短文案数据

物品类型字典

批量处理最多数据量阈值

## 3.13 文章管理

### 3.13.1 文章管理

文章管理模块允许管理员编辑和管理静态文章，如用户协议、计费规则等，并提供链接地址供应用使用。同时，需要考虑跨域问题，确保文章内容能够被其他应用正确访问。

#### 核心功能

1. **文章编辑**：支持富文本编辑器，方便管理员创建和编辑文章内容。
2. **跨域支持**：配置 CORS（跨域资源共享）头，解决跨域问题，确保其他应用能够正确访问文章内容。
3. **状态管理**：支持文章的发布、下架等状态管理，控制文章的可见性。

#### 技术实现

- 使用富文本编辑器提供直观的编辑体验。
- 文章编辑后提供URI地址供前端使用，也可返回文章内容。

### 3.13.2 公告管理

公告管理模块用于在界面中滚动显示公告信息，支持纯文字内容。公告可以用于向用户传达重要信息，如系统维护、促销活动等。

#### 核心功能

1. **公告创建**：管理员可以创建新的公告，设置公告内容和显示时间。
2. **公告编辑**：支持对已创建公告的编辑和更新。
3. **公告显示**：在界面中以滚动方式显示公告内容，支持多条公告循环显示。
4. **时间控制**：设置公告的开始时间和结束时间，控制公告的显示周期。
5. **优先级设置**：支持公告优先级设置，确保重要公告优先显示。

#### 技术实现

- 使用简单的文本编辑框供管理员输入公告内容。
- 将公告内容存储在数据库中，包括开始时间、结束时间、优先级等字段。
- 在前端通过 JavaScript 实现滚动显示效果，支持多条公告的循环播放。
- 根据公告的优先级和时间设置，动态控制公告的显示顺序和可见性。

## 3.14 系统管理能力

菜单管理

权限管理

用户管理

## 3.15 代理商接口对接

**分佣：** 使用已有系统实现。

需要给代理商接口送成本价和分佣价两个内容，

## 3.16 数据看板

订单数量，计佣订单数量，已入账订单数，待入账订单数，订单收入，计佣订单收入，已入账订单收入，待入账订单收入，总客户数，新增客户数，今日活跃用户数，今日下单用户数

# 四、技术架构与实现方案

## 4.1 架构设计

- **前端** ： Uniapp（小程序+未来APP） + PC管理端（Vue）。
- **后端** ：单体多模块： 订单服务、支付服务、物流API网关、消息中心、工单服务。
- **数据库** ：主库（MySQL）+ 物流日志（MongoDB时序数据）+ 行为数据（ClickHouse列式数据库）。

## 关键接口需求

- 物流公司API对接（韵达、中通等）。
- 电商平台开放平台（抖店、拼多多、小红书等）。
- 第三方支付（微信/支付宝企业支付接口）。

## 非功能性需求

- **性能** ：支持日并发 50000单，批量导入千条数据响应<3秒。
- **安全** ：敏感数据加密（如支付信息）、物流接口鉴权（HTTPS+Token）。
- **兼容性** ：Excel模板适配WPS/Office，浏览器支持Chrome/Firefox。

# 五、**风险与应对**

## 资金风险

### **黑产刷广告补贴**

用户IP+设备指纹风控，单个设备每日补贴不可超上限。获取频率不得低于阈值。

### **物流报价配置错误**

 新物流公司报价上线前，灰度验证。

### **资金池监管风险**

 预存款功能仅在APP端开发，需提前法务审核资质（单用途商业预付卡）。

### 发单为首重，在价格变更后，不进行后续增加运费的支付。

- 在续重价格的支付要做12小时内完成，超时系统进行拦截东西。
- 对普通用户在下单时限制每天下单的件数，后端可配置；对企业用户的每日最高下单价进行限制，后端可配置。
- 在用户有待支付金额时，禁止用户下单。



# 六、数据保护制度与措施

为保障"小B云运力"平台用户数据的安全与隐私，我们承诺遵守相关法律法规（如《中华人民共和国网络安全法》、《数据安全法》、《个人信息保护法》），并建立以下数据保护制度与技术措施。

## 6.1 数据处理设施

本平台所有服务及数据均托管于**阿里云**。
- **计算服务**：使用阿里云弹性计算服务（ECS）作为应用服务器。
- **数据库服务**：使用阿里云关系型数据库（RDS）进行核心业务数据的存储。
- **物理与环境安全**：依托阿里云提供的高标准数据中心，具备严格的物理访问控制、冗余供电、环境监控等措施，从基础设施层面保障服务的连续性和数据的物理安全。

## 6.2 技术安全措施

我们采取多层次、全方位的技术手段保护数据安全。

### 6.2.1 数据加密
- **传输加密**：客户端（小程序、Web管理后台）与服务器之间的所有数据通信，以及系统内部服务间的调用，均强制使用TLS 1.2及以上版本的加密协议（HTTPS），确保数据在传输过程中不被窃取或篡改。
- **静态加密**：存储在阿里云RDS中的所有用户数据，特别是个人身份信息（PII）、地址、联系方式等敏感信息，均启用数据库的透明数据加密（TDE）功能，实现静态加密存储。

### 6.2.2 访问控制
- **最小权限原则**：内部员工（包括开发、运营、客服）根据其工作职责授予系统和数据的最小必要访问权限。权限集将定期审计，及时回收不必要的权限。
- **身份认证与授权**：访问生产环境的后台系统、数据库等核心资源，必须通过严格的身份认证（如多因素认证MFA）。所有访问和操作行为均被详细记录，以备审计。
- **角色权限分离**：系统已定义超管、运营、财务、客服等不同角色（见2.1.2节），各角色拥有独立的、与其职责匹配的操作权限，防止权限滥用。

### 6.2.3 网络安全
- **网络隔离**：所有后端服务均部署在阿里云虚拟私有云（VPC）内，通过逻辑隔离保障网络环境的独立与安全。
- **防火墙策略**：通过配置阿里云安全组策略，严格限制服务器的端口访问，仅开放必要的服务端口，阻止未授权的网络访问。
- **Web应用防火墙（WAF）**：部署阿里云WAF服务，有效防护SQL注入、跨站脚本（XSS）、CC攻击等常见的Web应用层攻击。

### 6.2.4 日志与监控
- **操作日志**：对所有涉及敏感数据的操作（增、删、改、查）以及对核心系统的访问行为，均记录详细的审计日志。
- **安全监控**：利用阿里云安全中心等服务，对系统进行7x24小时的自动化安全监控，及时发现和告警潜在的安全威胁、漏洞和异常行为。

### 6.2.5 数据备份与恢复
- **自动备份**：利用阿里云RDS的自动备份功能，每日对数据库进行全量备份，并保留多天的备份记录。所有备份数据同样进行加密存储。
- **灾难恢复**：定期进行数据恢复演练，确保在发生数据损坏或丢失等意外情况时，能够迅速、有效地恢复数据，保障业务连续性。

## 6.3 停止合作后的数据处理方式

我们明确规定了在服务终止后对用户数据的处理流程，确保数据得到妥善处置。

- **数据删除期限**：当企业客户或终端用户注销账户、终止服务协议后，我们将在**90天**的宽限期后，启动数据清除流程。
- **处理方式**：
    - **个人身份信息（PII）**：对于可直接识别个人身份的信息（如姓名、电话、身份证号、详细地址），将从生产数据库中进行**永久性删除**，使其不可恢复。
    - **匿名化处理**：对于部分业务数据（如不含个人信息的订单记录），在剥离所有个人关联信息后，可进行**匿名化处理**，用于后续的统计分析。匿名化后的数据将无法追溯到任何个人主体。
- **法律遵从**：若相关法律法规（如会计法、电子商务法）要求对特定数据（如交易记录）保留更长时间，我们将依法在规定的时限内进行归档存储，并在法定期限届满后予以删除。
- **合作伙伴数据**：与运力资源方等合作伙伴终止合作时，将依据双方签署的协议条款，要求对方删除或返还我方共享的数据，并出具相关证明。

## 6.4 合规性标准

本平台的设计与运营将积极对标国内外主流的数据安全与隐私保护标准，如**ISO 27001**（信息安全管理体系）、**GB/T 35273-2020**（个人信息安全规范）等，并致力于持续改进我们的数据保护实践。

# 七、工期计划

| 月度               | 功能模块                                                                      | 开发内容                                                                                                                                          |
|--------------------|-------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------|
| 1个月 （MVP版本）    | 团队磨合与核心功能开发，发件能力，物流跟踪，支付能力，订单查看，定价算法、批量发件、 | 完成团队组建与磨合，搭建开发环境，开发企业用户管理、阶梯运价配置、单独发件、微信支付集成、订单中心基础功能、运力资源管理基础功能、代理商接口对接基础功能。 |
| 2-3个月 （功能完善） | 企业账户管理、次卡管理，运力资源管理、对账，发票开具，信息通知                     | 开发企业账户管理、物流跟踪功能、批量发件功能、文章管理、次卡管理。                                                                                     |
| 4个月 （高级功能）   | 小程序定制，开发票，数据报表                                                    | 开发小程序通知系统、短信通知系统、小程序定制功能。                                                                                                   |
| 5个月 （全面优化）   | 系统优化与收尾                                                                | 进行数据报表完善、系统性能优化、用户界面优化、安全性增强，修复测试中发现的问题，准备上线。                                                              |
