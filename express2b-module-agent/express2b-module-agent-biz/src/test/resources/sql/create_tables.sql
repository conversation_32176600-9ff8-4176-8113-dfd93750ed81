-- 将该建表 SQL 语句，添加到 express2b-module-agent-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "agent_pricing_rule" (
                                                    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                    "agent_id" bigint NOT NULL,
                                                    "markup_type" int NOT NULL,
                                                    "first_weight_markup" int NOT NULL,
                                                    "additional_weight_markup" int NOT NULL,
                                                    "brand_key" varchar,
                                                    "from_adcode" varchar,
                                                    "to_adcode" varchar,
                                                    "status" int NOT NULL,
                                                    "priority" int NOT NULL,
                                                    "creator" varchar DEFAULT '',
                                                    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                    "updater" varchar DEFAULT '',
                                                    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                    "deleted" bit NOT NULL DEFAULT FALSE,
                                                    "tenant_id" bigint NOT NULL DEFAULT 0,
                                                    PRIMARY KEY ("id")
    ) COMMENT '代理商定价（终端零售价）规则表';

-- 将该建表 SQL 语句，添加到 express2b-module-agent-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "pricing_template" (
                                                  "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                  "template_name" varchar NOT NULL,
                                                  "markup_type" int NOT NULL,
                                                  "first_weight_markup" int NOT NULL,
                                                  "additional_weight_markup" int NOT NULL,
                                                  "platform_first_price" int NOT NULL,
                                                  "platform_additional_price" int NOT NULL,
                                                  "status" int NOT NULL,
                                                  "description" varchar,
                                                  "creator" varchar DEFAULT '',
                                                  "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                  "updater" varchar DEFAULT '',
                                                  "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                  "deleted" bit NOT NULL DEFAULT FALSE,
                                                  "tenant_id" bigint NOT NULL DEFAULT 0,
                                                  PRIMARY KEY ("id")
    ) COMMENT '定价模板表';


-- 将该建表 SQL 语句，添加到 express2b-module-agent-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "agent_level_config" (
                                                    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                    "level_name" varchar NOT NULL,
                                                    "default_platform_pricing_template_id" bigint NOT NULL,
                                                    "upgrade_personal_daily_orders" int NOT NULL,
                                                    "upgrade_team_daily_orders" int NOT NULL,
                                                    "upgrade_observation_days" int NOT NULL,
                                                    "creator" varchar DEFAULT '',
                                                    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                    "updater" varchar DEFAULT '',
                                                    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                    "deleted" bit NOT NULL DEFAULT FALSE,
                                                    "tenant_id" bigint NOT NULL DEFAULT 0,
                                                    PRIMARY KEY ("id")
    ) COMMENT '代理商等级配置表';


-- 将该建表 SQL 语句，添加到 express2b-module-agent-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "agent_relationship" (
                                                    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                    "ancestor_id" bigint NOT NULL,
                                                    "descendant_id" bigint NOT NULL,
                                                    "depth" int NOT NULL,
                                                    "creator" varchar DEFAULT '',
                                                    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                    "updater" varchar DEFAULT '',
                                                    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                    "deleted" bit NOT NULL DEFAULT FALSE,
                                                    "tenant_id" bigint NOT NULL DEFAULT 0,
                                                    PRIMARY KEY ("id")
    ) COMMENT '代理商团队关系表';

-- 将该建表 SQL 语句，添加到 express2b-module-agent-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "agent_agent_profile" (
                                                     "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                     "member_id" bigint NOT NULL,
                                                     "name" varchar NOT NULL,
                                                     "mobile" varchar NOT NULL,
                                                     "email" varchar,
                                                     "status" int NOT NULL,
                                                     "level_id" bigint NOT NULL,
                                                     "referrer_agent_id" bigint,
                                                     "custom_pricing_template_id" bigint,
                                                     "wallet_id" varchar NOT NULL,
                                                     "system_tenant_id" bigint NOT NULL,
                                                     "direct_downline_count" int NOT NULL,
                                                     "total_downline_count" int NOT NULL,
                                                     "creator" varchar DEFAULT '',
                                                     "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                     "updater" varchar DEFAULT '',
                                                     "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                     "deleted" bit NOT NULL DEFAULT FALSE,
                                                     "tenant_id" bigint NOT NULL DEFAULT 0,
                                                     PRIMARY KEY ("id")
    ) COMMENT '代理商档案表';

-- 将该建表 SQL 语句，添加到 express2b-module-agent-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "agent_custom_template_request" (
                                                               "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                               "agent_id" bigint NOT NULL,
                                                               "requested_template_details" varchar NOT NULL,
                                                               "status" int NOT NULL,
                                                               "approved_template_id" bigint,
                                                               "requested_at" varchar NOT NULL,
                                                               "reviewed_at" varchar,
                                                               "reviewer_comments" varchar,
                                                               "remarks" varchar NOT NULL,
                                                               "creator" varchar DEFAULT '',
                                                               "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                               "updater" varchar DEFAULT '',
                                                               "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                               "deleted" bit NOT NULL DEFAULT FALSE,
                                                               "tenant_id" bigint NOT NULL DEFAULT 0,
                                                               PRIMARY KEY ("id")
    ) COMMENT '代理商自定义价格模板申请记录表';

-- 将该建表 SQL 语句，添加到 express2b-module-pricing-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "agent_selected_pricing_template" (
                                                                 "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                                 "superior_agent_id" bigint NOT NULL,
                                                                 "downline_agent_id" bigint NOT NULL,
                                                                 "pricing_template_id" bigint NOT NULL,
                                                                 "creator" varchar DEFAULT '',
                                                                 "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                                 "updater" varchar DEFAULT '',
                                                                 "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                                 "deleted" bit NOT NULL DEFAULT FALSE,
                                                                 "tenant_id" bigint NOT NULL DEFAULT 0,
                                                                 PRIMARY KEY ("id")
    ) COMMENT '代理商为下级选择的价格模板记录表';

-- 将该建表 SQL 语句，添加到 express2b-module-agent-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "agent_daily_performance_stat" (
                                                              "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                              "agent_id" bigint NOT NULL,
                                                              "stat_date" varchar NOT NULL,
                                                              "personal_order_count" int NOT NULL,
                                                              "team_order_count" int NOT NULL,
                                                              "personal_order_amount" int NOT NULL,
                                                              "team_order_amount" int NOT NULL,
                                                              "personal_profit_amount" int NOT NULL,
                                                              "team_profit_amount" int NOT NULL,
                                                              "commission_income_amount" int NOT NULL,
                                                              "bad_debt_count" int NOT NULL,
                                                              "bad_debt_amount" int NOT NULL,
                                                              "coupon_deduct_amount" int NOT NULL,
                                                              "subsidy_amount" int NOT NULL,
                                                              "withdrawal_amount" int NOT NULL,
                                                              "new_customer_count" int NOT NULL,
                                                              "active_customer_count" int NOT NULL,
                                                              "repeat_customer_count" int NOT NULL,
                                                              "creator" varchar DEFAULT '',
                                                              "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                              "updater" varchar DEFAULT '',
                                                              "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                              "deleted" bit NOT NULL DEFAULT FALSE,
                                                              "tenant_id" bigint NOT NULL DEFAULT 0,
                                                              PRIMARY KEY ("id")
    ) COMMENT '代理商日业绩统计表';
CREATE TABLE IF NOT EXISTS "agent_page_template" (
                                                     "id" int NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                     "module_name" varchar NOT NULL,
                                                     "component_type" varchar,
                                                     "display_name" varchar,
                                                     "state" int,
                                                     "dom" varchar NOT NULL,
                                                     "parent_dom" varchar,
                                                     "sub_module_id" varchar,
                                                     "sort_order" int,
                                                     "creator" varchar DEFAULT '',
                                                     "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                     "updater" varchar DEFAULT '',
                                                     "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                     "deleted" bit NOT NULL DEFAULT FALSE,
                                                     "tenant_id" bigint NOT NULL DEFAULT 0,
                                                     PRIMARY KEY ("id")
    ) COMMENT '页面模板';


