package com.yunyi.express2b.module.express.dal.mysql.invoice;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;

import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorderrelation.InvoiceOrderRelationPageDTO;
import com.yunyi.express2b.module.express.dal.dataobject.invoice.InvoiceOrderRelationDO;
import org.apache.ibatis.annotations.Mapper;


/**
 * 发票订单关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InvoiceOrderRelationMapper extends BaseMapperX<InvoiceOrderRelationDO> {

    default PageResult<InvoiceOrderRelationDO> selectPage(InvoiceOrderRelationPageDTO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InvoiceOrderRelationDO>()
                .eqIfPresent(InvoiceOrderRelationDO::getInvoiceId, reqVO.getInvoiceId())
                .eqIfPresent(InvoiceOrderRelationDO::getOrderNo, reqVO.getOrderNo())
                .eqIfPresent(InvoiceOrderRelationDO::getOrderAmount, reqVO.getOrderAmount())
                .betweenIfPresent(InvoiceOrderRelationDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InvoiceOrderRelationDO::getId));
    }

}