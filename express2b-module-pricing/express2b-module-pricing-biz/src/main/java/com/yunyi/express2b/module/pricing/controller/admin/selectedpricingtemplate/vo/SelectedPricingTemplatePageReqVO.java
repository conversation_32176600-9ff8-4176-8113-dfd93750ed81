package com.yunyi.express2b.module.pricing.controller.admin.selectedpricingtemplate.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 代理商为下级选择的价格模板记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SelectedPricingTemplatePageReqVO extends PageParam {

    @Schema(description = "操作选择的上级代理商ID", example = "18109")
    private Long superiorAgentId;

    @Schema(description = "被指定模板的下级代理商ID", example = "10787")
    private Long downlineAgentId;

    @Schema(description = "选择的价格模板ID", example = "9485")
    private Long pricingTemplateId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}