package com.yunyi.express2b.module.pricing.dal.dataobject.selectedpricingtemplate;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 代理商为下级选择的价格模板记录 DO
 *
 * <AUTHOR>
 */
@TableName("agent_selected_pricing_template")
@KeySequence("agent_selected_pricing_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SelectedPricingTemplateDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 操作选择的上级代理商ID
     */
    private Long superiorAgentId;
    /**
     * 被指定模板的下级代理商ID
     */
    private Long downlineAgentId;
    /**
     * 选择的价格模板ID
     */
    private Long pricingTemplateId;

}