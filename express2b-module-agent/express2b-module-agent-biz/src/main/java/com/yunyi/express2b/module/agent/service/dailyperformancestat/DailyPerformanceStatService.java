package com.yunyi.express2b.module.agent.service.dailyperformancestat;

import java.util.*;
import jakarta.validation.*;
import com.yunyi.express2b.module.agent.controller.admin.dailyperformancestat.vo.*;
import com.yunyi.express2b.module.agent.dal.dataobject.dailyperformancestat.DailyPerformanceStatDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;

/**
 * 代理商日业绩统计 Service 接口
 *
 * <AUTHOR>
 */
public interface DailyPerformanceStatService {

    /**
     * 创建代理商日业绩统计
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDailyPerformanceStat(@Valid DailyPerformanceStatSaveReqVO createReqVO);

    /**
     * 更新代理商日业绩统计
     *
     * @param updateReqVO 更新信息
     */
    void updateDailyPerformanceStat(@Valid DailyPerformanceStatSaveReqVO updateReqVO);

    /**
     * 删除代理商日业绩统计
     *
     * @param id 编号
     */
    void deleteDailyPerformanceStat(Long id);

    /**
     * 获得代理商日业绩统计
     *
     * @param id 编号
     * @return 代理商日业绩统计
     */
    DailyPerformanceStatDO getDailyPerformanceStat(Long id);

    /**
     * 获得代理商日业绩统计分页
     *
     * @param pageReqVO 分页查询
     * @return 代理商日业绩统计分页
     */
    PageResult<DailyPerformanceStatDO> getDailyPerformanceStatPage(DailyPerformanceStatPageReqVO pageReqVO);

}