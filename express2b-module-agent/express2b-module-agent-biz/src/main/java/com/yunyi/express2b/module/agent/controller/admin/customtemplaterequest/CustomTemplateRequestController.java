package com.yunyi.express2b.module.agent.controller.admin.customtemplaterequest;

import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;
import com.yunyi.express2b.module.agent.controller.admin.customtemplaterequest.vo.CustomTemplateRequestPageReqVO;
import com.yunyi.express2b.module.agent.controller.admin.customtemplaterequest.vo.CustomTemplateRequestRespVO;
import com.yunyi.express2b.module.agent.controller.admin.customtemplaterequest.vo.CustomTemplateRequestSaveReqVO;
import com.yunyi.express2b.module.agent.dal.dataobject.customtemplaterequest.CustomTemplateRequestDO;
import com.yunyi.express2b.module.agent.service.customtemplaterequest.CustomTemplateRequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 代理商自定义价格模板申请记录")
@RestController
@RequestMapping("/agent/custom-template-request")
@Validated
public class CustomTemplateRequestController {

    @Resource
    private CustomTemplateRequestService customTemplateRequestService;

    @PostMapping("/create")
    @Operation(summary = "创建代理商自定义价格模板申请记录")
    @PreAuthorize("@ss.hasPermission('agent:custom-template-request:create')")
    public CommonResult<Long> createCustomTemplateRequest(@Valid @RequestBody CustomTemplateRequestSaveReqVO createReqVO) {
        return success(customTemplateRequestService.createCustomTemplateRequest(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代理商自定义价格模板申请记录")
    @PreAuthorize("@ss.hasPermission('agent:custom-template-request:update')")
    public CommonResult<Boolean> updateCustomTemplateRequest(@Valid @RequestBody CustomTemplateRequestSaveReqVO updateReqVO) {
        customTemplateRequestService.updateCustomTemplateRequest(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代理商自定义价格模板申请记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:custom-template-request:delete')")
    public CommonResult<Boolean> deleteCustomTemplateRequest(@RequestParam("id") Long id) {
        customTemplateRequestService.deleteCustomTemplateRequest(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代理商自定义价格模板申请记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:custom-template-request:query')")
    public CommonResult<CustomTemplateRequestRespVO> getCustomTemplateRequest(@RequestParam("id") Long id) {
        CustomTemplateRequestDO customTemplateRequest = customTemplateRequestService.getCustomTemplateRequest(id);
        return success(BeanUtils.toBean(customTemplateRequest, CustomTemplateRequestRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代理商自定义价格模板申请记录分页")
    @PreAuthorize("@ss.hasPermission('agent:custom-template-request:query')")
    public CommonResult<PageResult<CustomTemplateRequestRespVO>> getCustomTemplateRequestPage(@Valid CustomTemplateRequestPageReqVO pageReqVO) {
        PageResult<CustomTemplateRequestDO> pageResult = customTemplateRequestService.getCustomTemplateRequestPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CustomTemplateRequestRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代理商自定义价格模板申请记录 Excel")
    @PreAuthorize("@ss.hasPermission('agent:custom-template-request:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCustomTemplateRequestExcel(@Valid CustomTemplateRequestPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CustomTemplateRequestDO> list = customTemplateRequestService.getCustomTemplateRequestPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代理商自定义价格模板申请记录.xls", "数据", CustomTemplateRequestRespVO.class,
                        BeanUtils.toBean(list, CustomTemplateRequestRespVO.class));
    }


    /**
     * 插入自定义价格模版
     * @param templateSaveReqVO
     * @return
     */
    @PostMapping("/v1/agents/pricings/custom-templates/request")
    @Operation(summary = "插入自定义定价模板表")
    @PreAuthorize("\"@ss.hasPermission('agent:custom-template-request:create')")
    public CommonResult<Long> createTemplateSave(@Valid @RequestBody CustomTemplateRequestRespVO  templateSaveReqVO) {
        return success(customTemplateRequestService.insertTemplateSave(templateSaveReqVO));
    }

    /**
     * 传递自定义模板ID和修改后的状态 建立数据库联系
     * @param id 是自定义模版的ID
     * @param status
     * @return
     */
    @PutMapping("/update/anget-template-request")
    @Operation(summary = "更新代理商自定义模板数据")
    @PreAuthorize("\"@ss.hasPermission('agent:custom-template-request:update')")
    public CommonResult<String> updateTemplate(@RequestParam(value = "id") Long id,@RequestParam(value = "status") Long status){
        return customTemplateRequestService.updateTemplate(id, status) ? success("更新成功") : success("更新失败");
    }
    /**
     * 根据代理商ID获取代理商自定义申请表的申请状态
     * @param agentId
     * @return
     * 记得没有权限的加上权限 权限在云效中找,找到之后再进行数据使用(后面拼接方法名字)
     */
    @GetMapping("/v1/agents/pricings/custom-templates/requests")
    @Operation(summary = "回显代理商定价模版状态数据")
    @PreAuthorize("\"@ss.hasPermission('agent:custom-template-request:query')")
    public CommonResult<List<CustomTemplateRequestRespVO>> getTemplateById(@RequestParam(value = "agentId") Long agentId){
        return success(customTemplateRequestService.getQueryEcho(agentId));
    }
}
