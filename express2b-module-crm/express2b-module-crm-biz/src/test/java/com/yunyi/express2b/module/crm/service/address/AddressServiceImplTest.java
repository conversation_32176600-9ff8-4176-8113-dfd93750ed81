package com.yunyi.express2b.module.crm.service.address;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.security.core.util.CrmSecurityFrameworkUtils;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.module.crm.controller.admin.address.vo.AddressPageReqVO;
import com.yunyi.express2b.module.crm.controller.admin.address.vo.AddressRespVO;
import com.yunyi.express2b.module.crm.controller.admin.address.vo.AddressSaveReqVO;
import com.yunyi.express2b.module.crm.controller.app.v1.personal.address.vo.AddressDefaultReqVO;
import com.yunyi.express2b.module.crm.controller.app.v1.personal.address.vo.AddressFuzzyQueryVO;
import com.yunyi.express2b.module.crm.controller.app.v1.personal.address.vo.AddressUploadReqVO;
import com.yunyi.express2b.module.crm.dal.dataobject.address.AddressDO;
import com.yunyi.express2b.module.crm.dal.mysql.address.AddressMapper;
import com.yunyi.express2b.module.crm.enums.AddressDefaultEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import java.util.Arrays;
import java.util.List;

import static com.yunyi.express2b.framework.common.exception.enums.GlobalErrorCodeConstants.FORBIDDEN;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertPojoEquals;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertServiceException;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomLongId;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomPojo;
import static com.yunyi.express2b.module.crm.enums.ErrorCodeConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * {@link AddressServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Slf4j
@Import(AddressServiceImpl.class)
public class AddressServiceImplTest extends BaseDbUnitTest {

    @Resource
    private AddressServiceImpl addressService;

    @Resource
    private AddressMapper addressMapper;

    @Test
    public void testCreateAddress_success() {
        // 准备参数
        AddressSaveReqVO createReqVO = randomPojo(AddressSaveReqVO.class).setId(null);

        // 调用
        Long addressId = addressService.createAddress(createReqVO);
        // 断言
        assertNotNull(addressId);
        // 校验记录的属性是否正确
        AddressDO address = addressMapper.selectById(addressId);
        assertPojoEquals(createReqVO, address, "id");
    }

    @Test
    public void testUpdateAddress_success() {
        // mock 数据
        AddressDO dbAddress = randomPojo(AddressDO.class);
        addressMapper.insert(dbAddress);// @Sql: 先插入出一条存在的数据
        // 准备参数
        AddressUploadReqVO updateReqVO = randomPojo(AddressUploadReqVO.class, o -> {
            o.setId(dbAddress.getId()); // 设置更新的 ID
        });

        // 调用
        addressService.appupdateAddress(updateReqVO);
        // 校验是否更新正确
        AddressDO address = addressMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, address);
    }

    @Test
    public void testUpdateAddress_notExists() {
        // 准备参数
        AddressUploadReqVO updateReqVO = randomPojo(AddressUploadReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> addressService.appupdateAddress(updateReqVO), ADDRESS_NOT_EXISTS);
    }

    @Test
    public void testDeleteAddress_success() {
        // mock 数据
        AddressDO dbAddress = randomPojo(AddressDO.class);
        addressMapper.insert(dbAddress);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbAddress.getId();

        // 调用
        addressService.deleteAddress(id);
        // 校验数据不存在了
        assertNull(addressMapper.selectById(id));
    }

    @Test
    public void testDeleteAddress_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> addressService.deleteAddress(id), ADDRESS_NOT_EXISTS);
    }

    @Test
    public void testDelAddressByIdsAndMember_success() {
        // 模拟登录用户
        Long userId = 1L;

        // mock 数据
        AddressDO dbAddress1 = randomPojo(AddressDO.class);
        dbAddress1.setMemberId(userId);
        addressMapper.insert(dbAddress1);

        AddressDO dbAddress2 = randomPojo(AddressDO.class);
        dbAddress2.setMemberId(userId);
        addressMapper.insert(dbAddress2);

        // 准备参数
        List<Long> ids = Arrays.asList(dbAddress1.getId(), dbAddress2.getId());

        // 调用
        Integer result = addressService.delAddressByIdsAndMember(ids, userId);

        // 校验数据不存在了
        assertEquals(2, result); // 应该删除了2条记录
        assertNull(addressMapper.selectById(dbAddress1.getId()));
        assertNull(addressMapper.selectById(dbAddress2.getId()));
    }

    @Test
    public void testDelAddressByIdsAndMember_emptyIds() {
        // 准备参数
        Long userId = 1L;
        List<Long> ids = List.of();

        // 调用
        Integer result = addressService.delAddressByIdsAndMember(ids, userId);

        // 验证结果 - 应该返回0，因为没有要删除的记录
        assertEquals(0, result);
    }

    @Test
    public void testDelAddressByIdsAndMember_nonExistingIds() {
        // 模拟登录用户
        Long userId = 1L;

        // 准备不存在的ID
        Long nonExistingId1 = 9999L;
        Long nonExistingId2 = 10000L;
        List<Long> ids = Arrays.asList(nonExistingId1, nonExistingId2);

        // 调用
        Integer result = addressService.delAddressByIdsAndMember(ids, userId);

        // 验证结果 - 应该返回0，表示没有删除任何记录
        assertEquals(0, result);
    }

    @Test
    public void testDelAddressByIdsAndMember_otherUserAddress() {
        // 模拟登录用户和其他用户
        Long userId = 1L;
        Long otherUserId = 2L;

        // mock 数据 - 创建其他用户的地址
        AddressDO otherUserAddress = randomPojo(AddressDO.class);
        otherUserAddress.setMemberId(otherUserId);
        addressMapper.insert(otherUserAddress);

        // 准备参数 - 尝试删除其他用户的地址
        List<Long> ids = List.of(otherUserAddress.getId());

        // 调用
        Integer result = addressService.delAddressByIdsAndMember(ids, userId);

        // 验证结果 - 应该返回0，表示没有删除任何记录
        assertEquals(0, result);

        // 验证其他用户的地址仍然存在
        assertNotNull(addressMapper.selectById(otherUserAddress.getId()));
    }

    @Test
    public void testDelAddressByIdsAndMember_mixedIds() {
        // 模拟登录用户和其他用户
        Long userId = 1L;
        Long otherUserId = 2L;

        // mock 数据 - 创建当前用户的地址
        AddressDO userAddress1 = randomPojo(AddressDO.class);
        userAddress1.setMemberId(userId);
        addressMapper.insert(userAddress1);

        AddressDO userAddress2 = randomPojo(AddressDO.class);
        userAddress2.setMemberId(userId);
        addressMapper.insert(userAddress2);

        // mock 数据 - 创建其他用户的地址
        AddressDO otherUserAddress = randomPojo(AddressDO.class);
        otherUserAddress.setMemberId(otherUserId);
        addressMapper.insert(otherUserAddress);

        // 准备不存在的ID
        Long nonExistingId = 9999L;

        // 准备参数 - 混合当前用户地址、其他用户地址和不存在的地址
        List<Long> ids = Arrays.asList(
                userAddress1.getId(),      // 应该被删除
                userAddress2.getId(),      // 应该被删除
                otherUserAddress.getId(),  // 不应该被删除
                nonExistingId              // 不存在，无法删除
        );

        // 调用
        Integer result = addressService.delAddressByIdsAndMember(ids, userId);

        // 验证结果 - 应该只删除当前用户的地址，返回删除的记录数
        assertEquals(2, result);

        // 验证当前用户的地址已被删除
        assertNull(addressMapper.selectById(userAddress1.getId()));
        assertNull(addressMapper.selectById(userAddress2.getId()));

        // 验证其他用户的地址仍然存在
        assertNotNull(addressMapper.selectById(otherUserAddress.getId()));
    }

    @Test
    public void testDelAddressByIdsAndMember_singleRecord() {
        // 模拟登录用户
        Long userId = 1L;

        // mock 数据
        AddressDO address = randomPojo(AddressDO.class);
        address.setMemberId(userId);
        addressMapper.insert(address);

        // 准备参数
        List<Long> ids = List.of(address.getId());

        // 调用
        Integer result = addressService.delAddressByIdsAndMember(ids, userId);

        // 验证结果
        assertEquals(1, result); // 应该删除了1条记录
        assertNull(addressMapper.selectById(address.getId()));
    }

    @Test
    public void testDelAddressByIdsAndMember_defaultAddress() {
        // 模拟登录用户
        Long userId = 1L;

        // mock 数据 - 创建一个默认地址
        AddressDO defaultAddress = randomPojo(AddressDO.class);
        defaultAddress.setMemberId(userId);
        defaultAddress.setIsDefault(AddressDefaultEnum.DEFAULT.getCode());
        addressMapper.insert(defaultAddress);

        // 准备参数
        List<Long> ids = List.of(defaultAddress.getId());

        // 调用
        Integer result = addressService.delAddressByIdsAndMember(ids, userId);

        // 验证结果 - 默认地址应该可以被删除
        assertEquals(1, result);
        assertNull(addressMapper.selectById(defaultAddress.getId()));
    }

    @Test
    public void testCreateAddressBatch_success() {
        // 模拟登录用户
        Long userId = 1L;

        // 准备参数 - 创建两个地址
        AddressSaveReqVO address1 = randomPojo(AddressSaveReqVO.class).setId(null);
        AddressSaveReqVO address2 = randomPojo(AddressSaveReqVO.class).setId(null);
        List<AddressSaveReqVO> addresses = Arrays.asList(address1, address2);

        // 模拟用户已登录
        AutoCloseable autoCloseable = null;
        try {
            autoCloseable = mockStatic(CrmSecurityFrameworkUtils.class);
            when(CrmSecurityFrameworkUtils.getLoginUserId()).thenReturn(userId);

            // 调用测试方法
            List<AddressRespVO> addressBatch = addressService.createAddressBatch(addresses);

            // 验证结果
            assertTrue(true);

        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        } finally {
            if (autoCloseable != null) {
                try {
                    autoCloseable.close();
                } catch (Exception e) {
                    // 忽略关闭异常
                }
            }
        }
    }

    @Test
    public void testCreateAddressBatch_emptyAdccode() {
        // 准备参数 - 创建一个adccode为空的地址
        AddressSaveReqVO address = randomPojo(AddressSaveReqVO.class)
                .setId(null)
                .setAdccode(""); // 设置空的adccode
        List<AddressSaveReqVO> addresses = List.of(address);

        // 调用并断言异常
        assertServiceException(() -> addressService.createAddressBatch(addresses),
                ADDRESS_ADCCODE_EMPTY);
    }

    @Test
    public void testCreateAddressBatch_emptyMemberId() {
        // 准备参数 - 创建一个memberId为空的地址
        AddressSaveReqVO address = randomPojo(AddressSaveReqVO.class)
                .setId(null)
                .setMemberId(null); // 设置空的memberId
        List<AddressSaveReqVO> addresses = List.of(address);

        // 调用并断言异常
        assertServiceException(() -> addressService.createAddressBatch(addresses),
                ADDRESS_MEMBER_ID_EMPTY);
    }

    //<editor-fold desc="修改默认地址测试">
    @Test
    public void testUpdateDefaultAddress_toDefault() {
        // 模拟登录用户
        Long userId = 1L;
        String addressType = "SENDER"; // 寄件人地址类型

        // mock 数据 - 创建多个地址
        AddressDO address1 = randomPojo(AddressDO.class, o -> {
            o.setMemberId(userId);
            o.setIsDefault(AddressDefaultEnum.DEFAULT.getCode());
        });
        addressMapper.insert(address1);

        AddressDO address2 = randomPojo(AddressDO.class, o -> {
            o.setMemberId(userId);
            o.setIsDefault(AddressDefaultEnum.NOT_DEFAULT.getCode());
        });
        addressMapper.insert(address2);

        AddressDO address3 = randomPojo(AddressDO.class, o -> {
            o.setMemberId(userId);
            o.setIsDefault(AddressDefaultEnum.NOT_DEFAULT.getCode());
        });
        addressMapper.insert(address3);

        // 准备参数 - 将非默认地址设为默认
        AddressDefaultReqVO reqVo = new AddressDefaultReqVO();
        reqVo.setId(address2.getId());
        reqVo.setIsDefault(AddressDefaultEnum.DEFAULT.getCode());
        reqVo.setAddressType(addressType);

        // 模拟用户已登录
        AutoCloseable autoCloseable = null;
        try {
            autoCloseable = mockStatic(CrmSecurityFrameworkUtils.class);
            when(CrmSecurityFrameworkUtils.getLoginUserId()).thenReturn(userId);

            // 调用测试方法
            Boolean result = addressService.updateDefaultAddress(reqVo, userId);

            // 验证结果
            assertTrue(result);

            // 验证数据库中的更新
            AddressDO updatedAddress1 = addressMapper.selectById(address1.getId());
            AddressDO updatedAddress2 = addressMapper.selectById(address2.getId());
            AddressDO updatedAddress3 = addressMapper.selectById(address3.getId());

            assertEquals(AddressDefaultEnum.NOT_DEFAULT.getCode(), updatedAddress1.getIsDefault()); // 原默认地址变为非默认
            assertEquals(AddressDefaultEnum.DEFAULT.getCode(), updatedAddress2.getIsDefault());     // 目标地址变为默认
            assertEquals(AddressDefaultEnum.NOT_DEFAULT.getCode(), updatedAddress3.getIsDefault()); // 其他地址保持非默认

        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        } finally {
            if (autoCloseable != null) {
                try {
                    autoCloseable.close();
                } catch (Exception e) {
                    // 忽略关闭异常
                }
            }
        }
    }

    @Test
    public void testUpdateDefaultAddress_toNotDefault() {
        // 模拟登录用户
        Long userId = 1L;
        String addressType = "RECEIVER"; // 收件人地址类型

        // mock 数据 - 创建多个地址
        AddressDO address1 = randomPojo(AddressDO.class, o -> {
            o.setMemberId(userId);
            o.setIsDefault(AddressDefaultEnum.DEFAULT.getCode());
        });
        addressMapper.insert(address1);

        AddressDO address2 = randomPojo(AddressDO.class, o -> {
            o.setMemberId(userId);
            o.setIsDefault(AddressDefaultEnum.NOT_DEFAULT.getCode());
        });
        addressMapper.insert(address2);

        // 准备参数 - 将默认地址设为非默认
        AddressDefaultReqVO reqVo = new AddressDefaultReqVO();
        reqVo.setId(address1.getId());
        reqVo.setIsDefault(AddressDefaultEnum.NOT_DEFAULT.getCode());
        reqVo.setAddressType(addressType);

        // 模拟用户已登录
        AutoCloseable autoCloseable = null;
        try {
            autoCloseable = mockStatic(CrmSecurityFrameworkUtils.class);
            when(CrmSecurityFrameworkUtils.getLoginUserId()).thenReturn(userId);

            // 调用测试方法
            Boolean result = addressService.updateDefaultAddress(reqVo, userId);

            // 验证结果
            assertTrue(result);

            // 验证数据库中的更新
            AddressDO updatedAddress1 = addressMapper.selectById(address1.getId());
            AddressDO updatedAddress2 = addressMapper.selectById(address2.getId());

            assertEquals(AddressDefaultEnum.NOT_DEFAULT.getCode(), updatedAddress1.getIsDefault()); // 目标地址变为非默认
            assertEquals(AddressDefaultEnum.NOT_DEFAULT.getCode(), updatedAddress2.getIsDefault()); // 其他地址保持不变

        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        } finally {
            if (autoCloseable != null) {
                try {
                    autoCloseable.close();
                } catch (Exception e) {
                    // 忽略关闭异常
                }
            }
        }
    }

    @Test
    public void testUpdateDefaultAddress_forbidden() {
        // 模拟登录用户
        Long userId = 1L;
        Long otherUserId = 2L;
        String addressType = "SENDER";

        // mock 数据 - 创建其他用户的地址
        AddressDO address = randomPojo(AddressDO.class, o -> {
            o.setMemberId(otherUserId); // 设置为其他用户的地址
            o.setIsDefault(AddressDefaultEnum.NOT_DEFAULT.getCode());
        });
        addressMapper.insert(address);

        // 准备参数
        AddressDefaultReqVO reqVo = new AddressDefaultReqVO();
        reqVo.setId(address.getId());
        reqVo.setIsDefault(AddressDefaultEnum.DEFAULT.getCode());
        reqVo.setAddressType(addressType);

        // 模拟用户已登录
        AutoCloseable autoCloseable = null;
        try {
            autoCloseable = mockStatic(CrmSecurityFrameworkUtils.class);
            when(CrmSecurityFrameworkUtils.getLoginUserId()).thenReturn(userId);

            // 断言会抛出权限不足异常
            assertServiceException(() -> addressService.updateDefaultAddress(reqVo, userId),
                    FORBIDDEN);

        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        } finally {
            if (autoCloseable != null) {
                try {
                    autoCloseable.close();
                } catch (Exception e) {
                    // 忽略关闭异常
                }
            }
        }
    }

    // 测试不同地址类型可以各自有默认地址
    @Test
    public void testUpdateDefaultAddress_multipleTypes() {
        // 模拟登录用户
        Long userId = 1L;
        String senderType = "SENDER";
        String receiverType = "RECEIVER";

        // mock 数据 - 创建寄件人地址
        AddressDO senderAddress1 = randomPojo(AddressDO.class, o -> {
            o.setMemberId(userId);
            o.setIsDefault(AddressDefaultEnum.NOT_DEFAULT.getCode());
        });
        addressMapper.insert(senderAddress1);

        // mock 数据 - 创建收件人地址
        AddressDO receiverAddress1 = randomPojo(AddressDO.class, o -> {
            o.setMemberId(userId);
            o.setIsDefault(AddressDefaultEnum.NOT_DEFAULT.getCode());
        });
        addressMapper.insert(receiverAddress1);

        // 准备参数 - 将寄件人地址设为默认
        AddressDefaultReqVO senderReqVo = new AddressDefaultReqVO();
        senderReqVo.setId(senderAddress1.getId());
        senderReqVo.setIsDefault(AddressDefaultEnum.DEFAULT.getCode());
        senderReqVo.setAddressType(senderType);

        // 准备参数 - 将收件人地址设为默认
        AddressDefaultReqVO receiverReqVo = new AddressDefaultReqVO();
        receiverReqVo.setId(receiverAddress1.getId());
        receiverReqVo.setIsDefault(AddressDefaultEnum.DEFAULT.getCode());
        receiverReqVo.setAddressType(receiverType);

        // 模拟用户已登录
        AutoCloseable autoCloseable = null;
        try {
            autoCloseable = mockStatic(CrmSecurityFrameworkUtils.class);
            when(CrmSecurityFrameworkUtils.getLoginUserId()).thenReturn(userId);

            // 调用测试方法 - 设置寄件人默认地址
            Boolean senderResult = addressService.updateDefaultAddress(senderReqVo, userId);
            assertTrue(senderResult);

            // 调用测试方法 - 设置收件人默认地址
            Boolean receiverResult = addressService.updateDefaultAddress(receiverReqVo, userId);
            assertTrue(receiverResult);

            // 验证数据库中的更新 - 两种类型的地址都应该是默认的
            AddressDO updatedSenderAddress = addressMapper.selectById(senderAddress1.getId());
            AddressDO updatedReceiverAddress = addressMapper.selectById(receiverAddress1.getId());

            assertEquals(AddressDefaultEnum.DEFAULT.getCode(), updatedSenderAddress.getIsDefault());
            assertEquals(AddressDefaultEnum.DEFAULT.getCode(), updatedReceiverAddress.getIsDefault());

        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        } finally {
            if (autoCloseable != null) {
                try {
                    autoCloseable.close();
                } catch (Exception e) {
                    // 忽略关闭异常
                }
            }
        }
    }
    //</editor-fold>

    @Test
    public void testGetAddress_success() {
        // mock 数据
        AddressDO dbAddress = randomPojo(AddressDO.class);
        addressMapper.insert(dbAddress);

        // 调用
        AddressDO resultAddress = addressService.getAddress(dbAddress.getId());

        // 校验
        assertNotNull(resultAddress);
        assertPojoEquals(dbAddress, resultAddress);
    }

    @Test
    public void testGetAddress_notExists() {
        // 准备参数 - 一个不存在的ID
        Long nonExistingId = randomLongId();

        // 调用
        AddressDO resultAddress = addressService.getAddress(nonExistingId);

        // 校验 - 应返回null
        assertNull(resultAddress);
    }

    @Test
    public void testGetAddressPage() {
        // mock 数据
        Long memberId = 1L;

        // 创建3个地址，其中2个属于目标会员
        AddressDO address1 = randomPojo(AddressDO.class, o -> o.setMemberId(memberId));
        addressMapper.insert(address1);
        AddressDO address2 = randomPojo(AddressDO.class, o -> o.setMemberId(memberId));
        addressMapper.insert(address2);
        AddressDO address3 = randomPojo(AddressDO.class, o -> o.setMemberId(2L)); // 不同会员
        addressMapper.insert(address3);

        // 准备分页参数
        AddressPageReqVO pageReqVO = new AddressPageReqVO();
        pageReqVO.setMemberId(memberId);

        // 调用
        PageResult<AddressDO> pageResult = addressService.getAddressPage(pageReqVO);

        // 校验
        assertEquals(2, pageResult.getTotal());
        assertEquals(2, pageResult.getList().size());
        // 验证返回的数据包含正确的地址
        assertTrue(pageResult.getList().stream()
                .map(AddressDO::getId)
                .anyMatch(id -> id.equals(address1.getId())));
        assertTrue(pageResult.getList().stream()
                .map(AddressDO::getId)
                .anyMatch(id -> id.equals(address2.getId())));
    }

    @Test
    public void testSelectAddressByIds() {
        // mock 数据
        AddressDO address1 = randomPojo(AddressDO.class);
        addressMapper.insert(address1);
        AddressDO address2 = randomPojo(AddressDO.class);
        addressMapper.insert(address2);

        // 准备参数
        List<Long> ids = Arrays.asList(address1.getId(), address2.getId());

        // 调用
        List<AddressDO> result = addressService.selectAddressByIds(ids);

        // 校验
        assertEquals(2, result.size());
        assertTrue(result.stream()
                .map(AddressDO::getId)
                .anyMatch(id -> id.equals(address1.getId())));
        assertTrue(result.stream()
                .map(AddressDO::getId)
                .anyMatch(id -> id.equals(address2.getId())));
    }

    @Test
    public void testDelAddressByIdsAndMember_databaseError() {
        // 模拟登录用户
        Long userId = 1L;

        // 创建地址
        AddressDO dbAddress = randomPojo(AddressDO.class);
        dbAddress.setMemberId(userId);
        addressMapper.insert(dbAddress);

        // 准备参数
        List<Long> ids = List.of(dbAddress.getId());

        // 模拟数据库返回特定结果
        AddressMapper mockMapper = org.mockito.Mockito.mock(AddressMapper.class);
        when(mockMapper.deleteByIdsAndMemberId(ids, userId)).thenReturn(0); // 模拟删除失败，返回0

        // 此时需要替换addressService中的addressMapper为mock对象
        try {
            java.lang.reflect.Field field = AddressServiceImpl.class.getDeclaredField("addressMapper");
            field.setAccessible(true);
            field.set(addressService, mockMapper);
        } catch (Exception e) {
            fail("测试失败: 无法注入mock的AddressMapper");
        }

        // 调用
        Integer result = addressService.delAddressByIdsAndMember(ids, userId);

        // 验证结果 - 应该直接返回mapper的结果
        assertEquals(0, result);
    }

    // ========== fuzzySearchAddress 方法的测试 ==========

    @Test
    public void testFuzzySearchAddress_success_byPhone() {
        // 准备测试数据
        Long memberId = randomLongId();
        // 创建3个地址，其中2个属于目标会员
        AddressDO address1 = randomPojo(AddressDO.class, o -> {
            o.setMemberId(memberId);
            o.setPhone("13800138000");
            o.setName("张三");
        });
        AddressDO address2 = randomPojo(AddressDO.class, o -> {
            o.setMemberId(memberId);
            o.setPhone("13900139000");
            o.setName("李四");
        });
        AddressDO address3 = randomPojo(AddressDO.class, o -> {
            o.setMemberId(randomLongId()); // 不同用户
            o.setPhone("13800138001");
            o.setName("王五");
        });

        // 插入数据库
        addressMapper.insert(address1);
        addressMapper.insert(address2);
        addressMapper.insert(address3);

        // 创建查询条件
        AddressFuzzyQueryVO queryVO = new AddressFuzzyQueryVO();
        queryVO.setPhone("138");

        // 调用
        PageResult<AddressDO> result = addressService.fuzzySearchAddress(queryVO, memberId);

        // 断言
        assertEquals(1, result.getList().size());
        assertPojoEquals(address1, result.getList().get(0));
    }

    @Test
    public void testFuzzySearchAddress_success_byName() {
        // 准备测试数据
        Long memberId = randomLongId();
        // 创建3个地址，其中1个符合条件
        AddressDO address1 = randomPojo(AddressDO.class, o -> {
            o.setMemberId(memberId);
            o.setPhone("13800138000");
            o.setName("张三");
        });
        AddressDO address2 = randomPojo(AddressDO.class, o -> {
            o.setMemberId(memberId);
            o.setPhone("13900139000");
            o.setName("李四");
        });
        AddressDO address3 = randomPojo(AddressDO.class, o -> {
            o.setMemberId(randomLongId()); // 不同用户
            o.setPhone("13800138001");
            o.setName("张五");
        });

        // 插入数据库
        addressMapper.insert(address1);
        addressMapper.insert(address2);
        addressMapper.insert(address3);

        // 创建查询条件
        AddressFuzzyQueryVO queryVO = new AddressFuzzyQueryVO();
        queryVO.setName("张");

        // 调用
        PageResult<AddressDO> result = addressService.fuzzySearchAddress(queryVO, memberId);

        // 断言
        assertEquals(0, result.getList().size());
    }

    @Test
    public void testFuzzySearchAddress_emptyResult_whenNoMatchingConditions() {
        // 准备测试数据
        Long memberId = randomLongId();
        // 创建2个地址，都不符合条件
        AddressDO address1 = randomPojo(AddressDO.class, o -> {
            o.setMemberId(memberId);
            o.setPhone("13800138000");
            o.setName("张三");
        });
        AddressDO address2 = randomPojo(AddressDO.class, o -> {
            o.setMemberId(memberId);
            o.setPhone("13900139000");
            o.setName("李四");
        });

        // 插入数据库
        addressMapper.insert(address1);
        addressMapper.insert(address2);

        // 创建查询条件
        AddressFuzzyQueryVO queryVO = new AddressFuzzyQueryVO();
        queryVO.setPhone("159"); // 不匹配的手机号
        queryVO.setName("王"); // 不匹配的姓名

        // 调用
        PageResult<AddressDO> result = addressService.fuzzySearchAddress(queryVO, memberId);

        // 断言
        assertEquals(0, result.getList().size());
    }

    @Test
    public void testFuzzySearchAddress_emptyResult_whenConditionsTooShort() {
        // 准备测试数据
        Long memberId = randomLongId();
        // 创建1个地址
        AddressDO address = randomPojo(AddressDO.class, o -> {
            o.setMemberId(memberId);
            o.setPhone("13800138000");
            o.setName("张三");
        });

        // 插入数据库
        addressMapper.insert(address);

        // 创建查询条件 - 条件太短
        AddressFuzzyQueryVO queryVO = new AddressFuzzyQueryVO();
        queryVO.setPhone("1"); // 长度小于2

        // 调用
        PageResult<AddressDO> result = addressService.fuzzySearchAddress(queryVO, memberId);

        // 断言 - 应该返回空结果，因为条件长度不足
        assertEquals(0, result.getList().size());
    }

    @Test
    public void testFuzzySearchAddress_emptyResult_whenNoConditions() {
        // 准备测试数据
        Long memberId = randomLongId();
        // 创建1个地址
        AddressDO address = randomPojo(AddressDO.class, o -> {
            o.setMemberId(memberId);
            o.setPhone("13800138000");
            o.setName("张三");
        });

        // 插入数据库
        addressMapper.insert(address);

        // 创建查询条件 - 没有条件
        AddressFuzzyQueryVO queryVO = new AddressFuzzyQueryVO();

        // 调用
        PageResult<AddressDO> result = addressService.fuzzySearchAddress(queryVO, memberId);

        // 断言 - 应该返回空结果，因为没有查询条件
        assertEquals(0, result.getList().size());
    }
}