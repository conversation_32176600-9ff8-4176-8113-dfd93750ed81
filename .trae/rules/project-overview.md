---
description: 
globs: 
alwaysApply: true
---
# Express2B 项目概述

## 项目简介
Express2B 是一个基于 Spring Boot 的快递管理系统后端服务，采用多模块架构设计。

## 核心模块
- `express2b-dependencies`: Maven 依赖管理中心
- `express2b-framework`: 核心框架和通用组件
- `express2b-server`: 主应用入口模块
- `express2b-module-express`: 快递业务核心模块，支付模块
- `express2b-module-system`: 系统管理模块
- `express2b-module-infra`: 基础设施模块
- `express2b-module-agent`: 代理商模块
- `express2b-module-commission`: 分润模块
- `express2b-module-wallet`: 钱包模块
- `express2b-module-pricing`: 定价模块

## 技术栈
- Java 后端: Spring Boot 3.x
- 数据库: MySQL + MyBatis Plus
- 缓存: Redis + Redisson
- 安全认证: Spring Security

