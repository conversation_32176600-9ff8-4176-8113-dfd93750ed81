package com.yunyi.express2b.module.crm.dal.dataobject.cardstream;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 卡流水 DO
 *
 * <AUTHOR>
 */
@TableName("express2b_card_stream")
@KeySequence("express2b_card_stream_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CardStreamDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 卡id
     */
    private Long cardId;
    /**
     * 用户id
     */
    private Long personalId;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 支付单号
     */
    private Long payId;
    /**
     * 退款单号
     */
    private Long refundId;
    /**
     * 兑换码
     */
    private String redeemCode;
    /**
     * 增减次数
     */
    private Integer incDec;
    /**
     * 变化原因
     */
    private String reason;

}