package com.yunyi.express2b.module.agent.dal.mysql.customtemplaterequest;

import java.util.*;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.module.agent.dal.dataobject.customtemplaterequest.CustomTemplateRequestDO;
import org.apache.ibatis.annotations.Mapper;
import com.yunyi.express2b.module.agent.controller.admin.customtemplaterequest.vo.*;

/**
 * 代理商自定义价格模板申请记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CustomTemplateRequestMapper extends BaseMapperX<CustomTemplateRequestDO> {

    default PageResult<CustomTemplateRequestDO> selectPage(CustomTemplateRequestPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CustomTemplateRequestDO>()
                .eqIfPresent(CustomTemplateRequestDO::getAgentId, reqVO.getAgentId())
                .eqIfPresent(CustomTemplateRequestDO::getRequestedTemplateDetails, reqVO.getRequestedTemplateDetails())
                .eqIfPresent(CustomTemplateRequestDO::getStatus, reqVO.getStatus())
                .eqIfPresent(CustomTemplateRequestDO::getApprovedTemplateId, reqVO.getApprovedTemplateId())
                .eqIfPresent(CustomTemplateRequestDO::getRequestedAt, reqVO.getRequestedAt())
                .eqIfPresent(CustomTemplateRequestDO::getReviewedAt, reqVO.getReviewedAt())
                .eqIfPresent(CustomTemplateRequestDO::getReviewerComments, reqVO.getReviewerComments())
                .eqIfPresent(CustomTemplateRequestDO::getRemarks, reqVO.getRemarks())
                .betweenIfPresent(CustomTemplateRequestDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(CustomTemplateRequestDO::getId));
    }

    /**
     * 根据代理商自定义模版ID修改代理商的状态
     * @param id
     * @param status
     * @return
     */
    //Long updateStatusById(Long id, Long status);
    default Long updateStatusById(Long id,Long status){
        return Long.valueOf(update(new LambdaUpdateWrapper<CustomTemplateRequestDO>()
                .set(CustomTemplateRequestDO::getStatus,status)
                .eq(CustomTemplateRequestDO::getId,id)));
    }

    /**
     * 根据ID找到代理商的定价模板数据
     * @param id
     * @return
     */
    default String selectTemplateDetailsById(Long id) {
        CustomTemplateRequestDO record = selectOne(new LambdaQueryWrapperX<CustomTemplateRequestDO>()
                .select(CustomTemplateRequestDO::getRequestedTemplateDetails)
                .eq(CustomTemplateRequestDO::getId, id));
        return record != null ? record.getRequestedTemplateDetails() : null;
    }

    /**
     * 根据自定义模版ID查找当前代理商ID
     * @param id
     * @return
     */
    default Long selectAgentIdById(Long id) {
        CustomTemplateRequestDO record = selectOne(new LambdaQueryWrapperX<CustomTemplateRequestDO>()
                .select(CustomTemplateRequestDO::getAgentId)
                .eq(CustomTemplateRequestDO::getId, id));
        return record != null ? record.getAgentId() : null;
    }
    /**
     * 根据代理商ID查找历史记录
     * @param agentId
     * @return
     */
    default List<CustomTemplateRequestDO> selectQueryEcho(Long agentId) {
        List<CustomTemplateRequestDO> record = selectList(new LambdaQueryWrapperX<CustomTemplateRequestDO>()
                .select(CustomTemplateRequestDO::getAgentId,
                        CustomTemplateRequestDO::getRequestedTemplateDetails,
                        CustomTemplateRequestDO::getStatus,
                        CustomTemplateRequestDO::getRequestedAt,
                        CustomTemplateRequestDO::getReviewedAt,
                        CustomTemplateRequestDO::getReviewerComments,
                        CustomTemplateRequestDO::getRemarks,
                        CustomTemplateRequestDO::getCreateTime,
                        CustomTemplateRequestDO::getUpdateTime,
                        CustomTemplateRequestDO::getId)
                .eq(CustomTemplateRequestDO::getAgentId, agentId)
                .orderByDesc(CustomTemplateRequestDO::getId));
        return record;
    }
}