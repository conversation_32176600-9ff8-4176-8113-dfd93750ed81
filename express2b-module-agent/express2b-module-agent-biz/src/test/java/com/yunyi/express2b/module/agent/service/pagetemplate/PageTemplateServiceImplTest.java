package com.yunyi.express2b.module.agent.service.pagetemplate;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.module.agent.controller.admin.pagetemplate.vo.PageTemplatePageReqVO;
import com.yunyi.express2b.module.agent.controller.admin.pagetemplate.vo.PageTemplateSaveReqVO;
import com.yunyi.express2b.module.agent.dal.dataobject.pagetemplate.PageTemplateDO;
import com.yunyi.express2b.module.agent.dal.mysql.pagetemplate.PageTemplateMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import static com.yunyi.express2b.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static com.yunyi.express2b.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertPojoEquals;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertServiceException;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomInteger;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomPojo;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.PAGE_TEMPLATE_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link PageTemplateServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(PageTemplateServiceImpl.class)
public class PageTemplateServiceImplTest extends BaseDbUnitTest {

    @Resource
    private PageTemplateServiceImpl pageTemplateService;

    @Resource
    private PageTemplateMapper pageTemplateMapper;

    @Test
    public void testCreatePageTemplate_success() {
        // 准备参数
        PageTemplateSaveReqVO createReqVO = randomPojo(PageTemplateSaveReqVO.class).setId(null);

        // 调用
        Integer pageTemplateId = pageTemplateService.createPageTemplate(createReqVO);
        // 断言
        assertNotNull(pageTemplateId);
        // 校验记录的属性是否正确
        PageTemplateDO pageTemplate = pageTemplateMapper.selectById(pageTemplateId);
        assertPojoEquals(createReqVO, pageTemplate, "id");
    }

    @Test
    public void testUpdatePageTemplate_success() {
        // mock 数据
        PageTemplateDO dbPageTemplate = randomPojo(PageTemplateDO.class);
        pageTemplateMapper.insert(dbPageTemplate);// @Sql: 先插入出一条存在的数据
        // 准备参数
        PageTemplateSaveReqVO updateReqVO = randomPojo(PageTemplateSaveReqVO.class, o -> {
            o.setId(dbPageTemplate.getId()); // 设置更新的 ID
        });

        // 调用
        pageTemplateService.updatePageTemplate(updateReqVO);
        // 校验是否更新正确
        PageTemplateDO pageTemplate = pageTemplateMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, pageTemplate);
    }

    @Test
    public void testUpdatePageTemplate_notExists() {
        // 准备参数
        PageTemplateSaveReqVO updateReqVO = randomPojo(PageTemplateSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> pageTemplateService.updatePageTemplate(updateReqVO), PAGE_TEMPLATE_NOT_EXISTS);
    }

    @Test
    public void testDeletePageTemplate_success() {
        // mock 数据
        PageTemplateDO dbPageTemplate = randomPojo(PageTemplateDO.class);
        pageTemplateMapper.insert(dbPageTemplate);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Integer id = dbPageTemplate.getId();

        // 调用
        pageTemplateService.deletePageTemplate(id);
        // 校验数据不存在了
        assertNull(pageTemplateMapper.selectById(id));
    }

    @Test
    public void testDeletePageTemplate_notExists() {
        // 准备参数
        Integer id = randomInteger();

        // 调用, 并断言异常
        assertServiceException(() -> pageTemplateService.deletePageTemplate(id), PAGE_TEMPLATE_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPageTemplatePage() {
        // mock 数据
        PageTemplateDO dbPageTemplate = randomPojo(PageTemplateDO.class, o -> { // 等会查询到
            o.setModuleName(null);
            o.setComponentType(null);
            o.setDisplayName(null);
            o.setState(null);
            o.setDom(null);
            o.setParentDom(null);
            o.setSubModuleId(null);
            o.setSortOrder(null);
            o.setCreateTime(null);
        });
        pageTemplateMapper.insert(dbPageTemplate);
        // 测试 moduleName 不匹配
        pageTemplateMapper.insert(cloneIgnoreId(dbPageTemplate, o -> o.setModuleName(null)));
        // 测试 componentType 不匹配
        pageTemplateMapper.insert(cloneIgnoreId(dbPageTemplate, o -> o.setComponentType(null)));
        // 测试 displayName 不匹配
        pageTemplateMapper.insert(cloneIgnoreId(dbPageTemplate, o -> o.setDisplayName(null)));
        // 测试 state 不匹配
        pageTemplateMapper.insert(cloneIgnoreId(dbPageTemplate, o -> o.setState(null)));
        // 测试 dom 不匹配
        pageTemplateMapper.insert(cloneIgnoreId(dbPageTemplate, o -> o.setDom(null)));
        // 测试 parentDom 不匹配
        pageTemplateMapper.insert(cloneIgnoreId(dbPageTemplate, o -> o.setParentDom(null)));
        // 测试 subModuleId 不匹配
        pageTemplateMapper.insert(cloneIgnoreId(dbPageTemplate, o -> o.setSubModuleId(null)));
        // 测试 sortOrder 不匹配
        pageTemplateMapper.insert(cloneIgnoreId(dbPageTemplate, o -> o.setSortOrder(null)));
        // 测试 createTime 不匹配
        pageTemplateMapper.insert(cloneIgnoreId(dbPageTemplate, o -> o.setCreateTime(null)));
        // 准备参数
        PageTemplatePageReqVO reqVO = new PageTemplatePageReqVO();
        reqVO.setModuleName(null);
        reqVO.setComponentType(null);
        reqVO.setDisplayName(null);
        reqVO.setState(null);
        reqVO.setDom(null);
        reqVO.setParentDom(null);
        reqVO.setSubModuleId(null);
        reqVO.setSortOrder(null);
        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

        // 调用
        PageResult<PageTemplateDO> pageResult = pageTemplateService.getPageTemplatePage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbPageTemplate, pageResult.getList().get(0));
    }

}