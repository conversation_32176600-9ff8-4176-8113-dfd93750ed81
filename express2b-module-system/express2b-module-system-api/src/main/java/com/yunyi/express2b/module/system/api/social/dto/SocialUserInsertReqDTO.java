package com.yunyi.express2b.module.system.api.social.dto;

import com.yunyi.express2b.framework.common.enums.UserTypeEnum;
import com.yunyi.express2b.framework.common.validation.InEnum;
import com.yunyi.express2b.module.system.enums.social.SocialTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/3 20:18
 */
@Data
public class SocialUserInsertReqDTO {
    /**
     * 用户编号
     */
    @NotNull(message = "用户编号不能为空")
    private Long userId;
    /**
     * 用户类型
     */
    @InEnum(UserTypeEnum.class)
    @NotNull(message = "用户类型不能为空")
    private Integer userType;

    /**
     * 社交平台的类型
     */
    @InEnum(SocialTypeEnum.class)
    @NotNull(message = "社交平台的类型不能为空")
    private Integer socialType;

    /**
     * 社交平台的 openid
     */
    private String openid;

    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 用户头像
     */
    private String avatar;

    /**
     * ssoUserId
     */
    private String ssoUserId;
}
