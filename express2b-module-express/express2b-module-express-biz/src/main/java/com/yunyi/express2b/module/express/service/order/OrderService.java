package com.yunyi.express2b.module.express.service.order;

import com.alibaba.fastjson.JSONObject;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.module.express.controller.admin.order.dto.OrderCallBackRequestDTO;
import com.yunyi.express2b.module.express.controller.admin.order.dto.WorkbenchInterfaceDTO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.*;
import com.yunyi.express2b.module.express.controller.admin.paymentorder.vo.UnifiedOrderResponseNew;
import com.yunyi.express2b.module.express.controller.app.v1.order.vo.*;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.framework.api.login.api.cs.vo.CustomerServicesRequest;
import com.yunyi.framework.api.login.api.cs.vo.CustomerServicesResponse;
import com.yunyi.framework.api.login.api.qrcode.vo.PushRequest;
import com.yunyi.framework.api.logistics.api.express.vo.AddressListRequest;
import com.yunyi.framework.api.logistics.api.express.vo.ExpressTrackingData;
import com.yunyi.framework.api.logistics.api.order.vo.CreateOrderResponse;
import jakarta.validation.Valid;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 订单管理 Service 接口
 *
 * <AUTHOR>
 */
public interface OrderService {

    /**
     * 创建订单管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    UnifiedOrderResponseNew createOrder(@Valid OrderSaveReqVO createReqVO, List<RecipientVO> recipientVOs);

    /**
     * 多个寄件人和收件人批量创建订单
     * @param requestVO
     * @return
     */
    UnifiedOrderResponseNew createOrders(List<OrderWithRecipientVO> requestVO);

    /**
     * 统一支付
     *
     * @param agreePayVo
     * @param orderNos
     * @return
     */
    UnifiedOrderResponseNew paycreateOrder(@Valid AgreePayVo agreePayVo, List<String> orderNos);

    /**
     * 更新订单管理
     *
     * @param updateReqVO 更新信息
     */
    void updateOrder(@Valid OrderSaveReqVO updateReqVO);


    /**
     * 获得订单管理
     *
     * @param id 编号
     * @return 订单管理
     */
    OrderResponseVO getOrder(Long id);

    /**
     * 获得订单管理分页
     *
     * @param pageReqVO 分页查询
     * @return 订单管理分页
     */
    PageResult<OrderDO> getOrderPage(OrderPageReqVO pageReqVO);

    /**
     * 根据订单状态分页查询订单管理
     *
     * @param status 分页查询
     * @return 订单管理分页
     */
    PageResult<OrderDO> getOrderPages(String status);

    /**
     * 根据订单ID更新订单信息
     *
     * @param orderSaveReqVO 订单更新请求参数对象，包含订单ID和需要更新的字段数据
     * @return 受影响的数据库记录行数，通常用于判断操作是否成功执行
     */
    int updateByOrderId(OrderSaveReqVO orderSaveReqVO);

    /**
     * 执行订单取消操作
     *
     * @param orderCancelVO 订单取消请求参数对象，包含订单取消相关业务参数
     * @return 订单取消响应对象，包含取消操作结果状态和业务数据
     */
    OrderCancelVO cancelOrder(OrderCancelVO orderCancelVO);

    /**
     * 执行统一退款操作 部分退款和全部退款
     *
     * @param orderCancelVO
     * @return
     */
    OrderCancelVO refundcancelOrder(OrderCancelVO orderCancelVO);

    /**
     * 获取指定订单编号的订单状态
     *
     * @param orderNo
     * @return
     */
    String getOrderStatus(String orderNo);


    /**
     * 根据当前用户姓名手机号查询订单
     *
     * @param orderResUserVO
     * @return
     */
    PageResult<OrderDO> getOrderPageOrders(OrderResUserVO orderResUserVO);


    /**
     * 获取订单物流轨迹信息
     *
     * @param trackingNumber 订单编号
     * @return 包含物流轨迹详情的响应对象
     */


    List<ExpressTrackingData> getOrderLogistics(String trackingNumber, Long id);


    /**
     * 接收并处理订单消息
     *
     * @param request 消息接收请求参数对象
     * @return 消息处理结果标识字符串
     */
    String receiveOrderMessage(ReceiveMessageRequest request);

    /**
     * 获取指定时间范围内的支付状态金额统计
     *
     * @param startTime
     * @param endTime
     * @return 支付状态金额统计结果对象
     */
    PaymentStatusAmountReqVO getStatusPay(String startTime, String endTime);

    /**
     * 分页查询未支付订单列表
     *
     * @param pageReqVO 分页查询条件参数对象
     * @return 分页结果对象，包含订单数据列表
     */
    PageResult<OrderDO> unpaidOrders(OrderPageReqVO pageReqVO);

    /**
     * EXCEL批量导入地址数据
     *
     * @param importExcelAddress 待导入的地址解析对象列表
     * @param updateSupport      是否允许更新已存在地址
     * @return 地址导入结果响应对象
     */
    List<AddressImportReqVO> importAddressList(List<AddressImportReqVO> importExcelAddress, Boolean updateSupport);

    /**
     * 获取订单费用明细清单
     *
     * @param orderSaveReqVO 订单保存请求参数对象
     * @return 费用明细请求对象列表
     */
    List<FeeDetailReqVO> getFeeDetail(OrderSaveReqVO orderSaveReqVO);

    /**
     * 快速检索物流公司信息
     *
     * @param request 物流公司名称模糊匹配关键字
     * @return 符合条件的基础物流公司对象集合
     */
    ExpressListResponseVO getFastTrackLogistics(ExpressListRequestVO request);

    /**
     * 获取订单数量统计信息
     *
     * @param
     * @return 包含订单统计数据的响应对象
     */
    OrdersCountQeqVO getOrdersCount();

    /**
     * 统计今日订单数量
     *
     * @param
     * @return 当日订单总数
     */
    Long todayCountOrdersCount();

    /**
     * 统计未付款订单数量
     *
     * @return
     */
    Long counts();

    /**
     * 处理Excel文件并同步到SSO服务器
     *
     * @param file Excel文件对象
     * @return 地址导入结果响应对象
     */
    AddressResponse processExcelAndSyncToSSO(MultipartFile file);

    /**
     * 订单回调传入的参数
     */
    void orderCallback(OrderCallBackRequestDTO orderCallBackRequest);


    CancelOrderResponseVO cancelOrderCallBack(CancelOrderCallBackVO cancelOrderCallBackVO);

    //CreateOrderResponse createTrackingNumber(OrderSaveReqVO createOrderVO) throws Exception;

    CreateOrderResponse createTracking(OrderCreateRequest orderCreateRequest) throws Exception;

    /**
     * 解析内容生成地址保存请求对象列表
     *
     * @param content 需要解析的原始文本内容
     * @return 解析后的地址保存请求对象集合
     */
    List<AddressRespVO> contentParse(AddressListRequest content);

    /**
     * 手动复制粘贴的解析地址
     *
     * @param content 需要解析的原始文本内容
     * @return 解析后的地址保存请求对象集合
     */
    AddressResponse manualParseAddress(AddressListRequest content);

    /**
     * 订单物流轨迹信息回调
     * @param trackCallBackVO
     */
    void trackCallback(TrackCallBackVO trackCallBackVO);

    /**
     * 订单列表
     * @param startTime
     * @param endTime
     * @return
     */
    OrderListRespVO getOrderList(String startTime, String endTime);

    /**
     * 订阅列表
     * @return
     */
    CommonResult sendtemplete();

    /**
     * 订单数 订单付款数 已入账订单数（） 带入账订单数（） 销售额 实时对比数据下方的对比昨日
     *
     * @param workbenchInterfaceDTO
     * @return
     */
    WorkbenchInterfaceDTO workbenchInterface(WorkbenchInterfaceDTO workbenchInterfaceDTO);

    /**
     * 订单推送到统一推广平台
     * @param
     * @return
     */
    Boolean createOrderToPromo();


    /**
     * 获取在线客服数据
     * @param request
     * @return
     */
    CustomerServicesResponse getOnlineCustomerData(CustomerServicesRequest request);
    /**
     * 下载海报
     */
    List<PosterTemplateVO> downloadPoster();

    /**
     * 生成小程序
     */
    JSONObject generateApplets();

    /**
     * 创建二维码
     */
    JSONObject createQrCode();

    /**
     * 修改用户信息
     */
    JSONObject updatePersonal(UpdateUserRequest updateUserRequest);

    /**
     * 获取微信关注公众号链接
     */
    OfficialAccountResponseVO officialAccountLink();

    /**
     * 公众号关注/取关推送通知接口
     */
    Long officialAccountAttentionOrClearance(PushRequest pushRequest);

    /**
     * 开通平台代理
     */
    JSONObject openPlatformAgent();
}