package com.yunyi.express2b.module.express.mq.message;

import com.yunyi.express2b.framework.mq.redis.core.pubsub.AbstractRedisChannelMessage;
import com.yunyi.express2b.module.express.mq.consumer.OrderStatusConsumer;
import com.yunyi.express2b.module.express.mq.producer.OrderStatusProducer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单状态更新消息
 * 当订单状态更新时，通过生产者（{@link OrderStatusProducer}）将向订阅者（{@link OrderStatusConsumer}）发布此消息。
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/29 11:57
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderStatusRefreshMessage extends AbstractRedisChannelMessage {
    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单状态-变更前
     */
    private String statusStart;
    /**
     * 订单状态-变更后（即本次状态的目标）
     */
    private String statusEnd;
    /**
     * 操作人ID（系统或用户）
     */
    private Long operatorId;

    /**
     * 定义通道名称
     *
     * @return 通道名称
     */
    @Override
    public String getChannel() {
        return "express.order.status.refresh";
    }
}
