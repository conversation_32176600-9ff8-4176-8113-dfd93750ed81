package com.yunyi.express2b.module.express.controller.app.v1.price.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 寄件价格表查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/9 下午7:31
 */
@Data
public class PriceListReqVO {
    /**
     * 寄件人省份
     */
    @NotNull
    private String senderProvince;

    /**
     * 寄件人城市
     */
    @NotNull
    private String senderCity;

    /**
     * 收件人省份 收件人地址传空返回全部省
     */
    private String receiverProvince;

    /**
     * 收件人城市
     */
    private String receiverCity;

    /**
     * 快递公司编码
     */
    @NotNull
    private String expressCode;

    /**
     * 最小发单量
     */
    @NotNull
    private Integer minQuantity;
}
