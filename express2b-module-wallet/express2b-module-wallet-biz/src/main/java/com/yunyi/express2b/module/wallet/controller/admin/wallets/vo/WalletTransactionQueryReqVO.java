package com.yunyi.express2b.module.wallet.controller.admin.wallets.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/22 12:59
 */


@Schema(description = "管理后台 - 钱包流水 WalletTransactionQueryReq VO")
@Data
@ExcelIgnoreUnannotated
public class WalletTransactionQueryReqVO {
    @Schema(description = "交易类型 (具体取值见字典说明)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("交易类型 (具体取值见字典说明)")
    private String transactionType;

    @Schema(description = "交易金额 (正为收入,负为支出) (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易金额 (正为收入,负为支出) (单位: 分)")
    private Integer amount;

    @Schema(description = "交易前账户总余额 (可用+冻结) (单位: 分)")
    @ExcelProperty("交易前账户总余额 (可用+冻结) (单位: 分)")
    private Integer balanceBeforeTransaction;

    @Schema(description = "交易后账户总余额 (可用+冻结) (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易后账户总余额 (可用+冻结) (单位: 分)")
    private Integer balanceAfterTransaction;

    @Schema(description = "交易后可用余额 (单位: 分)")
    @ExcelProperty("交易后可用余额 (单位: 分)")
    private Integer availableBalanceAfterTransaction;

    @Schema(description = "交易后冻结余额 (单位: 分)")
    @ExcelProperty("交易后冻结余额 (单位: 分)")
    private Integer frozenBalanceAfterTransaction;

    @Schema(description = "流水状态 (具体取值见字典说明)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("流水状态 (具体取值见字典说明)")
    private String transactionStatus;

    @Schema(description = "交易描述", example = "你猜")
    @ExcelProperty("交易描述")
    private String description;

    @Schema(description = "资金预计可用时间 (解冻时间)")
    @ExcelProperty("资金预计可用时间 (解冻时间)")
    private LocalDateTime availableAt;

    @Schema(description = "备注信息 (如操作人,调账原因)", example = "你猜")
    @ExcelProperty("备注信息 (如操作人,调账原因)")
    private String remark;

    @Schema(description = "可用余额 (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("可用余额 (单位: 分)")
    private Integer availableBalance;

    @Schema(description = "冻结余额 (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("冻结余额 (单位: 分)")
    private Integer frozenBalance;

    @Schema(description = "累计利润总额 (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("累计利润总额 (单位: 分)")
    private Integer totalProfitEarned;

    @Schema(description = "累计分润总额 (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("累计分润总额 (单位: 分)")
    private Integer totalCommissionEarned;

    @Schema(description = "累计已提现总额 (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("累计已提现总额 (单位: 分)")
    private Integer totalWithdrawnAmount;

    @Schema(description = "钱包状态 (具体取值见字典说明)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("钱包状态 (具体取值见字典说明)")
    private Integer walletStatus;

    @Schema(description = "最晚提现日 (针对最早一笔可用资金)")
    @ExcelProperty("最晚提现日 (针对最早一笔可用资金)")
    private LocalDate lastWithdrawalDeadline;
}
