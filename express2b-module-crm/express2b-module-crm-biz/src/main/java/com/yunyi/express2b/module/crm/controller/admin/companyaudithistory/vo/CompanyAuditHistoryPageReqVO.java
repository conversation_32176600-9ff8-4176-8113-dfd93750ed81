package com.yunyi.express2b.module.crm.controller.admin.companyaudithistory.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 企业账户审核记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CompanyAuditHistoryPageReqVO extends PageParam {

    @Schema(description = "企业id", example = "26593")
    private Long companyId;

    @Schema(description = "企业名称", example = "李四")
    private String companyName;

    @Schema(description = "审核人")
    private String auditUser;

    @Schema(description = "审核结果", example = "1")
    private Integer status;

    @Schema(description = "备注 审核失败原因")
    private String remarks;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}