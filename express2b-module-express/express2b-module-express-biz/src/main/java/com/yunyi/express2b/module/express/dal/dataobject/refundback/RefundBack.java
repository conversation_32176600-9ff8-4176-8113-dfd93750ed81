package com.yunyi.express2b.module.express.dal.dataobject.refundback;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025-03-29 13:48:39
 */
public class RefundBack {
    //退款单号
    @NotBlank(message = "退款单号不能为空")
    private String refundOrderSn;
    //退款金额
    @NotBlank(message = "退款金额不能为空")
    private Integer amount;
    //三方单号
    @NotBlank(message = "三方单号不能为空")
    private String thirdOrderSn;
}
