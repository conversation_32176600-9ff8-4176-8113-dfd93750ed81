package com.yunyi.express2b.module.agent.service.pagetemplate;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.yunyi.express2b.module.agent.controller.admin.pagetemplate.vo.*;
import com.yunyi.express2b.module.agent.dal.dataobject.pagetemplate.PageTemplateDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;

import com.yunyi.express2b.module.agent.dal.mysql.pagetemplate.PageTemplateMapper;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.*;

/**
 * 页面模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PageTemplateServiceImpl implements PageTemplateService {

    @Resource
    private PageTemplateMapper pageTemplateMapper;

    @Override
    public Integer createPageTemplate(PageTemplateSaveReqVO createReqVO) {
        // 插入
        PageTemplateDO pageTemplate = BeanUtils.toBean(createReqVO, PageTemplateDO.class);
        pageTemplateMapper.insert(pageTemplate);
        // 返回
        return pageTemplate.getId();
    }

    @Override
    public void updatePageTemplate(PageTemplateSaveReqVO updateReqVO) {
        // 校验存在
        validatePageTemplateExists(updateReqVO.getId());
        // 更新
        PageTemplateDO updateObj = BeanUtils.toBean(updateReqVO, PageTemplateDO.class);
        pageTemplateMapper.updateById(updateObj);
    }

    @Override
    public void deletePageTemplate(Integer id) {
        // 校验存在
        validatePageTemplateExists(id);
        // 删除
        pageTemplateMapper.deleteById(id);
    }

    private void validatePageTemplateExists(Integer id) {
        if (pageTemplateMapper.selectById(id) == null) {
            throw exception(PAGE_TEMPLATE_NOT_EXISTS);
        }
    }

    @Override
    public PageTemplateDO getPageTemplate(Integer id) {
        return pageTemplateMapper.selectById(id);
    }

    @Override
    public PageResult<PageTemplateDO> getPageTemplatePage(PageTemplatePageReqVO pageReqVO) {
        return pageTemplateMapper.selectPage(pageReqVO);
    }

}