package com.yunyi.express2b.module.express.controller.admin.workorder.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 基础工单响应vo
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/6/5 18:40
 */
@Data
public class BaseWorkOrderResVO {
    @Schema(description = "工单id", requiredMode = Schema.RequiredMode.REQUIRED, example = "10042")
    @ExcelProperty("工单id")
    private Long id;

    @Schema(description = "附件地址")
    private List<String> fileUrls;
}
