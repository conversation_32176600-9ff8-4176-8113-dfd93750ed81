package com.yunyi.express2b.module.crm.controller.admin.companyinfo.vo;

import com.yunyi.express2b.framework.common.validation.InEnum;
import com.yunyi.express2b.module.crm.enums.AuditStateEnum;
import com.yunyi.express2b.module.crm.enums.CompanyTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
/**
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 公司信息审核 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CompanyInfoRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "31118")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "企业账户id", example = "4603")
    @ExcelProperty("企业账户id")
    private Long companyId;

    @Schema(description = "名称", example = "芋艿")
    @ExcelProperty("名称")
    private String companyName;

    @Schema(description = "联系人姓名", example = "王五")
    @ExcelProperty("联系人姓名")
    private String contactName;

    @Schema(description = "联系电话")
    @ExcelProperty("联系电话")
    private String contactPhone;

    @Schema(description = "营业执照/证件照人像面")
    @ExcelProperty("营业执照/证件照人像面")
    private String companyLicense;

    @Schema(description = "营业执照/证件照国徽面")
    @ExcelProperty("营业执照/证件照国徽面")
    private String companyLicense1;

    @Schema(description = "备用")
    @ExcelProperty("备用")
    private String companyLicense2;

    @Schema(description = "企业类型（1：个人，2：企业）", example = "1")
    @ExcelProperty("企业类型（1：个人，2：企业）")
    @InEnum(value = CompanyTypeEnum.class)
    private Integer companyType;

    @Schema(description = "0待审核 1 审核成功 2 审核失败", example = "1")
    @ExcelProperty("0待审核 1 审核成功 2 审核失败")
    @InEnum(value = AuditStateEnum.class)
    private String status;

    @Schema(description = "审核备注（审核失败原因）")
    @ExcelProperty("审核备注（审核失败原因）")
    private String remarks;

    @Schema(description = "最后审核时间")
    @ExcelProperty("最后审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}