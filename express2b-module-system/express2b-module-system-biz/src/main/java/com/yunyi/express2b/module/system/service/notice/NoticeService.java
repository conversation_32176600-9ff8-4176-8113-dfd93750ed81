package com.yunyi.express2b.module.system.service.notice;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.module.system.controller.admin.notice.vo.NoticePageReqVO;
import com.yunyi.express2b.module.system.controller.admin.notice.vo.NoticeRespVO;
import com.yunyi.express2b.module.system.controller.admin.notice.vo.NoticeSaveReqVO;
import com.yunyi.express2b.module.system.dal.dataobject.notice.NoticeDO;

import java.util.List;

/**
 * 通知公告 Service 接口
 */
public interface NoticeService {

    /**
     * 创建通知公告
     *
     * @param createReqVO 通知公告
     * @return 编号
     */
    Long createNotice(NoticeSaveReqVO createReqVO);

    /**
     * 更新通知公告
     *
     * @param reqVO 通知公告
     */
    void updateNotice(NoticeSaveReqVO reqVO);

    /**
     * 删除通知公告
     *
     * @param id 编号
     */
    void deleteNotice(Long id);

    /**
     * 获得通知公告分页列表
     *
     * @param reqVO 分页条件
     * @return 部门分页列表
     */
    PageResult<NoticeDO> getNoticePage(NoticePageReqVO reqVO);

    /**
     * 获得通知公告
     *
     * @param id 编号
     * @return 通知公告
     */
    NoticeDO getNotice(Long id);

    /**
     * 获得正常状态的所有公告列表
     *
     * @return 公告列表
     */
    List<NoticeRespVO> getAnnouncementList();
}
