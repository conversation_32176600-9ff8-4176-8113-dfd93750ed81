package com.yunyi.express2b.module.agent.service.workbench;

import com.yunyi.express2b.module.agent.controller.admin.workbench.vo.AgentIncomeTrendVo;
import com.yunyi.express2b.module.agent.controller.admin.workbench.vo.AgentOrderTrendVo;
import com.yunyi.express2b.module.agent.controller.admin.workbench.vo.AgentTotalVo;


/**
 * 工作台业务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/27 下午1:15
 */

public interface WorkbenchService {
    /**
     * 获取代理商总数,代理商下单的数量,代理商直属下级的数量,代理商间接下属数量
     * @param agentId
     * @return
     */
    AgentTotalVo getTotal(Long agentId);

    /**
     * 获取代理商订单趋势图显示
     * @return
     */
    AgentOrderTrendVo getorderTrendDisplay(Long agentId);

    /**
     * 获取代理商收入趋势图显示
     * @return
     */
    AgentIncomeTrendVo getIncomeTrendDisplay(Long agentId);
}
