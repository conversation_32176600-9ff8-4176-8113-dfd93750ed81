package com.yunyi.express2b.module.wallet.controller.admin.requests.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/20 10:51
 */

@Schema(description = "管理后台 - 钱包详情响应VO walletDetailRespVO")
@Data
public class walletDetailRespVO {
    @Schema(description = "代理商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long agentId;

    @Schema(description = "代理商名称", example = "张三")
    private String agentName;

    @Schema(description = "可用余额(单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer availableBalance;

    @Schema(description = "冻结余额(单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer frozenBalance;

    @Schema(description = "累计利润总额(单位: 分)")
    private Integer totalProfitEarned;

    @Schema(description = "累计分润总额(单位: 分)")
    private Integer totalCommissionEarned;

    @Schema(description = "累计已提现总额(单位: 分)")
    private Integer totalWithdrawnAmount;

    @Schema(description = "钱包状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer status;

    @Schema(description = "钱包状态描述", example = "正常")
    private String statusName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2025-01-01 00:00:00")
    private String createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间", example = "2025-01-01 00:00:00")
    private String updateTime;
}
