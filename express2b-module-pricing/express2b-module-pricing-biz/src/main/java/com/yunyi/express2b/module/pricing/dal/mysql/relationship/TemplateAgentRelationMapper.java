package com.yunyi.express2b.module.pricing.dal.mysql.relationship;

import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.module.pricing.dal.dataobject.relationship.TemplateAgentRelationDo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 定价关系模版表
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/22 下午1:21
 */
@Mapper
public interface TemplateAgentRelationMapper extends BaseMapperX<TemplateAgentRelationDo> {
    default Long insertTemplateAgentRelation(Long agentId,Long templateId) {
        TemplateAgentRelationDo templateAgentRelationDo = new TemplateAgentRelationDo();
        templateAgentRelationDo.setAgentId(agentId);
        templateAgentRelationDo.setTemplateId(templateId);
        return (long) insert(templateAgentRelationDo);
    }
}
