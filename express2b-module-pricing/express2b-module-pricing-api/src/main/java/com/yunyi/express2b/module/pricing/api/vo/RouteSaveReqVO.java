package com.yunyi.express2b.module.pricing.api.vo;


import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


@Data
public class RouteSaveReqVO {


    private Long id;


    @NotEmpty(message = "品牌标识不能为空")
    private String brandKey;


    @NotEmpty(message = "线路标识（格式：品牌-始发地-目的地）不能为空")
    private String lineKey;


    private String sendProvince;


    private String receiveProvince;


    @NotEmpty(message = "始发地adcode不能为空")
    private String sendProvinceCode;


    @NotEmpty(message = "目的地adcode不能为空")
    private String receiveProvinceCode;


    private String productCode;


    @NotNull(message = "首重重量(g)不能为空")
    private Integer firstWeight;


    @NotNull(message = "续重重量(g)不能为空")
    private Integer additionalWeight;


    @NotNull(message = "供应首重成本价（分）不能为空")
    private Integer costFirstPrice;


    @NotNull(message = "供应续重成本价（分）不能为空")
    private Integer costOverPrice;


    private Integer originalFirstPrice;


    private Integer originalOverPrice;

}