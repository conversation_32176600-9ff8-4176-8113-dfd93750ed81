package com.yunyi.express2b.module.express.service.workorder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yunyi.express2b.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.express.controller.admin.workorder.vo.BaseWorkOrderResVO;
import com.yunyi.express2b.module.express.controller.admin.workorder.vo.CallbackReqVO;
import com.yunyi.express2b.module.express.controller.admin.workorder.vo.WorkOrderUpdateReqVO;
import com.yunyi.express2b.module.express.controller.app.v1.workorder.vo.*;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.workorder.WorkOrderFileDO;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.workorder.WorkOrderFileMapper;
import com.yunyi.express2b.module.express.enums.InitiatorTypeEnum;
import com.yunyi.express2b.module.express.enums.WorkOrderStatusEnum;
import com.yunyi.express2b.module.express.enums.WorkOrderTypeEnum;
import com.yunyi.framework.api.login.api.workorder.WorkorderApi;
import com.yunyi.framework.api.login.api.workorder.vo.WorkeRcordRequest;
import com.yunyi.framework.api.login.api.workorder.vo.WorkrRequest;
import com.yunyi.framework.api.login.utils.TransactionNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.yunyi.express2b.module.express.dal.dataobject.workorder.WorkOrderDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;

import com.yunyi.express2b.module.express.dal.mysql.workorder.WorkOrderMapper;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.*;
import static com.yunyi.express2b.module.express.enums.WorkOrderTypeEnum.*;

/**
 * 工单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class WorkOrderServiceImpl implements WorkOrderService {

    @Resource
    private WorkOrderMapper workOrderMapper;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private WorkOrderFileMapper workOrderFileMapper;
    @Resource
    private WorkorderApi workorderApi;
    //外部回调url地址
    @Value("${yunyi.work-order.callback}")
    private  String WORK_ORDER_CALLBACK; //todo 回调地址配置项问题

    /**
     * 创建工单
     * @param initiatorTypeEnum
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public String createWorkOrder(InitiatorTypeEnum initiatorTypeEnum, WorkOrderCreateReqVO createReqVO) {
        //关于initiatorTypeEnum参数说明：目前APP端请求当作用户处理，管理端请求当作系统处理
        // 1.校验订单
        OrderDO orderDO = orderMapper.selectOrderByOrderNo(createReqVO.getOrderSn());
        if (ObjectUtil.isEmpty(orderDO)){
            throw exception(WORK_ORDER_ORDER_NOT_EXISTS);
        }
        // 2.创建工单编号
        String workOrderSn = TransactionNoGenerator.generateWorkOrderNumber(
                                String.valueOf(initiatorTypeEnum.getCode()),//@注意：此处为操作人信息，目前默认为用户
                                createReqVO.getType(),
                                orderDO.getExpressCode(),
                                new Date());
        // 3.准备调用接口所需的对象
        WorkrRequest request = getWorkrRequest(createReqVO,orderDO);
        // 4.调用平台接口
        CommonResult<JSONObject> workOrder = workorderApi.workorder(request);
        log.debug("【调用运力平台接口返回结果】：{}", workOrder);
        // 5.判断返回值是否成功
        JSONObject resJson = workOrder.getData();
        if (!"00000".equals(resJson.getString("code"))){
            // 先调用第三方API接口创建工单，如果失败则不存储数据到工单便、附件表
            log.error("【工单创建失败-调用运力平台接口】：{}", workOrder);
            throw exception(WORK_ORDER_CREATE_FAIL);
        }
        // 6.转换DO对象
        WorkOrderDO workOrderDO = new WorkOrderDO();
        workOrderDO.setWorkOrderSn(workOrderSn);
        workOrderDO.setMemberId(SecurityFrameworkUtils.getLoginUserId());
        workOrderDO.setOperatorId(SecurityFrameworkUtils.getLoginUserId());
        workOrderDO.setOrderSn(createReqVO.getOrderSn());
        workOrderDO.setType(createReqVO.getType());
        workOrderDO.setPassengerQuestion(createReqVO.getContent());
        workOrderDO.setTripleId(Long.valueOf(resJson.getJSONObject("data").getString("id")));
        // 7.插入到数据库
        int insert = workOrderMapper.insert(workOrderDO);
            //7.1插入工单表
        if (insert <=  0){
            log.error("【工单创建失败-数据库插入】：{}", workOrderDO);
            throw exception(WORK_ORDER_CREATE_FAIL);
        }
            //7.2插入附件表
        if (createReqVO.getFileUrls() != null &&!createReqVO.getFileUrls().isEmpty()){
            List<WorkOrderFileDO> fileList = getWorkOrderFileDOS(createReqVO, workOrderDO);
            // 批量插入
            workOrderFileMapper.insertBatch(fileList);
        }
        // 8.返回工单编号
        return workOrderDO.getWorkOrderSn();
    }

    /**
     * 创建附件批量插入对象
     */
    private  List<WorkOrderFileDO> getWorkOrderFileDOS(WorkOrderCreateReqVO createReqVO, WorkOrderDO workOrderDO) {
        List<WorkOrderFileDO> fileList = new ArrayList<>();
        Long workOrderId = workOrderDO.getId();
        String creator = String.valueOf(SecurityFrameworkUtils.getLoginUserId());
        for (String fileUrl : createReqVO.getFileUrls()) {
            WorkOrderFileDO workOrderFileDO = new WorkOrderFileDO();
            workOrderFileDO.setFile(fileUrl);
            workOrderFileDO.setWorkOrderId(workOrderId);
            workOrderFileDO.setCreator(creator);
            fileList.add(workOrderFileDO);
        }
        return fileList;
    }

    /**
     * 获取调用运力平台接口所需的对象
     */
    private  WorkrRequest getWorkrRequest(WorkOrderCreateReqVO createReqVO, OrderDO orderDO) {
        //1. 校验参数
        if (GRAVIMETRIC_ANOMALY.getCode().equals(createReqVO.getType()) && ObjectUtil.isEmpty(createReqVO.getWeight())) {
            throw exception(WORK_ORDER_WEIGHT_REQUIRED);
        }
        List<Integer> workOrderTypeEnums = Arrays.asList(
                DAMAGED.getCode(),
                LOST_PARCEL.getCode(),
                MISSING_ITEMS.getCode(),
                WRONG_ITEMS.getCode());
        if (workOrderTypeEnums.contains(createReqVO.getType()) && ObjectUtil.isEmpty(createReqVO.getPrice())){
            throw exception(WORK_ORDER_PRICE_REQUIRED);
        }
        //2. 创建请求对象
        WorkrRequest request = new WorkrRequest();
        BeanUtil.copyProperties(createReqVO,request);
        if (orderDO == null || orderDO.getTrackingNumber() == null){
            throw exception(WORK_ORDER_ORDER_NOT_EXISTS);
        }
        request.setExpressSn(orderDO.getTrackingNumber());
        request.setCallBackUrl(WORK_ORDER_CALLBACK);
        if (createReqVO.getFileUrls()!=null){
            JSONArray  fileUrls = new JSONArray();
            for (String fileUrl : createReqVO.getFileUrls()){
                JSONObject  fileUrlObj = new JSONObject();
                fileUrlObj.put("type",0);
                fileUrlObj.put("uri",fileUrl);
                fileUrls.add(fileUrlObj);
            }
            request.setAttach(JSONObject.toJSONString(fileUrls));
        }
        //3. 返回请求对象
        return request;
    }

    /**
     * 发送工单消息
     * @param workOrderMessageReqVO
     * @return
     */
    @Override
    public CommonResult<Boolean> sendWorkOrderMessage(WorkOrderMessageReqVO workOrderMessageReqVO) {
        //1. 查询数据库校验参数
        WorkOrderDO  workOrderDO = workOrderMapper.selectById(workOrderMessageReqVO.getId());
        if (workOrderDO == null){
            log.error("【工单不存在】：{}", workOrderMessageReqVO.getId());
            throw exception(WORK_ORDER_NOT_EXISTS);
        }
        if (ObjectUtil.isEmpty(workOrderDO.getTripleId())){
            log.error("【工单TripleId字段数据缺失】：{}", workOrderMessageReqVO.getId());
            throw exception(WORK_ORDER_NOT_EXISTS);
        }
        //2. 准备三方接口请求对象
        WorkeRcordRequest workeRcordRequest = new WorkeRcordRequest();
        workeRcordRequest.setWorkId(workOrderDO.getTripleId());
        workeRcordRequest.setContent(workOrderMessageReqVO.getPassengerQuestion());
        //3. 发送三方接口请求
        CommonResult<JSONObject> jsonObjectCommonResult = workorderApi.orderRecord(workeRcordRequest);
        log.debug(jsonObjectCommonResult.toString());
        JSONObject jsonObject = jsonObjectCommonResult.getData();
        CommonResult<Boolean> booleanCommonResult = new CommonResult<>();
        booleanCommonResult.setMsg(jsonObject.getString("msg"));
        if ("00000".equals(jsonObject.getString("code"))){
            booleanCommonResult.setData(true);
            booleanCommonResult.setCode(GlobalErrorCodeConstants.SUCCESS.getCode());
        }else {
            booleanCommonResult.setData(false);
            booleanCommonResult.setCode(GlobalErrorCodeConstants.LOCKED.getCode());
        }
        return booleanCommonResult;
    }


    /**
     * 获得工单列表分页
     * @param isUser
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<WorkOrderDO> getWorkOrderPage(Boolean isUser,WorkOrderPageReqVO pageReqVO) {
        //强制用户查询自己的工单
        if (isUser){
            pageReqVO.setMemberId(SecurityFrameworkUtils.getLoginUserId());
        }
        PageResult<WorkOrderDO> workOrderDOPageResult = workOrderMapper.selectPage(pageReqVO);
        //返回工单类型的枚举名称
        workOrderDOPageResult.getList().forEach(workOrderDO -> {
            // 获取工单类型枚举并设置名称
            WorkOrderTypeEnum typeEnum = WorkOrderTypeEnum.getByCode(workOrderDO.getType());
            workOrderDO.setTypeName(typeEnum.getName());
            // 获取工单状态枚举并设置名称
            WorkOrderStatusEnum statusEnum = WorkOrderStatusEnum.getByCode(workOrderDO.getWorkOrderStatus());
            workOrderDO.setWorkOrderStatusName( statusEnum.getName());
            });
        return workOrderDOPageResult;
    }

    /**
     * 工单回调
     * @param callbackReqVO
     * @return
     */
    @Override
    public Boolean callback(CallbackReqVO callbackReqVO) {
        WorkOrderDO workOrderDO = new WorkOrderDO();
        CallbackReqVO.BasePOJO data = callbackReqVO.getData();
        workOrderDO.setTripleId(data.getConsultId());
        workOrderDO.setWorkOrderStatus(data.getStatus());
        workOrderDO.setCustomerServiceReply(data.getResult());
        String responseTime = data.getResponseTime();
        if (ObjectUtil.isNotEmpty(responseTime)){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime customerServiceTime = LocalDateTime.parse(responseTime, formatter);
            workOrderDO.setCustomerServiceTime(customerServiceTime);
        }
        log.debug("【工单回调】：{}",  workOrderDO);
        int i = workOrderMapper.updateByTripleId(workOrderDO);
        if(i <= 0){
            throw exception(WORK_ORDER_UPDATE_FAIL);
        }
        return true;
    }

    /**
     * 删除工单
     * @param id
     * @return
     */
    @Override
    public CommonResult<Boolean> deleteWorkOrder(Long id){
        // 校验存在
        validateSubscribeExists(id);
        // 删除
        workOrderMapper.deleteById(id);
        return success(true);
    }

    /**
     * 获得工单
     * @param isUser
     * @param id
     * @return
     */
    @Override
    public WorkOrderDO getWorkOrderById(Boolean isUser,Long id) {
        //用户强制查询本人工单
        if (isUser){
            return workOrderMapper.selectByIdAndMemberId(id,SecurityFrameworkUtils.getLoginUserId());
        }
        return workOrderMapper.selectById(id);
    }

    /**
     * 校验工单存在
     * @param id
     */
    private void validateSubscribeExists(Long id) {
        if (workOrderMapper.selectById(id) == null) {
            throw exception(WORK_ORDER_NOT_EXISTS);
        }
    }

    /**
     * 获取工单
     * @param baseWorkOrderVO
     * @return
     * @param <T>
     */
    @Override
    public <T extends BaseWorkOrderResVO> T getWorkOrderFile(T baseWorkOrderVO) {
        if (ObjectUtil.isEmpty(baseWorkOrderVO) || ObjectUtil.isEmpty(baseWorkOrderVO.getId()) ){
            throw exception(WORK_ORDER_NOT_EXISTS);
        }
        List<WorkOrderFileDO> workOrderFileDOS = workOrderFileMapper.selectList(
                new QueryWrapper<WorkOrderFileDO>().eq("work_order_id", baseWorkOrderVO.getId()));
        List<String> fileList = workOrderFileDOS.stream()
                .map(WorkOrderFileDO::getFile)
                .collect(Collectors.toList());
        baseWorkOrderVO.setFileUrls(fileList);
        return baseWorkOrderVO;
    }

    /**
     * 更新工单
     * @param updateReqVO
     * @return
     */
    @Override
    public int updateWorkOrder(WorkOrderUpdateReqVO updateReqVO) {
        WorkOrderDO workOrderDO = new WorkOrderDO();
        BeanUtil.copyProperties(updateReqVO,workOrderDO);
        int i = workOrderMapper.updateById(workOrderDO);
        return i;
    }
}