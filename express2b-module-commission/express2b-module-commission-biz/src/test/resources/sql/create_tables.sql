-- 将该建表 SQL 语句，添加到 express2b-module-commission-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "commission_detail" (
                                                   "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                   "order_id" bigint NOT NULL,
                                                   "order_no" varchar NOT NULL,
                                                   "target_id" bigint NOT NULL,
                                                   "target_type" int NOT NULL,
                                                   "amount" int NOT NULL,
                                                   "status" int NOT NULL,
                                                   "reason" varchar,
                                                   "calculate_time" varchar NOT NULL,
                                                   "creator" varchar DEFAULT '',
                                                   "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                   "updater" varchar DEFAULT '',
                                                   "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                   "deleted" bit NOT NULL DEFAULT FALSE,
                                                   "tenant_id" bigint NOT NULL DEFAULT 0,
                                                   PRIMARY KEY ("id")
    ) COMMENT '分润明细表';
-- 将该建表 SQL 语句，添加到 express2b-module-commission-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "commission_summary" (
                                                    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                    "target_id" bigint NOT NULL,
                                                    "summary_date" varchar NOT NULL,
                                                    "total_amount" int NOT NULL,
                                                    "detail_count" int NOT NULL,
                                                    "creator" varchar DEFAULT '',
                                                    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                    "updater" varchar DEFAULT '',
                                                    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                    "deleted" bit NOT NULL DEFAULT FALSE,
                                                    "tenant_id" bigint NOT NULL DEFAULT 0,
                                                    PRIMARY KEY ("id")
    ) COMMENT '分润汇总表';