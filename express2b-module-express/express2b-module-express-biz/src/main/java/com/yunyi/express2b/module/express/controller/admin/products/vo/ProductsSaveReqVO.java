package com.yunyi.express2b.module.express.controller.admin.products.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 物品信息新增/修改 Request VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductsSaveReqVO {

    @Schema(description = "物品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24345")
    private Long id;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14678")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "物品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "物品名称不能为空")
    private String name;

    @Schema(description = "物品重量(克/g)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "物品重量(克/g)不能为空")
    private Integer weight;

    @Schema(description = "物品分类", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "物品分类不能为空")
    private String category;

    @Schema(description = "长度(cm)")
    private Integer length;

    @Schema(description = "宽度(cm)")
    private Integer width;

    @Schema(description = "高度(cm)")
    private Integer height;

    @Schema(description = "物品详细描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    private String description;

    @Schema(description = "物品价格", example = "3710")
    private Integer price;

    @Schema(description = "物品数量", example = "3710")
    private Integer quantity;
}