package com.yunyi.express2b.module.crm.controller.app.v1.personal.address;

import com.yunyi.express2b.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.security.core.util.CrmSecurityFrameworkUtils;
import com.yunyi.express2b.module.crm.controller.admin.address.vo.AddressPageReqVO;
import com.yunyi.express2b.module.crm.controller.admin.address.vo.AddressRespVO;
import com.yunyi.express2b.module.crm.controller.admin.address.vo.AddressSaveReqVO;
import com.yunyi.express2b.module.crm.controller.app.v1.personal.address.vo.AddressDefaultReqVO;
import com.yunyi.express2b.module.crm.controller.app.v1.personal.address.vo.AddressFuzzyQueryVO;
import com.yunyi.express2b.module.crm.controller.app.v1.personal.address.vo.AddressUploadReqVO;
import com.yunyi.express2b.module.crm.controller.app.v1.personal.address.vo.DeleteAddressReqVO;
import com.yunyi.express2b.module.crm.dal.dataobject.address.AddressDO;
import com.yunyi.express2b.module.crm.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.crm.service.address.AddressService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.error;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

/**
 * app-用户地址
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/26 14:13
 */
@Tag(name = "app-用户地址")
@RestController
@RequestMapping("/v1/personal/address")
@Slf4j
public class PersonalAddressController {
    @Resource
    private AddressService addressService;

    @GetMapping("/get-address")
    @Operation(summary = "获得地址簿管理分页")
    public CommonResult<PageResult<AddressRespVO>> getAddressPage(@Valid AddressPageReqVO pageReqVO) {
        pageReqVO.setMemberId(CrmSecurityFrameworkUtils.getLoginUserId());
        PageResult<AddressDO> pageResult = addressService.getAddressPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AddressRespVO.class));
    }
    @PostMapping("/create-list")
    @Operation(summary = "创建地址簿管理")
    public CommonResult<List<AddressRespVO>> createAddressBatch(@Valid @RequestBody List<AddressSaveReqVO> addresses) {
        log.debug("createAddressBatch:{}", addresses);
        return success(addressService.createAddressBatch(addresses));
    }

    //修改个人地址
    @Operation(summary = "修改个人地址")
    @PostMapping("/update-address")
    public CommonResult<Boolean> appupdateAddress(@Valid  @RequestBody AddressUploadReqVO uploadReqVO) {
        addressService.appupdateAddress(uploadReqVO);
        return success(true);
    }

    //删除个人地址
    @Operation(summary = "删除个人地址")
    @PostMapping("/delete-address")
    public CommonResult<Boolean> deleteAddress(@RequestBody DeleteAddressReqVO addressVO) {
        AddressDO address = addressService.getAddress(addressVO.getId());
        if(address == null) {
            return error(ErrorCodeConstants.ADDRESS_NOT_EXISTS);
        }
        if(!CrmSecurityFrameworkUtils.getLoginUserId().equals(address.getMemberId())) {
            return error(GlobalErrorCodeConstants.FORBIDDEN);
        }
        addressService.deleteAddress(addressVO.getId());
        return success(true);
    }

    @Operation(summary = "批量删除个人地址")
    @PostMapping("/del-address-by-ids")
    public CommonResult<Integer> delAddressByIds(@RequestParam("ids") List<Long> ids){
        List<AddressDO> addressDOS = addressService.selectAddressByIds(ids);
        // 校验addressDOS中的memberId 与 CrmSecurityFrameworkUtils.getLoginUserId()一致，否则异常。
        addressDOS.forEach(address -> {
            if (!CrmSecurityFrameworkUtils.getLoginUserId().equals(address.getMemberId())) {
                throw exception(GlobalErrorCodeConstants.FORBIDDEN);
            }
        });
        return success(addressService.delAddressByIdsAndMember(ids, CrmSecurityFrameworkUtils.getLoginUserId()));
    }

    @Operation(summary = "修改默认寄件地址或者收件地址")
    @PostMapping("/default")
    public CommonResult<Boolean> updateDefaultAddress(@RequestBody @Valid AddressDefaultReqVO addressReqVo) {
        return success(addressService.updateDefaultAddress(addressReqVo, CrmSecurityFrameworkUtils.getLoginUserId()));
    }

    @GetMapping("/fuzzy-search")
    @Operation(summary = "模糊查询地址", description = "支持对手机号和姓名进行模糊查询，查询字符长度至少为2")
    public CommonResult<PageResult<AddressRespVO>> fuzzySearchAddress(@Valid AddressFuzzyQueryVO queryVO) {
        // 获取当前登录用户ID，只查询当前用户的地址
        Long memberId = CrmSecurityFrameworkUtils.getLoginUserId();

        // 调用服务执行查询
        PageResult<AddressDO> pageResult = addressService.fuzzySearchAddress(queryVO, memberId);

        // 转换结果
        return success(BeanUtils.toBean(pageResult, AddressRespVO.class));
    }
}
