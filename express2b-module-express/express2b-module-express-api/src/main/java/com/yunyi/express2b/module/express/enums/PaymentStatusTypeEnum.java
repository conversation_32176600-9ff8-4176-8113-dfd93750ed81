package com.yunyi.express2b.module.express.enums;

import com.yunyi.express2b.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 支付状态枚举
 */
@Getter
@AllArgsConstructor
public enum PaymentStatusTypeEnum implements ArrayValuable<String> {
    SUCCESS("SUCCESS","支付成功"),
    REFUND("REFUND","转入退款"),
    NOTPAY("NOTPAY","未支付"),
    CLOSED("CLOSED","已关闭"),
    CANCEL("CANCEL", "已撤销"), // 新增已撤销状态
    PAYING("PAYING", "用户支付中"), // 新增用户支付中状态
    PAY_FAIL("PAY_FAIL", "支付失败"), // 新增支付失败状态
    PAY_EXCEPTION("PAY_EXCEPTION", "支付异常"); // 新增支付异常状态

    private final String status;

    private final String name;

    public static final String[] ARRAYS = Arrays.stream(values()).map(PaymentStatusTypeEnum::getStatus).toArray(String[]::new);

    @Override
    public String[] array() {
        return ARRAYS;
    }
}