package com.yunyi.express2b.module.system.framework.web.config;

import com.yunyi.express2b.framework.swagger.config.Express2bSwaggerAutoConfiguration;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * system 模块的 web 组件的 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class SystemWebConfiguration {

    /**
     * system 模块的 API 分组
     */
    @Bean
    public GroupedOpenApi systemGroupedOpenApi() {
        return Express2bSwaggerAutoConfiguration.buildGroupedOpenApi("system");
    }

}
