package com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceorderrelation;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 发票订单关联 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InvoiceOrderRelationVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "8470")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "发票ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18102")
    @ExcelProperty("发票ID")
    private Long invoiceId;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单编号")
    private String orderNo;

    @Schema(description = "订单金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单金额")
    private Double orderAmount;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}