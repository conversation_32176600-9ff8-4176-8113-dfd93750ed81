package com.yunyi.express2b.module.express.controller.app.v1.price;

import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.module.express.controller.app.v1.price.vo.*;
import com.yunyi.express2b.module.express.service.price.PriceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 价格管理
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/2 下午4:07
 */
@Tag(name = "用户app - 价格管理")
@RestController
@RequestMapping("/v1/express/pricing")
@Validated
@RequiredArgsConstructor
public class PriceController {
    private final PriceService priceService;

    @PostMapping("/express")
    @Operation(summary = "运价查询")
    @PermitAll
    public CommonResult<List<PriceQueryRespVO>> queryExpressPrice(@Valid @RequestBody PriceQueryReqVO priceQueryReqVO) {
        return CommonResult.success(priceService.queryExpressPrice(priceQueryReqVO));
    }

    @PostMapping("/list")
    @Operation(summary = "寄件价格表")
    @PermitAll
    public CommonResult<List<PriceListRespVO>> getPriceList(@Valid @RequestBody PriceListReqVO priceListReqVo) {
        List<PriceListRespVO> priceList = priceService.getPriceList(priceListReqVo);
        return CommonResult.success(priceList);
    }

    @GetMapping("/volume-tier")
    @Operation(summary = "日发货量阶梯")
    @PermitAll
    public CommonResult<List<VolumeTierRespVo>> getVolumeTier(@RequestParam("brandKey") @NotNull String brandKey) {
        List<VolumeTierRespVo> volumeTier = priceService.getVolumeTier(brandKey);
        return CommonResult.success(volumeTier);
    }


}
