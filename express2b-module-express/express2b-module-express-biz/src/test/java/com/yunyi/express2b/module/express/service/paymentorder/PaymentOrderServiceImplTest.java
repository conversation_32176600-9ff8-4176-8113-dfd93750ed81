package com.yunyi.express2b.module.express.service.paymentorder;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.module.express.controller.admin.paymentorder.vo.PaymentOrderPageReqVO;
import com.yunyi.express2b.module.express.controller.admin.paymentorder.vo.PaymentOrderSaveReqVO;
import com.yunyi.express2b.module.express.controller.app.v1.paymenyorder.vo.PayOrderReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.paymentorder.PaymentOrderDO;
import com.yunyi.express2b.module.express.dal.mysql.paymentorder.PaymentOrderMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import static com.yunyi.express2b.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static com.yunyi.express2b.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertPojoEquals;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertServiceException;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomLongId;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomPojo;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.PAYMENT_ORDER_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link PaymentOrderServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(PaymentOrderServiceImpl.class)
public class PaymentOrderServiceImplTest extends BaseDbUnitTest {

    @Resource
    private PaymentOrderServiceImpl paymentOrderService;

    @Resource
    private PaymentOrderMapper paymentOrderMapper;

    @Test
    public void testCreatePaymentOrder_success() {
        // 准备参数
        PaymentOrderSaveReqVO createReqVO = randomPojo(PaymentOrderSaveReqVO.class).setId(null);

        // 调用
        Long paymentOrderId = paymentOrderService.createPaymentOrder(createReqVO);
        // 断言
        assertNotNull(paymentOrderId);
        // 校验记录的属性是否正确
        PaymentOrderDO paymentOrder = paymentOrderMapper.selectById(paymentOrderId);
        assertPojoEquals(createReqVO, paymentOrder, "id");
    }

    @Test
    public void testUpdatePaymentOrder_success() {
        // mock 数据
        PaymentOrderDO dbPaymentOrder = randomPojo(PaymentOrderDO.class);
        paymentOrderMapper.insert(dbPaymentOrder);// @Sql: 先插入出一条存在的数据
        // 准备参数
        PaymentOrderSaveReqVO updateReqVO = randomPojo(PaymentOrderSaveReqVO.class, o -> {
            o.setId(dbPaymentOrder.getId()); // 设置更新的 ID
        });

        // 调用
        paymentOrderService.updatePaymentOrder(updateReqVO);
        // 校验是否更新正确
        PaymentOrderDO paymentOrder = paymentOrderMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, paymentOrder);
    }

    @Test
    public void testUpdatePaymentOrder_notExists() {
        // 准备参数
        PaymentOrderSaveReqVO updateReqVO = randomPojo(PaymentOrderSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> paymentOrderService.updatePaymentOrder(updateReqVO), PAYMENT_ORDER_NOT_EXISTS);
    }

    @Test
    public void testDeletePaymentOrder_success() {
        // mock 数据
        PaymentOrderDO dbPaymentOrder = randomPojo(PaymentOrderDO.class);
        paymentOrderMapper.insert(dbPaymentOrder);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPaymentOrder.getId();

        // 调用
        paymentOrderService.deletePaymentOrder(id);
       // 校验数据不存在了
       assertNull(paymentOrderMapper.selectById(id));
    }

    @Test
    public void testDeletePaymentOrder_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> paymentOrderService.deletePaymentOrder(id), PAYMENT_ORDER_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPaymentOrderPage() {
       // mock 数据
       PaymentOrderDO dbPaymentOrder = randomPojo(PaymentOrderDO.class, o -> { // 等会查询到
           o.setTransactionNo(null);
           o.setOutTransactionNo(null);
           o.setOutOutTransactionNo(null);
           o.setOrderId(null);
           o.setAmount(null);
           o.setStatus(null);
           o.setPaymentMethod(null);
           o.setContent(null);
           o.setFee(null);
           o.setPaymentNumber(null);
           o.setPaymentSubmitTime(null);
           o.setLastStatusUpdateTime(null);
           o.setLastRefundTime(null);
           o.setReconciliationId(null);
           o.setReconciliationTime(null);
           o.setCreateTime(null);
           o.setIp(null);
           o.setClientId(null);
           o.setBody(null);
           o.setMemberId(null);
       });
       paymentOrderMapper.insert(dbPaymentOrder);
       // 测试 transactionNo 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setTransactionNo(null)));
       // 测试 outTransactionNo 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setOutTransactionNo(null)));
       // 测试 outOutTransactionNo 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setOutOutTransactionNo(null)));
       // 测试 orderId 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setOrderId(null)));
       // 测试 amount 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setAmount(null)));
       // 测试 status 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setStatus(null)));
       // 测试 paymentMethod 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setPaymentMethod(null)));
       // 测试 content 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setContent(null)));
       // 测试 fee 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setFee(null)));
       // 测试 paymentNumber 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setPaymentNumber(null)));
       // 测试 paymentSubmitTime 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setPaymentSubmitTime(null)));
       // 测试 lastStatusUpdateTime 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setLastStatusUpdateTime(null)));
       // 测试 lastRefundTime 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setLastRefundTime(null)));
       // 测试 reconciliationId 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setReconciliationId(null)));
       // 测试 reconciliationTime 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setReconciliationTime(null)));
       // 测试 createTime 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setCreateTime(null)));
       // 测试 ip 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setIp(null)));
       // 测试 clientId 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setClientId(null)));
       // 测试 body 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setBody(null)));
       // 测试 memberId 不匹配
       paymentOrderMapper.insert(cloneIgnoreId(dbPaymentOrder, o -> o.setMemberId(null)));
       // 准备参数
       PaymentOrderPageReqVO reqVO = new PaymentOrderPageReqVO();
       reqVO.setTransactionNo(null);
       reqVO.setOutTransactionNo(null);
       reqVO.setOutOutTransactionNo(null);
       reqVO.setOrderId(null);
       reqVO.setAmount(null);
       reqVO.setStatus(null);
       reqVO.setPaymentMethod(null);
       reqVO.setContent(null);
       reqVO.setFee(null);
       reqVO.setPaymentNumber(null);
       reqVO.setPaymentSubmitTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setLastStatusUpdateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setLastRefundTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setReconciliationId(null);
       reqVO.setReconciliationTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setIp(null);
       reqVO.setClientId(null);
       reqVO.setBody(null);
       reqVO.setMemberId(null);

       // 调用
       PageResult<PaymentOrderDO> pageResult = paymentOrderService.getPaymentOrderPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPaymentOrder, pageResult.getList().get(0));
    }
    @Test
    public  void testPay(){
        PayOrderReqVO payOrderReqVO=PayOrderReqVO.builder()
                .orderId(355L)
                .amount(13)
                .paymentMethod("微信")

                .build();
    }

}