package com.yunyi.express2b.module.express.service.brandscheduledconfig;

import cn.hutool.core.util.ObjectUtil;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.module.express.controller.admin.brandscheduledconfig.vo.BrandScheduledConfigPageReqVO;
import com.yunyi.express2b.module.express.controller.admin.brandscheduledconfig.vo.BrandScheduledConfigSaveReqVO;
import com.yunyi.express2b.module.express.controller.admin.orderscheduledtime.vo.AvailableDate;
import com.yunyi.express2b.module.express.controller.admin.orderscheduledtime.vo.TimeSlot;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import com.yunyi.express2b.module.express.dal.dataobject.brandscheduledconfig.BrandScheduledConfigDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;

import com.yunyi.express2b.module.express.dal.mysql.brandscheduledconfig.BrandScheduledConfigMapper;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.*;

/**
 * 快递品牌预约配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BrandScheduledConfigServiceImpl implements BrandScheduledConfigService {

    @Resource
    private BrandScheduledConfigMapper brandScheduledConfigMapper;

    /**
     * 创建
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Long createBrandScheduledConfig(BrandScheduledConfigSaveReqVO createReqVO) {
        // 插入
        BrandScheduledConfigDO brandScheduledConfig = BeanUtils.toBean(createReqVO, BrandScheduledConfigDO.class);
        brandScheduledConfigMapper.insert(brandScheduledConfig);
        // 返回
        return brandScheduledConfig.getId();
    }

    /**
     * 更新
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateBrandScheduledConfig(BrandScheduledConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateBrandScheduledConfigExists(updateReqVO.getId());
        // 更新
        BrandScheduledConfigDO updateObj = BeanUtils.toBean(updateReqVO, BrandScheduledConfigDO.class);
        brandScheduledConfigMapper.updateById(updateObj);
    }

    /**
     * 删除
     * @param id 编号
     */
    @Override
    public void deleteBrandScheduledConfig(Long id) {
        // 校验存在
        validateBrandScheduledConfigExists(id);
        // 删除
        brandScheduledConfigMapper.deleteById(id);
    }


    /**
     * 校验品牌预约配置存在
     * @param id
     */
    private void validateBrandScheduledConfigExists(Long id) {
        if (brandScheduledConfigMapper.selectById(id) == null) {
            throw exception(BRAND_SCHEDULED_CONFIG_NOT_EXISTS);
        }
    }

    /**
     * 获取品牌预约时间
     *
     * @return
     */
    @Override
    public CommonResult<List<AvailableDate>> getBrandTime(String brandKey) {
        // 获取系统当前时间
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        List<AvailableDate> availableDateList = new ArrayList<>();
        BrandScheduledConfigDO brandScheduledConfigDO = brandScheduledConfigMapper.selectTime(brandKey);

        if (brandScheduledConfigDO == null) {
            return CommonResult.success(new ArrayList<>()); // 返回空列表
        }

        // 检查并设置默认值
        Integer scheduledDays = brandScheduledConfigDO.getScheduledDays();
        LocalTime scheduledTimeStart = brandScheduledConfigDO.getScheduledTimeStart();
        LocalTime scheduledTimeEnd = brandScheduledConfigDO.getScheduledTimeEnd();
        Integer intervalMinutes = brandScheduledConfigDO.getIntervalMinutes();

        if (scheduledDays == null || scheduledDays <= 0) {
            scheduledDays = 7; // 默认 7 天
        }
        if (scheduledTimeStart == null) {
            scheduledTimeStart = LocalTime.of(9, 0); // 默认 09:00
        }
        if (scheduledTimeEnd == null) {
            scheduledTimeEnd = LocalTime.of(17, 0); // 默认 17:00
        }
        if (intervalMinutes == null || intervalMinutes <= 0) {
            intervalMinutes = 60; // 默认 60分钟
        }

        int intervalSeconds = intervalMinutes * 60;
        int count = (scheduledTimeEnd.toSecondOfDay() - scheduledTimeStart.toSecondOfDay()) / intervalSeconds;

        for (int i = 1; i <= scheduledDays; i++) {
            AvailableDate availableDate = new AvailableDate();

            // 修复日期格式问题
            LocalDate localDate = LocalDate.now().plusDays(i);
            availableDate.setDate(localDate.format(DateTimeFormatter.ISO_LOCAL_DATE)); // 使用 LocalDate 的 toString 方法生成标准格式

            List<TimeSlot> timeSlots = new ArrayList<>();
            for (int j = 0; j < count; j++) {
                TimeSlot timeSlot = new TimeSlot();

                LocalTime startTime = scheduledTimeStart.plusMinutes(j * intervalMinutes);
                LocalTime endTime = startTime.plusMinutes(intervalMinutes);
                timeSlot.setStart(startTime.format(DateTimeFormatter.ofPattern("HH:mm"))); // 格式化为 HH:mm
                timeSlot.setEnd(endTime.format(DateTimeFormatter.ofPattern("HH:mm"))); // 格式化为 HH:mm
                timeSlots.add(timeSlot);
            }
            availableDate.setTimeSlots(timeSlots);

            availableDateList.add(availableDate);
        }
        System.out.println(availableDateList);

        return CommonResult.success(availableDateList);
    }


    /**
     * 获得快递品牌预约配置分页
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<BrandScheduledConfigDO> getBrandScheduledConfigPage(BrandScheduledConfigPageReqVO pageReqVO) {
        return brandScheduledConfigMapper.selectPage(pageReqVO);
    }

    /**
     * 获取所有品牌的调度配置信息
     * @return
     */
    @Override
    public CommonResult<List<BrandScheduledConfigDO>> getAllBrandS() {
        return brandScheduledConfigMapper.getAllBrandS();


    }

    /**
     * 获取指定品牌的调度配置信息
     * @param id 品牌的唯一键值，用于标识特定的品牌
     * @return
     */
    @Override
    public CommonResult<BrandScheduledConfigDO> getBrandScheduledConfig(Integer id) {
        BrandScheduledConfigDO brandScheduledConfigDO = brandScheduledConfigMapper.selectById(id);
        if (ObjectUtil.isEmpty(brandScheduledConfigDO)){
            throw exception(ErrorCodeConstants.BRAND_SCHEDULED_CONFIG_NOT_EXISTS);
        }
        return CommonResult.success(brandScheduledConfigDO);
    }
}
