package com.yunyi.express2b.module.express.controller.app.v1.order.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/7 19:05
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = false)
public class AddressImportReqVO {
    /**
     * 姓名
     */
    @ExcelProperty("收件人姓名")
    private String name;

    /**
     * 联系电话
     */
    @ExcelProperty("收件人联系方式")
    private String phone;

    /**
     * 详细地址（街道级）
     */
    @ExcelProperty("收件人地址")
    private String address;

    /**
     * 物品名称
     * <p>
     * 对应Excel表格中"物品名称"列，用于记录物品的标准命名
     * 示例值：电子产品/服装/书籍等具体物品称谓
     */
    @ExcelProperty("物品名称")
    private String itemName;

    /**
     * 物品重量（单位：公斤）
     * <p>
     * 对应Excel表格中"重量"列，默认值为1公斤
     * 特殊说明：当单元格为空时会自动填充默认值，支持小数格式
     */
    @ExcelProperty("重量（不填默认1公斤）")
    private String itemWeight;

    /**
     * 物品声明价值（单位：人民币元）
     * <p>
     * 对应Excel表格中"物品声明价值"列，用于海关申报等场景
     * 要求：必须为有效数值，不支持货币符号或文字描述
     */
    @ExcelProperty("物品声明价值（元）")
    private String itemValue;

    /**
     * 包装备注信息
     * <p>
     * 对应Excel表格中"备注"列，记录特殊包装要求
     * 典型内容：易碎品/防潮处理/特殊加固等说明
     */
    @ExcelProperty("备注")
    private String packagingDetails;

    /**
     * 所属城市
     */
    private String city;

    /**
     * 所属城市
     */
    private String cityCode;

    /**
     * 所属区/县
     */
    private String county;

    /**
     *
     * 区县编码
     */
    private String countryCode;

    /**
     *
     * code值
     */
    private String adccode;

}
