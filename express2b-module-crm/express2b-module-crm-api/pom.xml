<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.yunyi.express2b</groupId>
        <artifactId>express2b-module-crm</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>express2b-module-crm-api</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>

    </description>

    <dependencies>
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-common</artifactId>
        </dependency>

        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

</project>
