---
description: 
globs: 
alwaysApply: true
---
## 概要设计文档详细要求:

### 文档需要包含以下核心章节：

1. 模块划分 (Module Division):

   - 功能模块划分：根据业务功能或技术职责进行清晰划分。

   - 模块关系：说明模块间的依赖和交互方式（可辅以图示，使用 Mermaid 语法）。

   - 模块职责：详细描述各模块功能和边界。

2. 数据对象使用规范 (Data Object Usage Specification):

   - 清晰定义和规范使用以下三种数据对象：

     - 领域对象 (Domain Object / Entity, DO): 说明其定义、严格的使用要求（仅限模块内部使用，严禁跨模块和在 Controller 层使用）、以及在文档中需要说明的核心概念和原则（无需全部定义）。

     - 数据传输对象 (Data Transfer Object, DTO): 说明其定义、严格的使用要求（仅用于模块与模块之间的接口调用参数和返回值）、以及在文档中必须定义出关键模块间接口所使用的 DTO 的结构（包含主要字段和说明）。

     - 视图对象 (View Object, VO): 说明其定义、严格的使用要求（用于同一模块内 Service 层与 Controller 层之间，以及后端与前端之间的数据传输）、以及在文档中应说明的作用、使用场景，并对于关键 Service 接口返回的 VO，应定义其结构。

3. 接口设计 (Interface Design):

   - 3.1. 模块间接口:

     - 文档化格式要求: 对于每一个重要的模块间接口，文档中应包含以下信息：

       - 接口名称 (Interface Name): 接口的完整名称。

       - 方法名称 (Method Name): 接口中方法的名称。

       - 功能描述 (Description): 简要说明该方法的作用。

       - 输入参数 (Parameters): 列出方法的输入参数，每个参数包含：

         - 参数名 (Parameter Name)

         - 参数类型 (Parameter Type): 必须是已定义的 DTO 或基础数据类型。

         - 是否必须 (Required)

         - 描述 (Description)

       - 返回值 (Return Value): 说明方法的返回值，包含：

         - 返回值类型 (Return Type): 必须是已定义的 DTO 或基础数据类型，或 void。

         - 描述 (Description)

       - 可能抛出的异常 (Possible Exceptions): 列出该方法可能抛出的自定义业务异常或运行时异常及其说明。

       - 接口定义位置 (Definition Location): 建议的接口在代码中的位置。

   - 3.2. 对外接口 (Controller 设计):

     - 文档化格式要求: 对于每一个对外 API 接口，文档中应包含以下信息：

       - 接口分类: 标明是面向终端用户的前端接口(`app-api`)还是面向管理系统的后端接口(`admin-api`)。
       
       - 包路径: 
         - 前端接口: `com.yunyi.express2b.module.<module-name>.controller.app.[v1|v2]`
         - 后端接口: `com.yunyi.express2b.module.<module-name>.controller.admin`

       - 接口路径 (API Path): 接口的相对URL路径（无需包含`app-api`或`admin-api`前缀，由框架统一控制）。
         - URL地址不允许出现大写字符，只能使用小写字符、下划线和数字。
         - 应遵循RESTful风格，使用名词复数表示资源集合，名词单数+ID表示具体资源。

       - HTTP 方法 (HTTP Method): 使用的 HTTP 方法 (GET, POST, PUT, DELETE, PATCH 等)。

       - 功能描述 (Description): 简要说明该 API 的作用。

       - 请求参数 (Request Parameters):

         - 路径参数 (Path Variables): 名称、类型、是否必须、描述。

         - 查询参数 (Query Parameters): 名称、类型、是否必须、描述。

         - 请求体 (Request Body) (若有):

           - 请求体类型 (Request Body Type) (例如：JSON)

           - 数据结构 (Data Structure): 必须引用已定义的 VO 或 DTO 结构，并列出主要字段、类型、是否必须、描述。

       - 响应 (Response):

       - 响应状态码 (HTTP Status Codes): 列出可能返回的 HTTP 状态码及其含义。

       - 响应体 (Response Body):

       - 响应体类型 (Response Body Type): 统一使用JSON格式

       - 数据结构 (Data Structure): 统一使用`CommonResult<T>`响应结构，其中T必须引用已定义的VO结构，并列出主要字段、类型、描述。

           ```java
           // CommonResult<T>主要属性
           code: Integer  // 状态码，成功时为预定义成功代码，失败时为具体错误代码
           data: T        // 泛型数据字段，仅在请求成功时存在
           msg: String    // 描述错误信息，成功时不包含此字段
           ```

       - 错误处理 (Error Handling): 说明当发生业务错误或系统错误时使用的错误码，必须从以下两类错误码中引用：
         - 项目级/共享错误码：定义在`com.yunyi.express2b.framework.common.exception.GlobalErrorCodeConstants`
         - 模块特有错误码：定义在`com.yunyi.express2b.module.<module-name>.api.enums.<ModuleName>ErrorCodeConstants`

       - 认证/授权要求 (Authentication/Authorization): 说明访问该接口是否需要认证，需要哪些权限。

   - 3.3. 接口契约:
     - 强调接口定义的稳定性以及如何进行版本管理。
     - 对于前端接口，说明版本控制策略（如在app目录下使用v1、v2等子目录）。
     - 强调所有接口必须符合RESTful设计原则并实现接口幂等性。
     - 提到所有Controller层应使用Swagger注解自动生成API文档。

4. 数据流和交互 (Data Flow and Interaction):

   - 关键业务流程数据流图：描述核心流程中数据（必须标明 DO/DTO/VO 类型）在组件/模块间流动（使用 Mermaid 语法）。

   - 交互时序图：复杂交互过程的时序图，必须清晰标明传递的数据对象类型 (DO/DTO/VO)（使用 Mermaid 语法）。

5. 数据库设计概要 (Database Design Overview):

   - 主要数据表：列出关键表及其主要字段。

   - 表关系：说明关键表间关系。

   - 数据访问策略：简述数据访问层设计。

输出格式与语言要求：

- 语言: 始终使用 中文 进行交流和文档撰写。

- 文档格式: 使用 Markdown 格式输出文档内容。




- 图表绘制 (Mermaid): 如果需要绘制图表（如模块关系图、数据流图、时序图等），必须使用 Mermaid 语法。在 Mermaid 图中，所有的说明内容、中文内容，均需要使用 英文半角双引号 " " 进行包括。