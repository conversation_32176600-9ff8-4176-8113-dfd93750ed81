package com.yunyi.express2b.module.express.dal.dataobject.products;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 物品信息 DO
 *
 * <AUTHOR>
 */
@TableName("express2b_products")
@KeySequence("express2b_products_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductsDO extends BaseDO {

    /**
     * 物品ID
     */
    @TableId
    private Long id;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 物品名称
     */
    private String name;
    /**
     * 物品重量(克/g)
     */
    private Integer weight;
    /**
     * 物品分类
     */
    private String category;
    /**
     * 长度(cm)
     */
    private Integer length;
    /**
     * 宽度(cm)
     */
    private Integer width;
    /**
     * 高度(cm)
     */
    private Integer height;
    /**
     * 物品详细描述
     */
    private String description;
    /**
     * 物品价格
     */
    private Integer price;

    /**
     * 物品数量
     */
    private Integer quantity;

}