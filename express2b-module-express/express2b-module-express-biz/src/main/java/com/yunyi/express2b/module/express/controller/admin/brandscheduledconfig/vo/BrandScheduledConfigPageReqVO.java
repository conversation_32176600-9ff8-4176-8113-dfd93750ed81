package com.yunyi.express2b.module.express.controller.admin.brandscheduledconfig.vo;

import lombok.*;

import java.time.LocalTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
/**
 * 管理后台 快递品牌预约配置分页 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 快递品牌预约配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrandScheduledConfigPageReqVO extends PageParam {

    @Schema(description = "快递品牌标识")
    private String brandKey;

    @Schema(description = "预约天数")
    private Integer scheduledDays;

    @Schema(description = "预约开始时间")
    private LocalTime scheduledTimeStart;

    @Schema(description = "预约结束时间")
    private LocalTime scheduledTimeEnd;

    @Schema(description = "预约间隔分钟数")
    private Integer intervalMinutes;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}