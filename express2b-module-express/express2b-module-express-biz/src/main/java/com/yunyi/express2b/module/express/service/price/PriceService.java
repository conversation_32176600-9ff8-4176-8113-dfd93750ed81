package com.yunyi.express2b.module.express.service.price;

import com.yunyi.express2b.module.express.controller.app.v1.price.vo.*;

import java.util.List;

/**
 * 价格管理service
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/2 下午5:31
 */
public interface PriceService {
    /**
     * 运价查询
     */
    List<PriceQueryRespVO> queryExpressPrice(PriceQueryReqVO priceQueryReqVO);

    /**
     * 寄件价格表
     */
    List<PriceListRespVO> getPriceList(PriceListReqVO priceListReqVo);

    /**
     * 日发货量阶梯
     */
    List<VolumeTierRespVo> getVolumeTier(String brandKey);

    /**
     * 批量运价查询，多收件人批量查询，就等于多收件地址的订单价格查询。
     */
   // List<PriceQueryRespVO> batchQueryExpressPrice(BatchPriceQueryReqVO batchPriceQueryReqVO);
}
