package com.yunyi.express2b.module.pricing.service.pricingrule;

import java.util.*;
import jakarta.validation.*;
import com.yunyi.express2b.module.pricing.controller.admin.pricingrule.vo.*;
import com.yunyi.express2b.module.pricing.dal.dataobject.pricingrule.PricingRuleDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;

/**
 * 代理商定价（终端零售价）规则 Service 接口
 *
 * <AUTHOR>
 */
public interface PricingRuleService {

    /**
     * 创建代理商定价（终端零售价）规则
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRule(@Valid PricingRuleSaveReqVO createReqVO);

    /**
     * 更新代理商定价（终端零售价）规则
     *
     * @param updateReqVO 更新信息
     */
    void updateRule(@Valid PricingRuleSaveReqVO updateReqVO);

    /**
     * 删除代理商定价（终端零售价）规则
     *
     * @param id 编号
     */
    void deleteRule(Long id);

    /**
     * 获得代理商定价（终端零售价）规则
     *
     * @param id 编号
     * @return 代理商定价（终端零售价）规则
     */
    PricingRuleDO getRule(Long id);

    /**
     * 获得代理商定价（终端零售价）规则分页
     *
     * @param pageReqVO 分页查询
     * @return 代理商定价（终端零售价）规则分页
     */
    PageResult<PricingRuleDO> getRulePage(PricingRulePageReqVO pageReqVO);

}