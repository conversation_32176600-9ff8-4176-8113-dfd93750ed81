package com.yunyi.express2b.module.express.controller.admin.refundcallback.vo;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 退款回调请求vo
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/15 下午2:37
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundCallBackReqVO {
    /**
     * 退款单号
     */
    @NotBlank(message = "退款单号不能为空")
    private String refundOrderSn;

    /**
     * 第三方订单号
     */
    @NotBlank(message = "第三方订单号不能为空")
    private String thirdOrderSn;

    /**
     * 退款金额，单位：分
     */
    @Min(value = 1, message = "退款金额必须大于0")
    private Integer amount;
}
