package com.yunyi.express2b.module.express.service.countpaymoney.dto;

import com.alibaba.fastjson.annotation.JSONField;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

/**
 * 订单信息dto
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/28 19:53
 */
@Data
@Validated
public class OrderDetailDTO {
    /**
     * 今天已经发生了多少有效订单
     */
    @NotNull
    private Integer todayCount;
    /**
     * 统一登录用户id
     */
    private Long memberId;
    /**
     * 订单id
     */
    @NotNull
    private Long orderId;
    /**
     * 成本价
     */
    @NotNull
    private Integer costAmount;
    /**
     * 零售价
     */
    private Integer retailAmount;
    /**
     * 首重价格
     */
    @NotNull
    private Integer firstPrice;
    /**
     * 续重价格
     */
    @NotNull
    private Integer overPrice;
    /**
     * 阶梯价优惠金额
     */
    private Integer ladderDiscountAmount;

    /**
     * 单次续重
     */
    private Integer overWeight;
    /**
     * 总重量
     */
    private String weight;
    /**
     * 状态变更
     */
    private String status;

    /**
     * 会员等级优惠价格
     */
    private Integer membersLevelDiscountAmount;
    /**
     * 次卡优惠金额
     */
    private Integer subCardDiscountAmount;
    /**
     * 快递公司编码
     */
    private String expressCode;
    /**
     * 优惠总金额
     */
    private Integer discountAmount;
    /**
     * 代理商分佣
     */
    private Integer agentCommission;
    /**
     * 团长分佣
     */
    private Integer groupLeaderCommission;
    /**
     * 投保金额
     */
    private Integer insuredAmount;

    // 成本价格，单位：分
    private Integer costPrice;

    // 平台成本价格，单位：分
    private Integer routePrice;

    // 分润价格，单位：分
    private Integer profit;

}
