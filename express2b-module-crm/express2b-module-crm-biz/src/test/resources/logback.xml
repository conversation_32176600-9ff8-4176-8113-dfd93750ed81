<configuration>
    <!-- 定义控制台输出的 Appender -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%-4relative [%thread] %-5level %logger{35} - %msg %n</pattern>
        </encoder>
    </appender>

 <!-- 关闭根日志 -->
    <root level="OFF"/>

    <logger name="com.yunyi.express2b" level="DEBUG" additivity="false">
        <appender-ref ref="STDOUT" />
    </logger>
</configuration>