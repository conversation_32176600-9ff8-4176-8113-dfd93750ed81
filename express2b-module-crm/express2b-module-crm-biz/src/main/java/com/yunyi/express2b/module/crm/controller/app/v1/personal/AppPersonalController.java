package com.yunyi.express2b.module.crm.controller.app.v1.personal;

import cn.hutool.core.util.StrUtil;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.json.JsonUtils;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.security.config.SecurityProperties;
import com.yunyi.express2b.framework.security.core.util.CrmSecurityFrameworkUtils;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.crm.controller.admin.personalinfo.vo.PersonalInfoRespVO;
import com.yunyi.express2b.module.crm.controller.app.v1.personal.vo.UserLoginRequest;
import com.yunyi.express2b.module.crm.dal.dataobject.personalinfo.PersonalInfoDO;
import com.yunyi.express2b.module.crm.service.apppersonal.AppPersonalService;
import com.yunyi.express2b.module.crm.service.personalinfo.PersonalInfoService;
import com.yunyi.express2b.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import com.yunyi.express2b.module.system.enums.logger.LoginLogTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

/**
 * app-用户控制层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/26 10:34
 */

@Slf4j
@Tag(name = "app - 个人信息")
@RestController
@RequestMapping("/v1/personal")
public class AppPersonalController {
    @Autowired
    private AppPersonalService  appPersonalService;
    @Resource
    private PersonalInfoService personalInfoService;
    @Resource
    private SecurityProperties securityProperties;


    @Operation(summary = "登录")
    @PostMapping("/login")
    @PermitAll
    public CommonResult<OAuth2AccessTokenRespDTO> login(@RequestBody UserLoginRequest loginRequest) {
        log.debug("登录请求参数：{}",JsonUtils.toJsonString(loginRequest));
        return CommonResult.success(appPersonalService.login(loginRequest));
    }
    @PostMapping("/logout")
    @PermitAll
    @Operation(summary = "登出系统")
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        String token = SecurityFrameworkUtils.obtainAuthorization(request,
                securityProperties.getTokenHeader(), securityProperties.getTokenParameter());
        if (StrUtil.isNotBlank(token)) {
            appPersonalService.logout(token, LoginLogTypeEnum.LOGOUT_SELF.getType());
        }
        return success(true);
    }


    @PostMapping("/upgrade")
    @Operation(summary = "登记用户升级信息登记")
    public CommonResult<Boolean> userLevel(@RequestBody  PersonalInfoRespVO personalInfoRespVO) {

        return CommonResult.success(appPersonalService.upUserLevel(personalInfoRespVO));
    }

    @Operation(summary = "获取用户升级审核结果")
    @PostMapping("/get-user-level")
    public CommonResult<String> getUserLevel() {
        return CommonResult.success(appPersonalService.getmsg(CrmSecurityFrameworkUtils.getLoginUserId()));
    }


    /**
     * 修改昵称和头像
     */
    @Operation(summary = "修改头像")
    @PostMapping("/update-head")
    public CommonResult<Boolean> updateHead(@RequestBody PersonalInfoDO personalInfoDO) {
        return CommonResult.success(appPersonalService.updateHead(CrmSecurityFrameworkUtils.getLoginUserId(), personalInfoDO.getAvatar()));
    }

    @Operation(summary = "修改昵称")
    @PostMapping("/update-nickname")
    public CommonResult<Boolean> updateNickname(@RequestBody PersonalInfoDO personalInfoDO) {
        return CommonResult.success(appPersonalService.updateNickname(CrmSecurityFrameworkUtils.getLoginUserId(), personalInfoDO.getMemberName()));
    }

    @Operation(summary = "注销账号")
    @PostMapping("/cancellation")
    public CommonResult<Boolean> cancellation() {
        return CommonResult.success(appPersonalService.deletePersonalInfo(CrmSecurityFrameworkUtils.getLoginUserId()));
    }
    @GetMapping("/get")
    @Operation(summary = "获得个人信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<PersonalInfoRespVO> getPersonalInfo() {
        PersonalInfoDO personalInfo = personalInfoService.getPersonalInfo(CrmSecurityFrameworkUtils.getLoginUserId());
        return success(BeanUtils.toBean(personalInfo, PersonalInfoRespVO.class));
    }
    @GetMapping("/getcompany")
    @Operation(summary = "获得个人企业信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<String> getCompany() {
     return personalInfoService.getCompany(CrmSecurityFrameworkUtils.getLoginUserId());
    }


}
