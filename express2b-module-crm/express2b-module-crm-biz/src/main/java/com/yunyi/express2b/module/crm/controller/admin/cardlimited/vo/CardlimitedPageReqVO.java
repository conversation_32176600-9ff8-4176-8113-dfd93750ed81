package com.yunyi.express2b.module.crm.controller.admin.cardlimited.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 卡种信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CardlimitedPageReqVO extends PageParam {

    @Schema(description = "名称", example = "张三")
    private String cardlimitedName;

    @Schema(description = "零售价格,单位：分")
    private Integer price;

    @Schema(description = "可使用次数")
    private Integer number;

    @Schema(description = "优惠信息描述，html富文本", example = "你说的对")
    private String description;

    @Schema(description = "有效天数")
    private Integer validityPeriod;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}