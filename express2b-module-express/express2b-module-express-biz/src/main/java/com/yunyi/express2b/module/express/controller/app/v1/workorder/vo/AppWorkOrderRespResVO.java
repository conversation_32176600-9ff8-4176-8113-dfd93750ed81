package com.yunyi.express2b.module.express.controller.app.v1.workorder.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yunyi.express2b.module.express.controller.admin.workorder.vo.BaseWorkOrderResVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.yunyi.express2b.framework.excel.core.convert.DictConvert;

@Schema(description = "用户 APP - 工单 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ExcelIgnoreUnannotated
public class AppWorkOrderRespResVO extends BaseWorkOrderResVO {

    @Schema(description = "工单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("工单编号")
    private String workOrderSn;

    @Schema(description = "SSO用户ID", example = "3349")
    @ExcelProperty("SSO用户ID")
    private Long memberId;

    @Schema(description = "关联订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("关联订单编号")
    private String orderSn;

    @Schema(description = "类型", example = "1")
    @ExcelProperty("类型")
    private Integer type;

    @Schema(description = "问题描述")
    @ExcelProperty("问题描述")
    private String passengerQuestion;

    @Schema(description = "工单状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "工单状态", converter = DictConvert.class)
    private Integer workOrderStatus;

    @Schema(description = "客服受理时间")
    @ExcelProperty("客服受理时间")
    private LocalDateTime customerServiceTime;

    @Schema(description = "客服回复")
    @ExcelProperty("客服回复")
    private String customerServiceReply;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "三方平台Id", example = "1")
    private Long tripleId;

    /**
     * 工单类型枚举名称
     */
    @Schema(description = "工单类型枚举名称", example = "1")
    @TableField(exist = false)
    private String typeName;

    /**
     * 工单状态枚举名称
     */
    @Schema(description = "工单状态枚举名称", example = "1")
    @TableField(exist = false)
    private String workOrderStatusName;

}