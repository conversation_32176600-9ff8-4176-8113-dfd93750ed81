package com.yunyi.express2b.module.express.service.order;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.io.FileUtils;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;
import com.yunyi.express2b.framework.security.core.LoginUser;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.crm.api.address.AddressQueryApi;
import com.yunyi.express2b.module.crm.api.address.vo.AddressRespApiVO;
import com.yunyi.express2b.module.crm.api.card.paycard.vo.PayCardVo;
import com.yunyi.express2b.module.crm.api.card.refundcard.RefundCardApi;
import com.yunyi.express2b.module.crm.api.personInfo.PersonInfoApi;
import com.yunyi.express2b.module.crm.api.personInfo.dto.PersonalInfoSaveReqDTO;
import com.yunyi.express2b.module.express.controller.admin.order.dto.OrderCallBackRequestDTO;
import com.yunyi.express2b.module.express.controller.admin.order.dto.WorkbenchInterfaceDTO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.*;
import com.yunyi.express2b.module.express.controller.admin.orderstatus.vo.OrderStatusSaveReqVO;
import com.yunyi.express2b.module.express.controller.admin.paymentorder.vo.UnifiedOrderResponseNew;
import com.yunyi.express2b.module.express.controller.admin.products.vo.ProductsRespVO;
import com.yunyi.express2b.module.express.controller.admin.products.vo.ProductsSaveReqVO;
import com.yunyi.express2b.module.express.controller.admin.refundorder.vo.RefundOrderSaveReqVO;
import com.yunyi.express2b.module.express.controller.app.v1.order.vo.*;
import com.yunyi.express2b.module.express.controller.app.v1.paymenyorder.vo.PayOrderReqVO;
import com.yunyi.express2b.module.express.controller.app.v1.price.vo.PriceQueryReqVO;
import com.yunyi.express2b.module.express.controller.app.v1.price.vo.PriceQueryRespVO;
import com.yunyi.express2b.module.express.controller.app.v1.price.vo.ReceiverPlace;
import com.yunyi.express2b.module.express.dal.dataobject.batchorder.BatchOrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.brand.BrandDO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.paymentorder.PaymentOrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.products.ProductsDO;
import com.yunyi.express2b.module.express.dal.mysql.batchorder.BatchOrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.brand.BrandMapper;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.paymentorder.PaymentOrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.relorderpayment.RelOrderPaymentMapper;
import com.yunyi.express2b.module.express.enums.*;
import com.yunyi.express2b.module.express.enums.social.ExpressLogRecordConstants;
import com.yunyi.express2b.module.express.mapstruct.OrderMapStructMapper;
import com.yunyi.express2b.module.express.mapstruct.ProductMapStructMapper;
import com.yunyi.express2b.module.express.mq.producer.OrderStatusProducer;
import com.yunyi.express2b.module.express.service.countpaymoney.decorator.LadderCountDecorator;
import com.yunyi.express2b.module.express.service.countpaymoney.dto.OrderDetailDTO;
import com.yunyi.express2b.module.express.service.message.SmsService;
import com.yunyi.express2b.module.express.service.order.bo.OrderCallBackRequestBO;
import com.yunyi.express2b.module.express.service.orderstatus.OrderStatusService;
import com.yunyi.express2b.module.express.service.pay.PayService;
import com.yunyi.express2b.module.express.service.pay.RefundService;
import com.yunyi.express2b.module.express.service.price.PriceService;
import com.yunyi.express2b.module.express.service.products.ProductsService;
import com.yunyi.express2b.module.express.service.sendmessage.MessageUtils;
import com.yunyi.express2b.module.express.utils.BatchOrderNumberUtil;
import com.yunyi.express2b.module.express.utils.GenerateOrderNumberUtil;
import com.yunyi.express2b.module.express.utils.OrderConstants;
import com.yunyi.express2b.module.infra.api.config.ConfigApi;
import com.yunyi.framework.api.base.config.OrderConfig;
import com.yunyi.framework.api.login.api.cs.CustomerServicesApi;
import com.yunyi.framework.api.login.api.cs.vo.CustomerServicesRequest;
import com.yunyi.framework.api.login.api.cs.vo.CustomerServicesResponse;
import com.yunyi.framework.api.login.api.discount.DiscountApi;
import com.yunyi.framework.api.login.api.discount.vo.*;
import com.yunyi.framework.api.login.api.image.ImageApi;
import com.yunyi.framework.api.login.api.image.vo.UploadImageRequest;
import com.yunyi.framework.api.login.api.image.vo.UploadImageResponse;
import com.yunyi.framework.api.login.api.message.MessageApi;
import com.yunyi.framework.api.login.api.message.vo.TemplatePageRequest;
import com.yunyi.framework.api.login.api.message.vo.TemplatePageResponse;
import com.yunyi.framework.api.login.api.qrcode.orcodeApi;
import com.yunyi.framework.api.login.api.qrcode.vo.CodeRequest;
import com.yunyi.framework.api.login.api.qrcode.vo.PushRequest;
import com.yunyi.framework.api.login.utils.TransactionNoGenerator;
import com.yunyi.framework.api.logistics.api.express.ExpressApi;
import com.yunyi.framework.api.logistics.api.express.vo.AddressListResponse;
import com.yunyi.framework.api.logistics.api.express.vo.QueryExpressPriceRequest;
import com.yunyi.framework.api.logistics.api.express.vo.QueryExpressPriceResponse;
import com.yunyi.framework.api.logistics.api.express.vo.*;
import com.yunyi.framework.api.logistics.api.order.OrderApi;
import com.yunyi.framework.api.logistics.api.order.vo.CancelOrderRequest;
import com.yunyi.framework.api.logistics.api.order.vo.CancelOrderResponse;
import com.yunyi.framework.api.logistics.api.order.vo.CreateOrderRequest;
import com.yunyi.framework.api.logistics.api.order.vo.CreateOrderResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.alibaba.druid.util.StringUtils.isEmpty;
import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.ORDER_NOT_EXISTS;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.ORDER_STATUS_INVALID;

/**
 * 订单管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class OrderServiceImpl implements OrderService {

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private BatchOrderMapper batchOrderMapper;
    @Resource
    private RefundService refundService;
    @Resource
    private ExpressApi expressApi;
    @Resource
    private OrderStatusService orderStatusService;
    @Resource
    private SmsService smsService;
    @Resource
    private ImageApi imageApi;
    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private ProductsService productsService;

    @Resource
    private OrderApi orderApi;

    @Resource
    private BrandMapper brandMapper;

    @Resource
    private AddressQueryApi addressQueryApi;

    @Resource
    @Lazy
    private PriceService priceService;

    @Resource
    private PayService payService;

    @Autowired
    @Qualifier("myTaskExecutor")
    private Executor executor;

    @Autowired
    @Qualifier("myUpdatePriceExecutor")
    private Executor myUpdatePriceExecutor;

    @Autowired
    @Qualifier("myCancelOrderExecutor")
    private Executor myCancelOrderExecutor;

    @Autowired
    @Qualifier("myUpdateOrderStatusExecutor")
    private Executor myUpdateOrderStatusExecutor;

    @Resource
    private ConfigApi configApi;

    @Resource
    private PaymentOrderMapper paymentOrderMapper;

    @Resource
    OrderConfig orderConfig;
    @Resource
    private MessageApi messageApi;

    @Resource
    private RefundCardApi refundCardApi;

    @Resource
    private MessageUtils messageUtils;


    @Resource
    private DiscountApi discountApi;


    @Value("${yunyi.ssoUserId}")
    //推广平台默认用户id
    private Integer ssoUserId;

    @Value("${yunyi.clientId}")
    private Integer clientId;

    @Value("${yunyi.path}")
    private String path;


    @Resource
    private orcodeApi orcodeApi;

    @Resource
    private CustomerServicesApi customerServicesApi;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private PersonInfoApi personInfoApi;


    /**
     * 订单数 订单付款数 已入账订单数（） 带入账订单数（） 销售额 实时对比数据下方的对比昨日
     *
     * @param workbenchInterfaceDTO
     * @return
     */
    @Override
    public WorkbenchInterfaceDTO workbenchInterface(WorkbenchInterfaceDTO workbenchInterfaceDTO) {
        // 获取今日和昨日的时间范围
        TimeRange todayRange = getTimeRange(LocalDateTime.now());
        TimeRange yesterdayRange = getTimeRange(LocalDateTime.now().minusDays(1));

        // 设置今日时间范围并查询
        workbenchInterfaceDTO.setStartTime(todayRange.start);
        workbenchInterfaceDTO.setEndTime(todayRange.end);
        WorkbenchInterfaceDTO workbenchDay = paymentOrderMapper.getWorkbenchInterface(workbenchInterfaceDTO);

        // 设置昨日时间范围并查询
        workbenchInterfaceDTO.setStartTime(yesterdayRange.start);
        workbenchInterfaceDTO.setEndTime(yesterdayRange.end);
        WorkbenchInterfaceDTO workbenchYesterday = paymentOrderMapper.getWorkbenchInterface(workbenchInterfaceDTO);

        // 创建返回结果对象
        WorkbenchInterfaceDTO result = new WorkbenchInterfaceDTO();

        // 复制基础数据
        copyBaseData(result, workbenchDay);

        // 计算并设置变化比率
        calculateAndSetRatios(result, workbenchDay, workbenchYesterday);

        return result;
    }

    /**
     * 订单推送到统一推广平台
     * @return
     */
    @Override
    public Boolean createOrderToPromo() {
        List<OrderDO> orderDOList = orderMapper.selectPaidOrderList(OrderStatusTypeEnum.PAID_STATUSES);
        log.info("查询已付款的订单数量：{}", orderDOList.size());
        for (OrderDO orderDO : orderDOList) {
            //生成的唯一标识
            String taskId = GenerateOrderNumberUtil.generateTaskId("TS",
                    StringUtils.leftPad(String.valueOf(orderDO.getPersonalId()), 2, '0'),
                    StringUtils.leftPad(String.valueOf(orderDO.getBrandId()), 2, '0'));
            //查当前订单的分润金额
            Integer shareAmount = orderDO.getShareAmount();
            Integer sharePriceInCents = 0;
            if (orderDO.getShareAmount() == null || orderDO.getShareAmount() == 0) {
                //TODO
                log.info("订单分润金额不存在，查询默认分润金额");
                sharePriceInCents = new BigDecimal(configApi.getConfigValueByKey("shareprice"))
                        .multiply(BigDecimal.valueOf(100))
                        .intValueExact();


            }
            Integer profit = new BigDecimal(configApi.getConfigValueByKey("profit"))
                    .multiply(BigDecimal.valueOf(100))
                    .intValueExact();
            CreatOrderRequest creatOrderRequest = new CreatOrderRequest();
            creatOrderRequest.setSsoUserId(Math.toIntExact(orderDO.getPersonalId()));
            creatOrderRequest.setTaskId(taskId);
            creatOrderRequest.setOrderSn(orderDO.getOrderNo());
            creatOrderRequest.setTotalAmount(orderDO.getCostAmount());
            creatOrderRequest.setPayAmount(orderDO.getReceivedAmount());
            creatOrderRequest.setMarkup(sharePriceInCents);
            creatOrderRequest.setProfit(profit);
            creatOrderRequest.setStatus(1);

            try {
                CommonResult<JSONObject> creatorder = discountApi.creatorder(creatOrderRequest);
                if (creatorder != null && creatorder.getData().getString("code").equals(OrderConstants.code)) {
                    return Boolean.TRUE;
                } else if (creatorder != null && creatorder.getData().getString("code").equals(OrderConstants.noUserId)) {
                    log.info("用户不存在，使用默认用户:{}", ssoUserId);
                    creatOrderRequest.setSsoUserId(ssoUserId);
                    CommonResult<JSONObject> creatorder1 = discountApi.creatorder(creatOrderRequest);
                    if (creatorder != null && creatorder.getData().getString("code").equals(OrderConstants.code)) {
                        return Boolean.TRUE;
                    }
                }
            } catch (Exception e) {
                log.error("调用接口失败，订单号：{}", orderDO.getOrderNo(), e);
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 获取在线客服
     * @param request
     * @return
     */
    @LogRecord(type = ExpressLogRecordConstants.ONLINE_CUSTOMER,
            fail = "获取在线客服失败，失败原因：「{{#_errorMsg}}」",
            success = "在线客服信息:{{#customer}}",
            bizNo = "{{#userId}}")
    @Override
    public CustomerServicesResponse getOnlineCustomerData(CustomerServicesRequest request) {
        log.info("【在线客服】获取在线客服数据，请求参数：{}", request);
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        try {
            // 调用服务层接口获取客服信息
            CommonResult<CustomerServicesResponse> result = customerServicesApi.customerServices(request);
            log.info("【在线客服】调用服务层接口获取客服信息，响应数据：{}", result);

            if (result == null) {
                throw exception(ErrorCodeConstants.QUERY_DATA_IS_EMPTY);
            }

            if (result.getCode().equals(OrderConstants.successCode)) {
                log.info("【在线客服】成功获取客服信息，原始响应数据：{}", result.getData());

                // 使用 Jackson 反序列化为 CustomerServicesResponse
                CustomerServicesResponse response = new CustomerServicesResponse();

                @SuppressWarnings("unchecked")
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) result.getData();

                List<CustomerServicesResponse.CustomerServiceData> serviceDataList = dataList.stream()
                        .map(map -> objectMapper.convertValue(map, CustomerServicesResponse.CustomerServiceData.class))
                        .toList();

                response.setData(serviceDataList);
                log.info("【在线客服】解析后的客服数据：{}", response);
                LogRecordContext.putVariable("customer", response);
                return response;
            } else {
                log.warn("【在线客服】服务端返回失败，code: {}, msg: {}", result.getCode(), result.getMsg());
                throw exception(ErrorCodeConstants.ONLINE_CUSTOMER_SERVICE_UNAVAILABLE);
            }

        } catch (Exception e) {
            log.error("【在线客服】调用异常，请求参数：{}", request, e);
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR);
        }
    }

    /**
     * 下载推广海报
     */
    @LogRecord(type = ExpressLogRecordConstants.DOWNLOAD_POSTER,
            fail = "下载海报失败，失败原因：「{{#_errorMsg}}」",
            success = "海报的url:{{#urls}}",
            bizNo = "{{#userId}}")
    @Override
    public List<PosterTemplateVO> downloadPoster() {
        log.info("开始执行下载海报的方法");
        if (SecurityFrameworkUtils.getLoginUserId() == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        CodeRequest codeRequest = new CodeRequest();
        codeRequest.setParam(SecurityFrameworkUtils.getLoginUserId().toString());
        codeRequest.setClientId(clientId);
        codeRequest.setPath(path);

        try {
            CommonResult<JSONObject> result = orcodeApi.orcodePosters(codeRequest);
            log.info("【调用下载海报的接口返回数据】：{}", result);
            if (result != null && OrderConstants.successCode.equals(result.getCode())) {
                JSONObject dataJson = result.getData();
                List<PosterTemplateVO> posterList = new ArrayList<>();
                List<String> urlList = new ArrayList<>();
                if (dataJson != null && dataJson.containsKey("data")) {
                    JSONArray dataArray = dataJson.getJSONArray("data");
                    if (dataArray != null) {


                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject item = dataArray.getJSONObject(i);
                            PosterTemplateVO vo = new PosterTemplateVO();
                            vo.setTemplate(item.getString("template"));
                            vo.setUrl(item.getString("url"));
                            posterList.add(vo);
                            urlList.add(item.getString("url"));
                        }
                    }
                    LogRecordContext.putVariable("urls", urlList);
                }
                return posterList;
            } else {
                log.warn("下载海报接口返回非成功状态码: {}", result != null ? result.getCode() : "null");
                return Collections.emptyList();
            }

        } catch (Exception e) {
            log.error("调用下载海报接口异常", e);
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR, e.getMessage());
        }
    }

    /**
     * 分享小程序
     */
    @Override
    public JSONObject generateApplets() {
        log.info("开始执行生成小程序的方法 ");
        CodeRequest codeRequest = new CodeRequest();
        codeRequest.setParam(SecurityFrameworkUtils.getLoginUserId().toString());
        codeRequest.setClientId(clientId);
        codeRequest.setPath(path);
        CommonResult<JSONObject> result = null;
        try {
            result = orcodeApi.orcodeLink(codeRequest);
            log.info("【调用生成小程序的接口返回数据】：{}", result);

        } catch (Exception e) {
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR);
        }
        return result.getData();
    }

    /**
     * 创建二维码
     */
    @LogRecord(type = ExpressLogRecordConstants.QR_CODE,
            fail = "创建二维码失败，失败原因：「{{#_errorMsg}}」",
            success = "在线客服信息:{{#customer}}",
            bizNo = "{{#userId}}")
    @Override
    public JSONObject createQrCode() {
        log.info("开始执行创建二维码的方法");
        if (SecurityFrameworkUtils.getLoginUserId() == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }
        CodeRequest codeRequest = new CodeRequest();
        codeRequest.setParam("u=" + SecurityFrameworkUtils.getLoginUserId());
        codeRequest.setClientId(clientId);
        codeRequest.setPath(path);
        CommonResult<JSONObject> result = null;
        try {
            result = orcodeApi.orcode(codeRequest);
            log.info("【调用生成小程序的接口返回数据】：{}", result);
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR);
        }
        return result.getData();
    }

    /**
     * 更新个人数据
     */
    @Override
    public JSONObject updatePersonal(UpdateUserRequest updateUserRequest) {
        log.info("开始执行更新个人数据的方法,接收参数：{}", updateUserRequest);
        ModifyUserRequest modifyUserRequest = new ModifyUserRequest();
        modifyUserRequest.setUserName(updateUserRequest.getUserName());
        modifyUserRequest.setMobile(updateUserRequest.getMobile());
        modifyUserRequest.setIcon(updateUserRequest.getIcon());
        modifyUserRequest.setSsoUserId(updateUserRequest.getSsoUserId());
        modifyUserRequest.setVersion(updateUserRequest.getVersion());
        CommonResult<JSONObject> result = null;
        try {
            result = discountApi.modifyUser(modifyUserRequest);
        } catch (Exception e) {
            log.error("调用 modifyUser 接口异常", e);
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR, e.getMessage());
        }
        return result.getData();
    }

    /**
     * 获取公众号链接
     */
    @LogRecord(type = ExpressLogRecordConstants.OFFICIAL_ACCOUNT_LINK,
            fail = "获取微信关注公众号链接失败，失败原因：「{{#_errorMsg}}」",
            success = "获取链接:{{#url}}",
            bizNo = "{{#userId}}")
    @Override
    public OfficialAccountResponseVO officialAccountLink() {
        log.info("开始执行获取公众号链接的方法");
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        PushRequest pushRequest = new PushRequest();
        pushRequest.setClientId(clientId);
        OfficialAccountResponseVO vo = new OfficialAccountResponseVO();
        try {
            CommonResult<JSONObject> result = orcodeApi.pushSubscribe(pushRequest);
            if (result != null && OrderConstants.successCode.equals(result.getCode())) {
                log.info("【调用公众号链接的接口返回数据】：{}", result);
                JSONObject data = result.getData();
                String url = data.getJSONObject("data").getString("url");
                log.info("【url】：{}", url);
                LogRecordContext.putVariable("url", url);
                return vo.setUrl(url);
            }
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR);
        }
        return vo;
    }

    /**
     * 公众号关注或取关回调
     */
    @LogRecord(type = ExpressLogRecordConstants.CONCERN,
            fail = "公众号关注或取关回调失败，失败原因：「{{#_errorMsg}}」",
            success = "更新用户信息表，受影响行数:{{#row}}",
            bizNo = "{{#userId}}")
    @Override
    public Long officialAccountAttentionOrClearance(PushRequest pushRequest) {
        log.info("开始执行公众号关注或取关的回调方法,接收参数：{}", pushRequest);
        if (SecurityFrameworkUtils.getLoginUserId() == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }
        LogRecordContext.putVariable("userId", pushRequest.getSsoUserId());
        if (pushRequest == null && pushRequest == null) {
            throw exception(ErrorCodeConstants.PARAMETER_IS_EMPTY);
        }
        //根据用户id修改关注或者取消公众号状态
        PersonalInfoSaveReqDTO dto = new PersonalInfoSaveReqDTO();
        dto.setMemberId(SecurityFrameworkUtils.getLoginUserId());
        int row = personInfoApi.updatePersonalInfo(dto);
        if (row > 0) {
            LogRecordContext.putVariable("row", row);
        }
        return Long.valueOf(row);
    }

    /**
     * 获取开放平台代理
     */
    @LogRecord(type = ExpressLogRecordConstants.OPEN_PLATFORM_AGENT,
            fail = "开放平台代理失败，失败原因：「{{#_errorMsg}}」",
            success = "响应信息:{{#result}}",
            bizNo = "{{#userId}}")
    @Override
    public JSONObject openPlatformAgent() {
        log.info("【开始执行获取开放平台代理的方法】");
        if (SecurityFrameworkUtils.getLoginUserId() == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        OpenpRequest openpRequest = new OpenpRequest();
        openpRequest.setSsoUserId(String.valueOf(SecurityFrameworkUtils.getLoginUserId()));
        openpRequest.setRecommenderSsoId(String.valueOf(SecurityFrameworkUtils.getLoginUserId()));
        CommonResult<JSONObject> result = null;
        try {
            result = discountApi.openplatformproxy(openpRequest);
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR);
        }
        LogRecordContext.putVariable("result", result.getData());
        return result.getData();
    }


    /**
     * 获取指定日期的时间范围（凌晨 0 点到该日结束）
     *
     * @param dateTime 指定日期时间
     * @return 时间范围对象
     */
    private TimeRange getTimeRange(LocalDateTime dateTime) {
        LocalDateTime start = dateTime.with(LocalTime.MIN);
        LocalDateTime end = dateTime;
        return new TimeRange(start, end);
    }

    /**
     * 复制基础数据
     *
     * @param result 结果对象
     * @param source 源对象
     */
    private void copyBaseData(WorkbenchInterfaceDTO result, WorkbenchInterfaceDTO source) {
        result.setOrderQuantity(source.getOrderQuantity());
        result.setOrderPaymentQuantity(source.getOrderPaymentQuantity());
        result.setAlreadyCredited(source.getAlreadyCredited());
        result.setNumberRecordedOrders(source.getNumberRecordedOrders());
        result.setSalesVolume(source.getSalesVolume());
        result.setAgentId(source.getAgentId());
        result.setEndTime(source.getEndTime());
    }

    /**
     * 计算并设置变化比率
     *
     * @param result    结果对象
     * @param today     今日数据对象
     * @param yesterday 昨日数据对象
     */
    private void calculateAndSetRatios(WorkbenchInterfaceDTO result, WorkbenchInterfaceDTO today, WorkbenchInterfaceDTO yesterday) {
        calculateAndSetRatio(result, today, yesterday,
                WorkbenchInterfaceDTO::getOrderQuantity,
                WorkbenchInterfaceDTO::setOrderQuantitysize);
        calculateAndSetRatio(result, today, yesterday,
                WorkbenchInterfaceDTO::getOrderPaymentQuantity,
                WorkbenchInterfaceDTO::setOrderPaymentQuantitysize);
        calculateAndSetRatio(result, today, yesterday,
                WorkbenchInterfaceDTO::getAlreadyCredited,
                WorkbenchInterfaceDTO::setAlreadyCreditedsize);
        calculateAndSetRatio(result, today, yesterday,
                WorkbenchInterfaceDTO::getNumberRecordedOrders,
                WorkbenchInterfaceDTO::setNumberRecordedOrderssize);
        calculateAndSetRatio(result, today, yesterday,
                WorkbenchInterfaceDTO::getSalesVolume,
                WorkbenchInterfaceDTO::setSalesVolumesize);
    }

    /**
     * 计算并设置单个属性的变化比率
     *
     * @param result    结果对象
     * @param today     今日数据对象
     * @param yesterday 昨日数据对象
     * @param getter    属性获取方法
     * @param setter    属性设置方法
     * @param <T>       属性类型
     */
    private <T extends Number> void calculateAndSetRatio(WorkbenchInterfaceDTO result, WorkbenchInterfaceDTO today, WorkbenchInterfaceDTO yesterday,
                                                         java.util.function.Function<WorkbenchInterfaceDTO, T> getter,
                                                         java.util.function.BiConsumer<WorkbenchInterfaceDTO, Double> setter) {
        T todayValue = getter.apply(today);
        T yesterdayValue = getter.apply(yesterday);
        double ratio = calculateRatio(
                todayValue != null ? todayValue.doubleValue() - yesterdayValue.doubleValue() : 0.0,
                yesterdayValue != null ? yesterdayValue.doubleValue() : 0.0
        );
        setter.accept(result, ratio);
    }

    // 封装计算比率的方法
    private double calculateRatio(Double dayValue, Double yesterdayValue) {
        if (yesterdayValue == null || yesterdayValue == 0) {
            return 0.0;
        }
        return (dayValue != null ? dayValue : 0.0) / yesterdayValue;
    }

    /**
     * 时间范围类
     */
    private static class TimeRange {
        LocalDateTime start;
        LocalDateTime end;

        TimeRange(LocalDateTime start, LocalDateTime end) {
            this.start = start;
            this.end = end;
        }
    }


    /**
     * 获取订阅列表
     * @return
     */
    @Override
    public CommonResult sendtemplete() {
        //组装请求参数
        TemplatePageRequest request = new TemplatePageRequest();
        request.setClientId(16);
        CommonResult<TemplatePageResponse> templatePageResponseCommonResult = messageApi.templatesPage(request);
        if (templatePageResponseCommonResult.getCode() != 00000 || !templatePageResponseCommonResult.isSuccess()) {
            log.error("查询订阅列表: {}", request);
            throw exception(ErrorCodeConstants.FAILED_QUERY_SUBSCRIPTION, "查询订阅列表失败");
        }
        log.info("查询订阅列表消息成功，结果: {}", templatePageResponseCommonResult);
        return templatePageResponseCommonResult;
    }


    /**
     * 统一支付
     * @param agreePayVo
     * @param orderNos
     * @return
     */
    @LogRecord(type = ExpressLogRecordConstants.PAY_ALL_ORDERS,
            fail = "统一支付失败，失败原因：「{{#_errorMsg}}」",
            success = "接收参数:{{#agreePayVo}}，统一支付的订单编号：{{#orderNos}}，最终微信支付总金额：{{#agreePayVo.receivedAmount}}",
            bizNo = "{{#userId}}")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UnifiedOrderResponseNew paycreateOrder(AgreePayVo agreePayVo, List<String> orderNos) {
        log.info("createOrder:{},{}", agreePayVo, orderNos);
        LogRecordContext.putVariable("agreePayVo", agreePayVo);
        LogRecordContext.putVariable("orderNos", orderNos);
        if (orderNos == null || orderNos.isEmpty()) {
            throw exception(ErrorCodeConstants.ORDER_ERROR);
        }
        //根据订单编号order_no集合查询出来所有的订单详情 拿到orderDOS 传到pay方法中
        List<OrderDO> orderDOS = orderMapper.selectOrderListByOrderNos(orderNos);
        UnifiedOrderResponseNew unifiedOrderResponse = null;
        //支付的时候传递支付的钱数和基本数据信息
        unifiedOrderResponse = pay(orderDOS, agreePayVo.getReceivedAmount()); //优惠总金额
        log.info("响应unifiedOrderResponse:{}", unifiedOrderResponse);
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        return unifiedOrderResponse;
    }


    /**
     * 创建订单管理
     * @param createReqVO 创建信息
     * @param recipientVOs
     * @return
     */
    @LogRecord(type = ExpressLogRecordConstants.CREATE_ORDER,
            fail = "创建订单失败，失败原因：「{{#_errorMsg}}」",
            success = "寄件人信息:{{#createReqVO}}，收件人信息：{{#recipientVOs}}",
            bizNo = "{{#userId}}")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UnifiedOrderResponseNew createOrder(OrderSaveReqVO createReqVO, List<RecipientVO> recipientVOs) {
        log.info("创建订单接收参数:{},{}", createReqVO, recipientVOs);
        LogRecordContext.putVariable("createReqVO", createReqVO);
        LogRecordContext.putVariable("recipientVOs", recipientVOs);
        // 验证收件人信息是否为空
        if (recipientVOs == null || recipientVOs.isEmpty()) {
            throw exception(ErrorCodeConstants.ORDER_RECIPIENT_EMPTY);
        }
        List<OrderSaveReqVO> reqVOList = saveReqVOS(createReqVO, recipientVOs);
        if (CollectionUtil.isEmpty(reqVOList)) {
            throw exception(ErrorCodeConstants.ORDER_ERROR);
        }
        UnifiedOrderResponseNew unifiedOrderResponse = null;
        List<OrderDO> orderDOList = createOrder(reqVOList);
        log.info("订单信息:{}", orderDOList);
        Integer money = orderDOList.stream().map(OrderDO::getReceivedAmount).reduce(0, Integer::sum);
        log.info("【总金额】:{}", money);
        unifiedOrderResponse = pay(orderDOList, money);
        log.info("最终响应给前端的数据:{}", unifiedOrderResponse);
        LogRecordContext.putVariable("unifiedOrderResponse", unifiedOrderResponse);
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        return unifiedOrderResponse;
    }

    /**
     * 创建订单
     * @param requestVO
     * @return
     */
    @LogRecord(type = ExpressLogRecordConstants.CREATE_ORDER,
            fail = "创建订单失败，失败原因：「{{#_errorMsg}}」",
            success = "接收创建订单的参数:{{#createReqVO}},最终微信支付总金额：{{#money}}",
            bizNo = "{{#userId}}")
    @Override
    public UnifiedOrderResponseNew createOrders(List<OrderWithRecipientVO> requestVO) {
        log.info("开始执行多个寄件人和收件人的方法，参数为:{}", requestVO);
        LogRecordContext.putVariable("createReqVO", requestVO);
        if (CollectionUtil.isEmpty(requestVO)) {
            throw exception(ErrorCodeConstants.ORDER_ERROR);
        }
        UnifiedOrderResponseNew unifiedOrderResponse = null;
        Integer money = 0;
        List<OrderDO> list = new ArrayList<>();
        for (OrderWithRecipientVO orderWithRecipientVO : requestVO) {
            List<OrderSaveReqVO> reqVOList = saveReqVOS(orderWithRecipientVO.getCreateReqVO(),
                    orderWithRecipientVO.getRecipientVOs());
            if (CollectionUtil.isEmpty(reqVOList)) {
                throw exception(ErrorCodeConstants.ORDER_ERROR);
            }
            List<OrderDO> orderDOList = createOrder(reqVOList);
            log.info("订单信息:{}", orderDOList);
            list.addAll(orderDOList);
            money += orderDOList.stream().map(OrderDO::getReceivedAmount).reduce(0, Integer::sum);
        }
        log.info("【最终需要支付的总金额】:{}", money);
        LogRecordContext.putVariable("money", money);
        unifiedOrderResponse = pay(list, money);
        return unifiedOrderResponse;
    }

    /**
     * 多个订单存到list集合
     * @param reqVO
     * @param recipientVOs
     * @return
     */
    private List<OrderSaveReqVO> saveReqVOS(OrderSaveReqVO reqVO, List<RecipientVO> recipientVOs) {
        //预约取件时间（今天，明天，后天）
        String day = determineDayType(reqVO.getAppointmentTime());
        log.info("day:{}", day);
        //reqVO.setAppointmentTime(determineDayType(reqVO.getAppointmentTime()));
        //取件的预约开始和结束时间
        String scheduledDate = reqVO.getScheduledTime();
        String startTime = scheduledDate.split("-")[0];
        String endTime = scheduledDate.split("-")[1];

        LocalTime startLocalTime = stringToLocalTime(startTime);
        LocalTime endLocalTime = stringToLocalTime(endTime);
        reqVO.setScheduledStartTime(startLocalTime);
        reqVO.setScheduledEndTime(endLocalTime);
        List<OrderSaveReqVO> orderSaveReqVOList = new ArrayList<>();
        for (RecipientVO recipient : recipientVOs) {
            OrderSaveReqVO orderSaveReqVO = new OrderSaveReqVO();
            // 复制基础订单信息
            BeanUtils.copyProperties(reqVO, orderSaveReqVO);
            // 复制收件人信息
            BeanUtils.copyProperties(recipient, orderSaveReqVO);
            //新增
            orderSaveReqVO.setPersonalId(Objects.requireNonNull(SecurityFrameworkUtils.getLoginUserId()));
            orderSaveReqVO.setStatus(OrderStatusTypeEnum.CREATED.getStatus());
            orderSaveReqVO.setPricingKey(OrderConstants.PRICINGKEY);
            orderSaveReqVO.setProfitKey(OrderConstants.PROFITKEY);
            String orderNumber = generateOrderNumber(orderSaveReqVO);
            orderSaveReqVO.setOrderNo(orderNumber);
            orderSaveReqVOList.add(orderSaveReqVO);
        }
        return orderSaveReqVOList;
    }

    /**
     * 多个订单合并付款
     * @param list
     * @param money
     * @return
     */
    @LogRecord(type = ExpressLogRecordConstants.CREATE_ORDER,
            fail = "发起微信支付失败，失败原因：「{{#_errorMsg}}」",
            success = "发起微信支付，支付金额:{{#money}}，返回响应:{{#wx}}",
            bizNo = "{{#userId}}")
    public UnifiedOrderResponseNew pay(List<OrderDO> list, Integer money) {
        log.info("【即将发起微信支付，需要支付金额为】：{}", money);
        LogRecordContext.putVariable("money", money);
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        OrderDO orderDO = list.get(0);
        List<Long> ids = list.stream().map(OrderDO::getId).collect(Collectors.toList());

        // 构造支付请求参数
        PayOrderReqVO payOrderReqVO = PayOrderReqVO.builder()
                //.orderId(ids.get(0))
                //加上了ids字段得数据 ids就是多个订单的
                .orderIds(ids)
                .amount(money)
                .paymentMethod(orderDO.getPayType())
                .body(orderDO.getPackagingDetails())
                .cardNumber(orderDO.getCardNumber())
                .content(orderDO.getPackagingDetails())
                .build();
        log.info("【构建微信支付请求参数】：{}", payOrderReqVO);

        // 校验支付请求参数
        if (payOrderReqVO == null || payOrderReqVO.getOrderIds() == null || payOrderReqVO.getAmount() == null) {
            log.error("支付请求参数不完整: {}", payOrderReqVO);
            throw exception(ErrorCodeConstants.PARAMETER_IS_EMPTY, "支付请求参数不完整");
        }
        UnifiedOrderResponseNew unifiedOrderResponse;
        try {
            unifiedOrderResponse = payService.payOrder(payOrderReqVO);
            log.info("支付结果：{}", unifiedOrderResponse);
            // 校验支付接口返回值
            if (unifiedOrderResponse == null) {
                log.error("支付接口返回空响应，支付参数: {}", payOrderReqVO);
                throw exception(ErrorCodeConstants.QT_ORDER_PAY_FAIL, "支付接口返回空响应");
            }
            //支付回调方法已经留存更新状态的代码（原因是支付回调更新状态未及时）
        } catch (Exception e) {
            log.error("支付失败，支付参数: {}, 异常信息: {}", payOrderReqVO, e.getMessage(), e);
            throw exception(ErrorCodeConstants.QT_ORDER_PAY_FAIL, "支付失败，请稍后重试");
        }
        //返回订单编号
        List<String> orderNos = new ArrayList<>();
        List<OrderInfoResponseVO> senderOrderList = new ArrayList<>();
        List<OrderInfoResponseVO> receiverOrderList = new ArrayList<>();
        log.info("订单编号：{}", orderNos);
        for (OrderDO order : list) {
            orderNos.add(order.getOrderNo());
            // 创建发件人信息
            OrderInfoResponseVO senderVO = new OrderInfoResponseVO();
            senderVO.setSenderName(order.getSenderName());
            senderVO.setSenderPhone(order.getSenderPhone());
            senderVO.setSenderAddress(order.getSenderAddress());
            senderOrderList.add(senderVO);

            // 创建收件人信息
            OrderInfoResponseVO receiverVO = new OrderInfoResponseVO();
            receiverVO.setReceiverName(order.getReceiverName());
            receiverVO.setReceiverPhone(order.getReceiverPhone());
            receiverVO.setReceiverAddress(order.getReceiverAddress());
            receiverOrderList.add(receiverVO);
        }
        // 构造最终响应
        UnifiedOrderResponseNew unifiedOrder = UnifiedOrderResponseNew.builder()
                .id(ids)
                .paySign(unifiedOrderResponse.getPaySign())
                .aPackage(unifiedOrderResponse.getAPackage())
                .appId(unifiedOrderResponse.getAppId())
                .signType(unifiedOrderResponse.getSignType())
                .timeStamp(unifiedOrderResponse.getTimeStamp())
                .nonceStr(unifiedOrderResponse.getNonceStr())
                .orderNo(orderNos)
                .senderOrderList(senderOrderList)
                .receiverOrderList(receiverOrderList)
                .build();
        log.info("【响应支付信息】:{}", unifiedOrder);
        LogRecordContext.putVariable("wx", unifiedOrder);
        return unifiedOrder;
    }


    /**
     * 下单（单个和批量）
     * @param orderSaveReqVOList
     * @return
     */
    @LogRecord(type = ExpressLogRecordConstants.CREATE_ORDER,
            fail = "创建订单失败，失败原因：「{{#_errorMsg}}」",
            success = "创建订单的信息{{#orderDOList}}",
            bizNo = "{{#userId}}")
    private List<OrderDO> createOrder(List<OrderSaveReqVO> orderSaveReqVOList) {
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());

        // 参数校验
        if (orderSaveReqVOList == null || orderSaveReqVOList.isEmpty()) {
            throw exception(ErrorCodeConstants.OBJECT_NOT_EXIST);
        }
        // 获取第一个订单请求对象
        OrderSaveReqVO createReqVO = orderSaveReqVOList.get(0);

        // 多个发件人地址 ID
        List<Long> senderAddressIds = new ArrayList<>();
        // 多个收件人地址 ID
        List<Long> receiverAddressIds = new ArrayList<>();

        for (OrderSaveReqVO orderSaveReqVO : orderSaveReqVOList) {
            Long senderAddressId = orderSaveReqVO.getSenderAddressId();
            if (senderAddressId != null) {
                senderAddressIds.add(senderAddressId);
            }

            Long receiverAddressId = orderSaveReqVO.getReceiverAddressId();
            if (receiverAddressId != null) {
                receiverAddressIds.add(receiverAddressId);
            }
        }
        if (senderAddressIds.isEmpty()) {
            throw exception(ErrorCodeConstants.SENDER_ADDRESS_NOT_EXISTS);
        }
        if (receiverAddressIds.isEmpty()) {
            throw exception(ErrorCodeConstants.RECEIVER_ADDRESS_NOT_EXISTS);
        }
        // 查询地址信息
        List<Long> addressIds = new ArrayList<>();
        // 只查询第一个发件人地址
        addressIds.add(senderAddressIds.get(0));
        // 查询所有收件人地址
        addressIds.addAll(receiverAddressIds);

        Map<Long, AddressRespApiVO> addressMap = getAddressMap(addressIds);
        if (addressMap == null || addressMap.isEmpty()) {
            throw exception(ErrorCodeConstants.OBJECT_NOT_EXIST);
        }

        // 验证寄件人地址是否存在
        Map<Long, String> invalidAddresses = new HashMap<>();
        AddressRespApiVO sender = addressMap.get(senderAddressIds.get(0));
        if (sender == null) {
            invalidAddresses.put(senderAddressIds.get(0), "发件人地址不存在");
        }
        // 验证收件人地址是否存在
        for (Long receiverAddressId : receiverAddressIds) {
            AddressRespApiVO receiver = addressMap.get(receiverAddressId);
            if (receiver == null) {
                invalidAddresses.put(receiverAddressId, "收件人地址不存在");
            }
        }

        if (!invalidAddresses.isEmpty()) {
            throw exception(ErrorCodeConstants.INVALID_ADDRESSES);
        }
        // 构建收件人信息（包含省市名称和重量）
        List<ReceiverPlace> receiverPlaces = receiverAddressIds.stream()
                .map(receiverAddressId -> {
                    AddressRespApiVO receiver = addressMap.get(receiverAddressId);
                    ReceiverPlace place = new ReceiverPlace();
                    place.setReceiverProvince(receiver.getProvinceName());
                    place.setReceiverCity(receiver.getCityName());
                    place.setReceiverDistrict(receiver.getDistrictName());
                    place.setReceiverAddress(receiver.getAddress());
                    // 目前所有订单的重量相同，取第一个订单的重量
                    place.setWeight(orderSaveReqVOList.get(0).getItemWeight());
                    return place;
                })
                .collect(Collectors.toList());

        // 获取完整的发件人地址信息
        AddressRespApiVO senderAddress = addressMap.get(senderAddressIds.get(0));
        if (senderAddress == null) {
            throw exception(ErrorCodeConstants.SENDER_ADDRESS_NOT_EXISTS);
        }

        // 快递询价：对每个收件人单独调用 queryPrice
        List<PriceQueryRespVO> priceQueryRespVOS = new ArrayList<>();

        for (ReceiverPlace place : receiverPlaces) {
            PriceQueryReqVO priceQueryReqVO = PriceQueryReqVO.builder()
                    .todayCount(Math.toIntExact(todayCountOrdersCount()))
                    .senderProvince(senderAddress.getProvinceName())
                    .senderCity(senderAddress.getCityName())
                    .senderDistrict(senderAddress.getDistrictName())
                    .senderAddress(senderAddress.getAddress())
                    .expressCode(createReqVO.getExpressCode())
                    .useSubCard(createReqVO.getUseSubCard())
                    .receiverPlaces(Collections.singletonList(place))
                    .build();

            List<PriceQueryRespVO> respList = queryPrice(priceQueryReqVO, createReqVO);
            if (respList != null && !respList.isEmpty()) {
                priceQueryRespVOS.addAll(respList);
            }
        }

        log.info("【价格查询结果】----------------：{}", priceQueryRespVOS);
        LogRecordContext.putVariable("priceQueryRespVOS", priceQueryRespVOS);

        if (priceQueryRespVOS == null || priceQueryRespVOS.isEmpty()) {
            throw exception(ErrorCodeConstants.EXPRESS_PRICE_NOT_EXISTS);
        }

        List<OrderDO> orderDOList = new ArrayList<>();
        //询价，存入金额
        for (int i = 0; i < orderSaveReqVOList.size(); i++) {
            OrderSaveReqVO orderSaveReqVO = orderSaveReqVOList.get(i);
            PriceQueryRespVO priceQueryRespVO =
                    priceQueryRespVOS.size() > i ? priceQueryRespVOS.get(i) : priceQueryRespVOS.get(0);
            log.info("【订单询价查询结果】----------------：{}", priceQueryRespVO.getPayMoney());


            OrderDO orderDO = BeanUtils.toBean(orderSaveReqVO, OrderDO.class);
            orderDO.setSenderAddress(sender.getProvinceName() + sender.getCityName() + sender.getDistrictName() + sender.getAddress());
            AddressRespApiVO receiver = addressMap.get(orderSaveReqVO.getReceiverAddressId());
            orderDO.setReceiverAddress(receiver.getProvinceName() + receiver.getCityName() + receiver.getDistrictName() + receiver.getAddress());
            orderDO.setRetailAmount(priceQueryRespVO.getRetailAmount());
            orderDO.setCostAmount(priceQueryRespVO.getFirstPrice() * 100 + priceQueryRespVO.getOverPrice() * 100);
            orderDO.setReceivedAmount(priceQueryRespVO.getPayMoney());
            orderDO.setDiscountAmount(priceQueryRespVO.getDiscountAmount());
            //代理商的id目前未确定，先写死
            orderDO.setAgentId(Long.valueOf(OrderConstants.agentId));
            //TODO
            //此处分润金额，询价的时候取出这个字段，现询价接口还没有提供分润金额字段，暂时获取config中的数据
            int shareAmount = 0;
            if (priceQueryRespVO.getRetailAmount() == null || priceQueryRespVO.getRetailAmount() == 0) {
                //元->分
                shareAmount = new BigDecimal(configApi.getConfigValueByKey("shareprice"))
                        .multiply(BigDecimal.valueOf(100))
                        .intValueExact();
            } else {
                shareAmount=priceQueryRespVO.getRetailAmount();
            }
            orderDO.setShareAmount(shareAmount);
            //默认为未开票状态
            orderDO.setInvoiceStatus(InvoiceStatusTypeEnum.NOT_INVOICED.getStatus());
            orderMapper.insert(orderDO);
            LogRecordContext.putVariable("orderNo", orderDO.getOrderNo());
            // 记录物品信息
            ProductsSaveReqVO productsSaveReqVO = ProductsSaveReqVO.builder()
                    .orderId(orderDO.getId())
                    .category(orderSaveReqVO.getItemCategory())
                    .name(orderSaveReqVO.getItemName())
                    .price(orderSaveReqVO.getItemValue())
                    .description(orderSaveReqVO.getPackagingDetails())
                    .height(orderSaveReqVO.getHeight())
                    .weight(orderSaveReqVO.getItemWeight())
                    .width(orderSaveReqVO.getWidth())
                    .length(orderSaveReqVO.getLength())
                    .quantity(orderSaveReqVO.getItemQuantity())
                    .build();
            productsService.createProducts(productsSaveReqVO);

            // 订单状态记录
            OrderStatusSaveReqVO orderStatusSaveReqVO = OrderStatusSaveReqVO.builder()
                    .orderId(orderDO.getId())
                    .statusStart(OrderStatusTypeEnum.NULL.getStatus())
                    .statusEnd(OrderStatusTypeEnum.CREATED.getStatus())
                    .build();
            orderStatusService.createOrderStatus(orderStatusSaveReqVO);
            orderDOList.add(orderDO);
        }
        log.info("【订单表最终要更新的数据】----------------：{}", orderDOList);
        LogRecordContext.putVariable("orderDOList", orderDOList);
        return orderDOList;
    }

    /**
     * 生成订单编号
     */
    private String generateOrderNumber(OrderSaveReqVO createReqVO) {
        String businessType = BusinessTypeEnum.EA.getStatus();
        String userId = String.valueOf(SecurityFrameworkUtils.getLoginUserId());
        String personalId = StringUtils.leftPad(userId, 2, '0');
        String merchantId = StringUtils.leftPad(String.valueOf(createReqVO.getBrandId()), 2, '0');
        String fullOrderNumber;
        // 最大重试次数
        int maxRetries = 10;
        int retryCount = 0;

        do {
            String orderNumber = TransactionNoGenerator.orderNumber(businessType, personalId, merchantId);
            log.info("【订单编号生成】----------------：{}", orderNumber);
            String endOrderNumber = TransactionNoGenerator.generateCheckCode(orderNumber);
            log.info("【订单编号校验码】----------------：{}", endOrderNumber);
            fullOrderNumber = orderNumber + endOrderNumber;
            retryCount++;

            // 验证订单编号唯一性,如果不存在则返回
            if (orderMapper.validateOrderExistsByOrderNo(fullOrderNumber)) {
                return fullOrderNumber;
            }

            // 如果重试次数超过最大值,抛出异常
            if (retryCount >= maxRetries) {
                throw exception(ErrorCodeConstants.ORDER_ORDERNO_EXISTS);
            }
        } while (true);
    }

    //预约日期转换
    public String determineDayType(String appointmentTime) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-M-d");
        // 解析输入的日期字符串
        LocalDate inputDate = LocalDate.parse(appointmentTime, formatter);
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 判断是今天、明天还是后天
        if (inputDate.isEqual(today)) {
            return "今天";
        } else if (inputDate.isEqual(today.plusDays(1))) {
            return "明天";
        } else if (inputDate.isEqual(today.plusDays(2))) {
            return "后天";
        } else {
            return "其他日期";
        }
    }


    /**
     * 预约时间转换
     * @param timeStr
     * @return
     */
    public LocalTime stringToLocalTime(String timeStr) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        LocalTime localTime = LocalTime.parse(timeStr, formatter);
        return localTime;
    }

    /**
     * 询价的方法
     * @param priceQueryReqVO
     * @param createReqVO
     * @return
     */
    @LogRecord(type = ExpressLogRecordConstants.PRICE_QUERY,
            fail = "询价失败，失败原因：{{#_errorMsg}}",
            success = "询价所需参数{{#price}},询价结果{{#priceQueryRespVOS}}",
            bizNo = "{{#userId}}")
    public List<PriceQueryRespVO> queryPrice(PriceQueryReqVO priceQueryReqVO, OrderSaveReqVO createReqVO) {
        LogRecordContext.putVariable("price", priceQueryReqVO);
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        log.info("询价参数：{}", createReqVO);
        List<PriceQueryRespVO> priceQueryRespVOS = null;
        try {
            priceQueryRespVOS = priceService.queryExpressPrice(priceQueryReqVO);
            log.info("询价结果：{}", priceQueryRespVOS);
            LogRecordContext.putVariable("priceQueryRespVOS", priceQueryRespVOS);
        } catch (Exception e) {
            log.error("运价查询失败", e);
            throw exception(ErrorCodeConstants.EXPRESS_PRICE_QUERY_FAIL, e.getMessage());
        }
        //校验结果
        if (CollectionUtils.isEmpty(priceQueryRespVOS)) {
            throw exception(ErrorCodeConstants.ADDRESS_PARSE_IS_EMPTY);
        }
        return priceQueryRespVOS;
    }

    /**
     * 取消订单回调
     * @param cancelOrderCallBackVO
     * @return
     */
    @Override
    public CancelOrderResponseVO cancelOrderCallBack(CancelOrderCallBackVO cancelOrderCallBackVO) {
        System.out.println("cancelOrderCallBackVO" + cancelOrderCallBackVO);
        // 判断回调请求是否为空
        if (cancelOrderCallBackVO == null || isEmpty(cancelOrderCallBackVO.toString())) {
            throw exception(ErrorCodeConstants.PARAMETER_IS_EMPTY);
        }
        return null;
    }

    // 根据寄件人和收件人的地址簿id获取地址信息
    public Map<Long, AddressRespApiVO> getAddressMap(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new HashMap<>();
        }
        List<AddressRespApiVO> address = addressQueryApi.getAddress(ids);
        if (address == null || address.isEmpty()) {
            return new HashMap<>();
        }
        Map<Long, AddressRespApiVO> addressMap = new HashMap<>();
        for (int i = 0; i < ids.size(); i++) {
            Long id = ids.get(i);
            AddressRespApiVO addressRespApiVO = null;
            for (AddressRespApiVO addObj : address) {
                if (addObj.getId().equals(id)) {
                    addressRespApiVO = addObj;
                    break;
                }
            }
            if (addressRespApiVO != null) {
                addressMap.put(id, addressRespApiVO);
            } else {
                throw exception(ErrorCodeConstants.QUERY_DATA_IS_EMPTY);
            }
        }
        return addressMap;
    }

    /**
     * 向运力系统发起下单
     *
     * @return
     */
    @LogRecord(type = ExpressLogRecordConstants.CREATE_TRACKING,
            fail = "运力下单失败，失败原因：{{#_errorMsg}}",
            success = "运力下单所需参数{{#createTracking}},运力下单返回的结果{{#createOrderResponse}}",
            bizNo = "{{#userId}}")
    @Override
    public CreateOrderResponse createTracking(OrderCreateRequest request) throws Exception {
        log.info("【开始执行向运力下单的方法，请求参数为】----------：{}", request);
        LogRecordContext.putVariable("createTracking", request);
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());

        //查询地址簿取出code
        Long senderId = request.getSenderAddressId();
        Long receiverId = request.getReceiverAddressId();
        List<Long> ids = Arrays.asList(senderId, receiverId);
        Map<Long, AddressRespApiVO> addressMap = getAddressMap(ids);
        AddressRespApiVO sender = addressMap.get(senderId);
        AddressRespApiVO receiver = addressMap.get(receiverId);
        if (sender == null || receiver == null) {
            throw exception(sender == null ? ErrorCodeConstants.SENDER_ADDRESS_NOT_EXISTS :
                    ErrorCodeConstants.RECEIVER_ADDRESS_NOT_EXISTS);
        }
        String senderAfterDistrict = sender.getAddress();
        String receiverAfterDistrict = receiver.getAddress();
        String scheduledStartTime = request.getScheduledStartTime().toString().substring(0, 5);
        String scheduledEndTime = request.getScheduledEndTime().toString().substring(0, 5);
        CreateOrderRequest createOrderRequest = OrderMapStructMapper.INSTANCE.orderSaveReqVOToRequest(request);
        log.info("【转换后的请求参数】：{}", createOrderRequest);
        createOrderRequest.setDayType(determineDayType(request.getAppointmentTime()));
        createOrderRequest.setCallBackUrl(orderConfig.getCallBackUrl());
        createOrderRequest.setChannelId(OrderConstants.channelId);
        createOrderRequest.setSenderCity(sender.getCityName());
        createOrderRequest.setSenderCityCode(sender.getCityCode());
        createOrderRequest.setSenderDistrict(sender.getDistrictName());
        createOrderRequest.setSenderDistrictCode(sender.getDistrictCode());
        createOrderRequest.setSenderProvince(sender.getProvinceName());
        createOrderRequest.setSenderProvinceCode(sender.getProvinceCode());
        createOrderRequest.setReceiverCity(receiver.getCityName());
        createOrderRequest.setReceiverCityCode(receiver.getCityCode());
        createOrderRequest.setReceiverDistrict(receiver.getDistrictName());
        createOrderRequest.setReceiverDistrictCode(receiver.getDistrictCode());
        createOrderRequest.setReceiverProvince(receiver.getProvinceName());
        createOrderRequest.setReceiverProvinceCode(receiver.getProvinceCode());
        createOrderRequest.setSenderAddress(senderAfterDistrict);
        createOrderRequest.setReceiverAddress(receiverAfterDistrict);
        createOrderRequest.setPickupStartTime(scheduledStartTime);
        createOrderRequest.setPickupEndTime(scheduledEndTime);
        CommonResult<CreateOrderResponse> result = null;
        CreateOrderResponse data;
        try {
            result = orderApi.createOrder(createOrderRequest);
            log.info("[调用运力下单接口返回信息]：{}", result);
            LogRecordContext.putVariable("createOrderResponse", result);
            if (result == null || result.getData() == null) {
                throw exception(ErrorCodeConstants.ADDRESS_PARSE_IS_EMPTY);
            }
            if (result.getCode() == OrderConstants.CODE) {
                log.info("【已成功下单，开始更新订单状态】");
            } else {
                throw exception(ErrorCodeConstants.ORDER_INFO_ERROR, result.getMsg());
            }
            ObjectMapper objectMapper = new ObjectMapper();
            data = objectMapper.convertValue(result.getData(), CreateOrderResponse.class);
            log.info("【接收返回参数，并进行参数转换】:{}", data);
            // 更新订单数据,同时更新订单状态为待接单
            int row = orderMapper.updateOrder(createOrderRequest.getOrderSn(), data.getUcmOrderSn());
            if (row > 0) {
                log.info("【更新订单数据成功】");
            }
            // 查询订单信息
            OrderDO orderDO = orderMapper.selectByOrderNo(request.getOrderNo());
            if (orderDO == null) {
                throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
            }
            // 校验订单状态(应为待接单)
            String currentStatus = orderDO.getStatus();
            if (!OrderStatusTypeEnum.WAIT.getStatus().equals(currentStatus)) {
                log.info("【订单状态不合法，当前状态】: {}", currentStatus);
                throw exception(ORDER_STATUS_INVALID);
            }
            //订单状态记录
            OrderStatusTypeEnum status = OrderStatusTypeEnum.valueOf(currentStatus);
            //获取消息生产者
            OrderStatusProducer orderStatusProducer = SpringUtil.getBean(OrderStatusProducer.class);
            switch (status) {
                // 待接单
                case WAIT:
                    orderStatusProducer.sendRoleRefreshMessage(Math.toIntExact(orderDO.getId()), request.getOrderNo(),
                            request.getStatus(), currentStatus, null);
                    break;
            }
        } catch (Exception e) {
            log.error("【运力下单异常】订单 {} 失败原因：{}", request.getOrderNo(), e.getMessage(), e);
            throw exception(ErrorCodeConstants.ORDER_CREATE_FAILURE, e.getMessage());
        }
        log.info("【结束执行向运力下单的方法，返回信息】----------：{}", data);
        LogRecordContext.putVariable("createOrderResponse", data);

        return data;
    }


    /**
     * 向运力系统下单回调
     *
     * @param orderCallBackRequestDTO
     */
    @LogRecord(type = ExpressLogRecordConstants.ORDER_CALL_BACK,
            fail = "运力下单回调失败，失败原因：{{#_errorMsg}}",
            success = "运力下单回调，接收回调参数{{#orderCallback}}",
            bizNo = "{{#userId}}")
    @Transactional(rollbackFor = Exception.class)
    public void orderCallback(OrderCallBackRequestDTO orderCallBackRequestDTO) {
        log.info("【执行下单回调，接收回调参数-------------------】:{}", orderCallBackRequestDTO);
        LogRecordContext.putVariable("orderCallback", orderCallBackRequestDTO);
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());

        // 判断回调请求是否为空
        if (orderCallBackRequestDTO == null || isEmpty(orderCallBackRequestDTO.toString())) {
            throw exception(ErrorCodeConstants.PARAMETER_IS_EMPTY);
        }

        OrderCallBackRequestBO orderCallBackRequestBO = OrderMapStructMapper.INSTANCE.orderCallBackRequestVOToBO(orderCallBackRequestDTO);

        OrderCallBackRequestBO.OrderCallbackData data = orderCallBackRequestBO.getData();
        log.info("【回调参数】:{}", data);
        LogRecordContext.putVariable("orderCallbackData", data);
        if (data == null) {
            throw exception(ErrorCodeConstants.PARAMETER_IS_EMPTY);
        }
        //判断订单是否为空
        if (StringUtils.isEmpty(data.getUcmOrderSn())) {
            throw exception(ErrorCodeConstants.PARAMETER_IS_EMPTY);
        }
        //根据回调参数的订单编号查询是否存在数据库
        OrderDO orderDO = orderMapper.selectOrderBycallBackParameter(data.getUcmOrderSn());
        log.info("【查询订单信息】:{}", orderDO);

        if (ObjectUtil.isEmpty(orderDO)) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        //进行分润
        //调用代理商分润公共方法(测试中，先注释该代码)
        log.info("开始调用代理商分润的公共方法---------------------");
//        DetailShareReqDTO reqDTO = new DetailShareReqDTO();
//        reqDTO.setOrderId(orderDO.getId());
//        reqDTO.setOrderNo(orderDO.getOrderNo());
//        reqDTO.setTargetId(orderDO.getAgentId());
//        //分润的目标类型
//        reqDTO.setTargetType(OrderConstants.targetType);
//        reqDTO.setAmount(2);
//        reqDTO.setCalculateTime(LocalDateTime.now());
//        reqDTO.setStatus(OrderConstants.detailShareStatus);
//        CommonResult<String> sharebenefit = pricingUtilsApiService.sharebenefit(reqDTO);
//        log.info("调用代理商分润响应:{}", sharebenefit);
//
//        if (data.getWeight() - orderDO.getItemWeight() > 0) {
//            //调用差价计算公共方法
//            PricingRuleApiPageReqVO pricingRuleApiPageReqVO = new PricingRuleApiPageReqVO();
//            pricingRuleApiPageReqVO.setFirstWeightMarkup(orderDO.getItemWeight());
//            pricingRuleApiPageReqVO.setAdditionalWeightMarkup(data.getWeight() - orderDO.getItemWeight());
//            pricingRuleApiPageReqVO.setAgentId(orderDO.getAgentId());
//            CommonResult<PricingRuleApiPageReqVO> difference = pricingUtilsApiService.difference(pricingRuleApiPageReqVO);
//            log.info("调用差价计算响应:{}", difference);
//        }
        //获取运单号
        String expressSn = data.getExpressSn();
        if (StringUtils.isEmpty(expressSn)) {
            throw exception(ErrorCodeConstants.PARAMETER_IS_EMPTY);
        }
        OrderStatusProducer orderStatusProducer = SpringUtil.getBean(OrderStatusProducer.class);
        switch (OrderCallbackStatusEnum.fromCode(data.getStatus())) {
            case SUCCESS -> {
                // 下单成功->对应小B的待接单
                //调用运力下单时已经做了订单状态更新
            }
            case ORDER_ACCEPTED, ORDER_CREATED, COLLECTING, PICKED_UP -> {
                // 已接单,已出单，收件中，已取件->对应小B的待取件
                log.info("准备更新订单状态");
                //判断当前订单状态是否为待接单
                if (!(OrderStatusTypeEnum.WAIT.getStatus().equals(orderDO.getStatus()))) {
                    throw exception(ORDER_STATUS_INVALID);
                }
                //根据订单编号更新运单号和待取件状态
                OrderDO orderDO1 = new OrderDO();
                orderDO1.setId(orderDO.getId());
                orderDO1.setUcmOrderSn(data.getUcmOrderSn());
                orderDO1.setTrackingNumber(expressSn);
                orderDO1.setReceiverStaffName(data.getCourierName());
                orderDO1.setReceiverStaffMobile(data.getCourierMobile());
                orderDO1.setReceiverStaffPickupCode(data.getPickupCode());
                orderDO1.setStatus(OrderStatusTypeEnum.PICKUP_PENDING.getStatus());
                log.info("【更新订单状态】:{}", orderDO1);
                orderMapper.updateOrderBycallBackParameter(orderDO1);
                //订单状态记录
                orderStatusProducer.sendRoleRefreshMessage(
                        Math.toIntExact(orderDO.getId()),
                        orderDO.getOrderNo(),
                        orderDO.getStatus(),
                        OrderStatusTypeEnum.PICKUP_PENDING.getStatus(),
                        null);
            }

            case WEIGHT_MODIFIED -> {
                //修改重量
                // 从配置中获取利润值
                Double profit = Double.parseDouble(configApi.getConfigValueByKey("profit"));
                //订单回调的折后价格
                Integer freight = data.getFreight();
                Integer amount = countProfit(freight, profit);
                //订单下单支付的实际金额
                Integer receivedAmount = orderDO.getReceivedAmount();
                if (receivedAmount < amount) {
                    Integer difference = amount - receivedAmount;
                    log.info("【订单需补付金额】: {}", difference);

                    //异步通知推广平台更新订单金额
                    CompletableFuture<Boolean> updateAmountFuture = asyncNotifyUpdateOrderAmount(orderDO, myUpdatePriceExecutor);
                    updateAmountFuture.thenAccept(success -> {
                        if (Boolean.TRUE.equals(success)) {
                            log.info("订单金额更新状态确认成功");
                        } else {
                            log.warn("订单金额更新失败");
                        }
                    });

                    Map<Long, AddressRespApiVO> addressMap = getAddressMap(List.of(orderDO.getSenderAddressId(),
                            orderDO.getReceiverAddressId()));
                    AddressRespApiVO sendAddress = addressMap.get(orderDO.getSenderAddressId());
                    AddressRespApiVO reciveAddress = addressMap.get(orderDO.getReceiverAddressId());
                    boolean isSameProvince = sendAddress.getProvinceName().equals(reciveAddress.getProvinceName());

                    LadderCountDecorator ladderCountDecorator = null;
                    OrderDetailDTO orderDetailDTO = new OrderDetailDTO();
                    orderDetailDTO.setTodayCount(Math.toIntExact(todayCountOrdersCount()));
                    orderDetailDTO.setOrderId(orderDO.getId());
                    orderDetailDTO.setCostAmount(orderDO.getCostAmount());
                    orderDetailDTO.setWeight(String.valueOf(data.getWeight() - orderDO.getItemWeight()));
                    orderDetailDTO.setFirstPrice(1);
                    orderDetailDTO.setOverPrice(1);
                    //优惠后的钱
                    Integer payMoney = ladderCountDecorator.countCouponPayMoneyNoId(isSameProvince, orderDO.getExpressCode(), orderDetailDTO);
                    //重新修改订单数据
                    OrderDO order = new OrderDO();
                    order.setId(orderDO.getId());
                    order.setStatus(OrderStatusTypeEnum.PENDING_ADDITIONAL_FEE.getStatus());
                    order.setItemWeight(data.getWeight());
                    int row = orderMapper.updateOrderInfo(order);
                    if (row > 0) {
                        log.info("【更新订单成功，受影响行数】:{}", row);
                    }
                    //重新发起支付
                    AgreePayVo agreePayVo = new AgreePayVo();
                    agreePayVo.setReceivedAmount(payMoney - receivedAmount);
                    try {
                        UnifiedOrderResponseNew unifiedOrderResponse = paycreateOrder(agreePayVo, List.of(orderDO.getOrderNo()));
                        log.info("【重新发起支付,拉起微信支付所需参数】: {}", unifiedOrderResponse);
                    } catch (Exception e) {
                        throw exception(ErrorCodeConstants.ORDER_CREATE_FAILURE, e.getMessage());
                    }
                }
            }

            case ORDER_REVIVED -> {
                //订单复活->对应小B的待补运费
            }

            case ABNORMAL_SIGNED -> {
                // 异常签收->
            }
            case SETTLED -> {
                // 已结算->对应小B的已结算
                log.info("准备更新订单状态为已结算");

                //没更新订单状态前的状态
                String oldStatus = orderDO.getStatus();
                //修改订单状态为已完成
                updateOrderStatusByNos(orderDO.getOrderNo(),
                        OrderStatusTypeEnum.PARTIAL_PAYMENT_PENDING.getStatus());
                //messageUtils.sendStatusUpdateMessage(orderDO);

                //异步通知推广平台更新订单状态
                CompletableFuture<Boolean> updateStatusFuture = asyncNotifyUpdateOrderStatus(orderDO, PromotionEnum.PARTIAL_PAYMENT_PENDING.getCode(), myUpdateOrderStatusExecutor);
                updateStatusFuture.thenAccept(success -> {
                    if (Boolean.TRUE.equals(success)) {
                        log.info("更新订单状态确认成功");
                    } else {
                        log.warn("更新订单状态失败");
                    }
                });

                //订单状态记录
                orderStatusProducer.sendRoleRefreshMessage(
                        Math.toIntExact(orderDO.getId()),
                        orderDO.getOrderNo(),
                        oldStatus,
                        OrderStatusTypeEnum.PARTIAL_PAYMENT_PENDING.getStatus(),
                        null);
            }

            case SIGNED -> {
                // 已签收->对应小B的已完成
                log.info("更新订单状态为已完成");

                //没更新订单状态前的状态
                String oldStatus = orderDO.getStatus();
                //修改订单状态为已完成
                updateOrderStatusByNos(orderDO.getOrderNo(),
                        OrderStatusTypeEnum.COMPLETED.getStatus());
                //订单状态记录
                orderStatusProducer.sendRoleRefreshMessage(
                        Math.toIntExact(orderDO.getId()),
                        orderDO.getOrderNo(),
                        oldStatus,
                        OrderStatusTypeEnum.COMPLETED.getStatus(),
                        null);

                //异步通知推广平台更新订单状态
                CompletableFuture<Boolean> updateStatusFuture = asyncNotifyUpdateOrderStatus(orderDO, PromotionEnum.COMPLETED.getCode(), myUpdateOrderStatusExecutor);
                updateStatusFuture.thenAccept(success -> {
                    if (Boolean.TRUE.equals(success)) {
                        log.info("更新订单状态确认成功");
                    } else {
                        log.warn("更新订单状态失败");
                    }
                });
            }

            case COLLECTION_FAILED, ORDER_CREATION_FAILED, USER_CANCELLED, ORDER_CANCELLED, ORDER_PLACEMENT_FAILED -> {
                // 揽收失败，出单失败,用户主动取消,订单已取消,下单失败->对应小B的已取消
                log.warn("订单准备取消");
                //调用运力取消订单的接口
                CancelOrderRequest cancelOrderRequest = new CancelOrderRequest();
                cancelOrderRequest.setUcmOrderSn(orderDO.getUcmOrderSn());
                cancelOrderRequest.setCancelMsg("取消");
                ucmCancelOrder(cancelOrderRequest);

            }

            case IN_TRANSIT, DELIVERY_IN_PROGRESS -> {
                // 运输中, 派送中->对应小B的运输中
                log.info("订单运输中，运单号：{}", data.getExpressSn());
                //没更新订单状态前的状态
                String oldStatus = orderDO.getStatus();
                //修改订单状态为运输中
                updateOrderStatus(orderDO.getOrderNo(),
                        OrderStatusTypeEnum.IN_TRANSIT.getStatus());

                messageUtils.sendStatusUpdateMessage(orderDO);

                //订单状态记录
                orderStatusProducer.sendRoleRefreshMessage(
                        Math.toIntExact(orderDO.getId()),
                        orderDO.getOrderNo(),
                        oldStatus,
                        OrderStatusTypeEnum.IN_TRANSIT.getStatus(),
                        null);
            }
            default -> {
                log.warn("未处理的状态码: {}，描述: {}", data.getStatus(), OrderCallbackStatusEnum.fromCode(data.getStatus()).getDescription());
                throw exception(ORDER_STATUS_INVALID, data.getStatus().toString());
            }
        }
    }

    /**
     * 异步通知推广平台更新订单状态
     *
     * @param orderDO
     * @return
     */
    private CompletableFuture<Boolean> asyncNotifyUpdateOrderStatus(OrderDO orderDO, Integer status, Executor executor) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 构建请求参数
                UpOrderStatusRequest requestVO = new UpOrderStatusRequest();
                requestVO.setSsoUserId(Math.toIntExact(orderDO.getPersonalId()));
                requestVO.setOrderSn(orderDO.getOrderNo());
                requestVO.setStatus(status);

                // 调用接口
                CommonResult<JSONObject> result = discountApi.upOrderStatus(requestVO);
                log.info("【调用修改订单状态接口响应】: {}", result);

                if (result == null || result.getData() == null) {
                    log.warn("接口返回为空或数据为空，订单号：{}", orderDO.getOrderNo());
                    return false;
                }

                boolean success = OrderConstants.code.equals(result.getData().getString("code"));

                if (success) {
                    log.info("异步通知更新订单状态成功，订单号：{}", orderDO.getOrderNo());
                } else {
                    log.warn("异步通知更新订单状态失败，订单号：{}", orderDO.getOrderNo());
                }

                return success;

            } catch (Exception e) {
                log.error("异步更新更新订单状态异常，订单号：{}", orderDO.getOrderNo(), e);
                return false;
            }
        }, executor);
    }

    /**
     * 异步通知推广平台更新订单金额
     */
    public CompletableFuture<Boolean> asyncNotifyUpdateOrderAmount(OrderDO orderDO, Executor executor) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 构建请求参数
                CreatOrderRequest requestVO = new CreatOrderRequest();
                requestVO.setSsoUserId(Math.toIntExact(orderDO.getPersonalId()));
                requestVO.setOrderSn(orderDO.getOrderNo());
                requestVO.setTotalAmount(orderDO.getCostAmount());
                requestVO.setPayAmount(orderDO.getReceivedAmount());
                requestVO.setMarkup(orderDO.getRetailAmount() - orderDO.getCostAmount());
                requestVO.setProfit(2000);
                requestVO.setStatus(1);

                // 调用接口
                CommonResult<JSONObject> result = discountApi.upOrderAmount(requestVO);
                log.info("【调用订单金额更新接口响应】: {}", result);

                if (result == null || result.getData() == null) {
                    log.warn("接口返回为空或数据为空，订单号：{}", orderDO.getOrderNo());
                    return false;
                }

                boolean success = OrderConstants.code.equals(result.getData().getString("code"));

                if (success) {
                    log.info("异步通知更新订单金额成功，订单号：{}", orderDO.getOrderNo());
                } else {
                    log.warn("异步通知更新订单金额失败，订单号：{}", orderDO.getOrderNo());
                }

                return success;

            } catch (Exception e) {
                log.error("异步更新订单金额异常，订单号：{}", orderDO.getOrderNo(), e);
                return false;
            }
        }, executor);
    }


    /**
     * 异步通知推广平台取消订单
     */
    public CompletableFuture<Boolean> asyncNotifyCancelOrder(OrderDO orderDO, Executor executor) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 构建请求参数
                CancelRequest requestVO = new CancelRequest();
                requestVO.setSsoUserId(Math.toIntExact(orderDO.getPersonalId()));
                requestVO.setOrderSn(orderDO.getOrderNo());
                requestVO.setStatus(1);

                // 调用接口
                CommonResult<JSONObject> result = discountApi.cancelOrder(requestVO);
                log.info("【调用推广平台取消订单接口响应】: {}", result);

                if (result == null || result.getData() == null) {
                    log.warn("接口返回为空或数据为空，订单号：{}", orderDO.getOrderNo());
                    return false;
                }

                boolean success = OrderConstants.code.equals(result.getData().getString("code"));

                if (success) {
                    log.info("异步通知取消订单成功，订单号：{}", orderDO.getOrderNo());
                } else {
                    log.warn("异步通知取消订单失败，订单号：{}", orderDO.getOrderNo());
                }

                return success;

            } catch (Exception e) {
                log.error("异步通知取消订单异常，订单号：{}", orderDO.getOrderNo(), e);
                return false;
            }
        }, executor);
    }


    // 计算利润
    public Integer countProfit(Integer amount, Double profit) {
        // 计算利润金额，通过将金额与利润相乘然后加上原始金额来获得总利润金额
        BigDecimal profitAmount = NumberUtil.add(NumberUtil.mul(amount, profit), amount);
        // 将利润金额四舍五入到最接近的整数并转换为整型
        return NumberUtil.round(profitAmount, 0).intValue();
    }

    /**
     * 调用统一退款 支持部分退款 全部退款
     *
     * @param orderCancelVO
     */
    @Override
    public OrderCancelVO refundcancelOrder(OrderCancelVO orderCancelVO) {
        //根据订单编号查询支付单信息
        OrderDO order = orderMapper.selectOrder(orderCancelVO.getOrderNo());
        PaymentOrderDO paymentOrderDO = paymentOrderMapper.getPaymentOrder(order.getId());
        //需要通知退款的订单(所需参数)
        RefundOrderSaveReqVO saveReqVO = new RefundOrderSaveReqVO();
        saveReqVO.setPaymentOrderId(paymentOrderDO.getId());
        saveReqVO.setAmount(paymentOrderDO.getAmount());
        saveReqVO.setRefundReason(saveReqVO.getRefundReason());

        if (saveReqVO == null) {
            log.error("【退款失败：所需参数为空】：{}", saveReqVO);
            throw exception(ErrorCodeConstants.REFUND_CREATE_FAIL);
        }
        try {
            refundService.createRefundOrderInfo(saveReqVO);
            log.info("退款成功");
        } catch (Exception e) {
            log.error("退款失败");
            throw exception(ErrorCodeConstants.REQUEST_REFUND_FAIL);
        }
        return orderCancelVO;
    }

    /**
     * 取消订单
     *
     * @param orderCancelVO
     */
    @LogRecord(type = ExpressLogRecordConstants.ORDER_CANCEL,
            fail = "{{#orderCancelVO.orderNo}}-订单取消失败:，失败原因:{{#_errorMsg}}",
            success = "接收参数:{{#orderCancelVO}}}",
            bizNo = "{{#userId}}")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderCancelVO cancelOrder(OrderCancelVO orderCancelVO) {
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        ArrayList<RefundOrderSaveReqVO> refundOrderSaveReqVO = new ArrayList<>();
        log.info("【开始执行取消订单的方法--------------】：{}", orderCancelVO);
        LogRecordContext.putVariable("orderCancelVO", orderCancelVO);
        //判断订单是否存在
        OrderDO order = orderMapper.selectOrder(orderCancelVO.getOrderNo());
        if (ObjectUtil.isNull(order)) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        // 新增：校验订单状态是否支持取消
        if (!Arrays.asList(OrderStatusTypeEnum.CREATED.getStatus(), OrderStatusTypeEnum.PAID.getStatus(),
                OrderStatusTypeEnum.WAIT.getStatus(), OrderStatusTypeEnum.PENDING_ADDITIONAL_FEE.getStatus(),
                OrderStatusTypeEnum.PICKUP_PENDING.getStatus()).contains(order.getStatus())) {
            throw exception(ErrorCodeConstants.ORDER_STATUS_INVALID);
        }
        //查询当前订单的状态
        OrderStatusTypeEnum status = OrderStatusTypeEnum.valueOf(order.getStatus());
        String orderNo = orderCancelVO.getOrderNo();
        //调用订单状态广播消息生产者
        OrderStatusProducer orderStatusProducer = SpringUtil.getBean(OrderStatusProducer.class);
        RefundOrderSaveReqVO saveReqVO = null;
        if (!OrderStatusTypeEnum.CREATED.equals(status)) {
            List<PaymentOrderDO> paymentOrderList = paymentOrderMapper.selectListById(order.getId());
            for (PaymentOrderDO paymentOrderDO : paymentOrderList) {
                //创建退款单(所需参数)
                saveReqVO = new RefundOrderSaveReqVO();
                saveReqVO.setPaymentOrderId(paymentOrderDO.getId());
                saveReqVO.setAmount(order.getReceivedAmount());
                saveReqVO.setRefundReason(saveReqVO.getRefundReason());
                refundOrderSaveReqVO.add(saveReqVO);
            }
        }
        //调用运力取消订单的接口
        CancelOrderRequestVO cancelOrderRequestVO = new CancelOrderRequestVO();
        cancelOrderRequestVO.setUcmOrderSn(orderCancelVO.getUcmOrderSn());
        cancelOrderRequestVO.setCancelMsg("取消");
        CancelOrderRequest cancelOrderRequest = BeanUtils.toBean(cancelOrderRequestVO, CancelOrderRequest.class);
        switch (status) {
            case CREATED -> {
                //修改订单状态为已作废
                updateOrderStatusByNos(order.getOrderNo(), OrderStatusTypeEnum.CANCELED.getStatus());
                //订单状态记录
                orderStatusProducer.sendRoleRefreshMessage(Math.toIntExact(order.getId()), orderNo,
                        OrderStatusTypeEnum.CREATED.getStatus(),
                        OrderStatusTypeEnum.CANCELED.getStatus(), userId());
                //退还次卡
                if (order.getCardNumber() != null) {
                    PayCardVo payCardVo = new PayCardVo();
                    payCardVo.setOrderId(order.getId());
                    payCardVo.setCardNumber(order.getCardNumber());
                    refundCardApi.refundCard(payCardVo);
                }
            }
            //已付款(次卡问题需要退还次卡)
            case PAID -> {
                //修改订单状态为已取消
                updateOrderStatusByNos(order.getOrderNo(), OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus());
                //记录已取消
                orderStatusProducer.sendRoleRefreshMessage(Math.toIntExact(order.getId()), orderNo,
                        OrderStatusTypeEnum.PAID.getStatus(),
                        OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus(), userId());

                //异步通知推广平台取消订单
                CompletableFuture<Boolean> cancelOrderFuture = asyncNotifyCancelOrder(order, myCancelOrderExecutor);
                cancelOrderFuture.thenAccept(success -> {
                    if (Boolean.TRUE.equals(success)) {
                        log.info("取消订单确认成功");
                    } else {
                        log.warn("取消订单失败");
                    }
                });
                //换成批量插入的方法
                for (RefundOrderSaveReqVO reqVO : refundOrderSaveReqVO) {
                    refundService.createRefundOrderInfo(reqVO);
                }

                //修改订单状态为退款中
                updateOrderStatusByNos(order.getOrderNo(), OrderStatusTypeEnum.REFUND_PROCESSING.getStatus());
                //记录退款中
                orderStatusProducer.sendRoleRefreshMessage(Math.toIntExact(order.getId()), orderNo,
                        OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus(),
                        OrderStatusTypeEnum.REFUND_PROCESSING.getStatus(), userId());
            }

            //待接单：(次卡问题需要更新)
            case WAIT -> {
                //调用运力平台取消订单的方法
                ucmCancelOrder(cancelOrderRequest);
                //修改订单状态为已取消
                updateOrderStatusByNos(order.getOrderNo(), OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus());
                //记录已取消
                orderStatusProducer.sendRoleRefreshMessage(Math.toIntExact(order.getId()), orderNo,
                        OrderStatusTypeEnum.WAIT.getStatus(),
                        OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus(), userId());

                //异步通知推广平台取消订单
                CompletableFuture<Boolean> cancelOrderFuture = asyncNotifyCancelOrder(order, myCancelOrderExecutor);
                cancelOrderFuture.thenAccept(success -> {
                    if (Boolean.TRUE.equals(success)) {
                        log.info("取消订单确认成功");
                    } else {
                        log.warn("取消订单失败");
                    }
                });
                //创建退款单
                //换成批量插入的方法
                for (RefundOrderSaveReqVO reqVO : refundOrderSaveReqVO) {
                    refundService.createRefundOrderInfo(reqVO);
                }

                //修改订单状态为退款中
                updateOrderStatus(order.getOrderNo(),
                        OrderStatusTypeEnum.REFUND_PROCESSING.getStatus());
                //记录退款中
                orderStatusProducer.sendRoleRefreshMessage(Math.toIntExact(order.getId()), orderNo,
                        OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus(),
                        OrderStatusTypeEnum.REFUND_PROCESSING.getStatus(), userId());
            }

            //代补运费
            case PENDING_ADDITIONAL_FEE -> {
                //调用运力平台取消订单的方法
                ucmCancelOrder(cancelOrderRequest);
                //修改订单状态为已取消
                updateOrderStatusByNos(order.getOrderNo(),
                        OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus());
                //记录已取消
                orderStatusProducer.sendRoleRefreshMessage(Math.toIntExact(order.getId()), orderNo,
                        OrderStatusTypeEnum.PENDING_ADDITIONAL_FEE.getStatus(),
                        OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus(), userId());
                //异步通知推广平台取消订单
                CompletableFuture<Boolean> cancelOrderFuture = asyncNotifyCancelOrder(order, myCancelOrderExecutor);
                cancelOrderFuture.thenAccept(success -> {
                    if (Boolean.TRUE.equals(success)) {
                        log.info("取消订单确认成功");
                    } else {
                        log.warn("取消订单失败");
                    }
                });
                //创建退款单
                //换成批量插入的方法
                for (RefundOrderSaveReqVO reqVO : refundOrderSaveReqVO) {
                    refundService.createRefundOrderInfo(reqVO);
                }

                //修改订单状态为退款中
                updateOrderStatus(order.getOrderNo(),
                        OrderStatusTypeEnum.REFUND_PROCESSING.getStatus());
                //记录退款中
                orderStatusProducer.sendRoleRefreshMessage(Math.toIntExact(order.getId()), orderNo,
                        OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus(),
                        OrderStatusTypeEnum.REFUND_PROCESSING.getStatus(), userId());
            }

            //待取件：(次卡问题需要更新)
            case PICKUP_PENDING -> {
                //调用运力平台取消订单的方法
                ucmCancelOrder(cancelOrderRequest);
                //修改订单状态为已取消
                updateOrderStatusByNos(order.getOrderNo(),
                        OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus());
                //记录已取消
                orderStatusProducer.sendRoleRefreshMessage(Math.toIntExact(order.getId()), orderNo,
                        OrderStatusTypeEnum.PICKUP_PENDING.getStatus(),
                        OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus(), userId());
                //异步通知推广平台取消订单
                CompletableFuture<Boolean> cancelOrderFuture = asyncNotifyCancelOrder(order, myCancelOrderExecutor);
                cancelOrderFuture.thenAccept(success -> {
                    if (Boolean.TRUE.equals(success)) {
                        log.info("取消订单确认成功");
                    } else {
                        log.warn("取消订单失败");
                    }
                });
                //创建退款单
                //createRefundOrder(saveReqVO);
                //判断是否为空
                //换成批量插入的方法
                for (RefundOrderSaveReqVO reqVO : refundOrderSaveReqVO) {
                    refundService.createRefundOrderInfo(reqVO);
                }
                //修改订单状态为退款中
                updateOrderStatus(order.getOrderNo(),
                        OrderStatusTypeEnum.REFUND_PROCESSING.getStatus());
                //记录退款中
                orderStatusProducer.sendRoleRefreshMessage(Math.toIntExact(order.getId()), orderNo,
                        OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus(),
                        OrderStatusTypeEnum.REFUND_PROCESSING.getStatus(), userId());

            }
            default -> throw exception(ORDER_STATUS_INVALID);
        }
        return orderCancelVO;
    }

    /**
     * 调用运力平台取消订单接口（抽取方法）
     *
     * @param cancelOrderRequest 请求参数
     */
    private CancelOrderResponse ucmCancelOrder(CancelOrderRequest cancelOrderRequest) {
        CommonResult<CancelOrderResponse> cancelOrderResult;
        try {
            // 调用运力取消订单接口
            cancelOrderResult = orderApi.cancelOrder(cancelOrderRequest);
            if (cancelOrderResult == null) {
                log.error("【调用运力取消订单接口返回结果为空】：{}", cancelOrderResult);
                throw exception(ErrorCodeConstants.ADDRESS_PARSE_IS_EMPTY);
            }
            //根据响应code值判断
            if (!OrderConstants.CODE.equals(cancelOrderResult.getCode())) {
                log.error("订单取消失败");
                throw exception(ErrorCodeConstants.CANCEL_ORDER_FAILED);
            }
            log.info("【运力取消订单成功】统一订单号：{}", cancelOrderRequest.getUcmOrderSn());

        } catch (Exception e) {
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR, e.getMessage());
        }
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.convertValue(cancelOrderResult.getData(), CancelOrderResponse.class);

    }

    /**
     * 更新订单状态
     */
    public void updateOrderStatus(String orderNo, String status) {
        OrderSaveReqVO updateReq = new OrderSaveReqVO();
        updateReq.setOrderNo(orderNo);
        updateReq.setStatus(status);
        updateByOrderId(updateReq);
    }

    /**
     * 更新订单状态
     */
    public Integer updateOrderStatusByNos(String orderNo, String status) {
        log.info("【执行更新订单状态】：{}", orderMapper.updateOrderByNos(orderNo, status));
        return orderMapper.updateOrderByNos(orderNo, status);
    }

    /**
     * 更新订单状态,
     */
    public void updateOrderByNos(String orderNo, String status) {
        OrderSaveReqVO updateReq = new OrderSaveReqVO();
        updateReq.setOrderNo(orderNo);
        updateReq.setStatus(status);
        updateByOrderId(updateReq);
    }

    /**
     * 更新订单信息
     * @param orderSaveReqVO 订单更新请求参数对象，包含订单ID和需要更新的字段数据
     * @return
     */
    @Override
    public int updateByOrderId(OrderSaveReqVO orderSaveReqVO) {
        return orderMapper.updateByOrderId(orderSaveReqVO);
    }

    /**
     * 根据订单编号获取订单状态
     * @param orderNo
     * @return
     */
    @Override
    public String getOrderStatus(String orderNo) {
        return orderMapper.getOrderStatus(orderNo);
    }


    /**
     * 获取订单列表
     * @param orderResUserVO
     * @return
     */
    @Override
    public PageResult<OrderDO> getOrderPageOrders(OrderResUserVO orderResUserVO) {
        PageResult<OrderDO> pageResult = orderMapper.selectPageOrders(orderResUserVO);
        return pageResult;
    }

    /**
     * 更新订单信息
     * @param updateReqVO 更新信息
     */

    @Override
    public void updateOrder(OrderSaveReqVO updateReqVO) {
        // 校验存在
        validateOrderExists(updateReqVO.getId());
        // 更新
        OrderDO updateObj = BeanUtils.toBean(updateReqVO, OrderDO.class);
        orderMapper.updateById(updateObj);
    }

    /**
     * 校验订单是否存在
     * @param id
     */
    private void validateOrderExists(Long id) {
        if (orderMapper.selectById(id) == null) {
            throw exception(ORDER_NOT_EXISTS);
        }
    }

    /**
     * 查询订单信息
     * @param id 编号
     * @return
     */
    @Override
    public OrderResponseVO getOrder(Long id) {
        log.info("【查询订单信息】：{}", SecurityFrameworkUtils.getLoginUserId());
        //根据当前登录用户id查询订单信息
        OrderDO orderDO = orderMapper.selectOrderInfo(id, SecurityFrameworkUtils.getLoginUserId());
        log.info("【查询订单信息】：{}", orderDO);
        //
        //String time = time(orderDO.getCreateTime());
        OrderRespVO orderRespVO = BeanUtils.toBean(orderDO, OrderRespVO.class);
        OrderResponseVO orderResponseVO = BeanUtils.toBean(orderRespVO, OrderResponseVO.class);
        //查询brand表，取出品牌名称
        BrandDO brandDO = brandMapper.selectBrandNameByBrandKey(orderDO.getExpressCode());
        log.info("【查询品牌名称】：{}", brandDO);
//        orderRespVO.setCreateTime(time);
        List<ProductsDO> productsDOList = productsService.selectProductsInfo(id);
        List<ProductsRespVO> productsRespVOS = ProductMapStructMapper.INSTANCE.doListToVoList(productsDOList);
        orderResponseVO.setProductsRespVOList(productsRespVOS);
        orderResponseVO.setPlainSenderName(orderDO.getSenderName());
        orderResponseVO.setPlainReceiverName(orderDO.getReceiverName());
        orderResponseVO.setPlainSenderPhone(orderDO.getSenderPhone());
        orderResponseVO.setPlainReceiverPhone(orderDO.getReceiverPhone());
        orderResponseVO.setBrandName(brandDO.getBrandName());
        return orderResponseVO;
    }

    /**
     * 时间格式转换
     * @param localDateTime
     * @return
     */
    public String time(LocalDateTime localDateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String time = localDateTime.format(formatter);
        return time;
    }

    /**
     * 分页查询
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<OrderDO> getOrderPage(OrderPageReqVO pageReqVO) {
        Long id = SecurityFrameworkUtils.getLoginUser().getId();
        OrderPageReqVO reqVO = pageReqVO.setPersonalId(id);
        return orderMapper.selectPage(reqVO);
    }

    /**
     * 地址解析的方法
     *
     * @param content 需要解析的原始文本内容
     */
    public List<AddressRespVO> contentParse(AddressListRequest content) {
        log.info("【开始执行地址解析的方法，接收参数为】：{}", content);
        // 调用智能解析接口
        CommonResult<List<AddressListResponse>> listCommonResult;
        try {
            listCommonResult = expressApi.listAddressBatch(content);
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.ADDRESS_PARSE_EMPTY, e.getMessage());
        }
        // 检查解析结果
        if (listCommonResult == null || listCommonResult.getData() == null) {
            log.info("【调用运力解析接口返回结果为空】：{},【错误信息】", listCommonResult, listCommonResult.getMsg());
            return new ArrayList<>();
        }
        List<AddressListResponse> list = listCommonResult.getData();
        List<AddressParseVO> addressParseVOS = BeanUtils.toBean(list, AddressParseVO.class);

        List<AddressRespVO> addressRespVOList = new ArrayList<>();
        for (AddressParseVO addressParseVO : addressParseVOS) {
            AddressRespVO addressRespVO = new AddressRespVO();
            addressRespVO.setAddress(addressParseVO.getDetail_address());
            addressRespVO.setPhone(addressParseVO.getPhone());
            addressRespVO.setName(addressParseVO.getName());
            addressRespVO.setProvinceCode(addressParseVO.getProvinceCode());
            addressRespVO.setCityCode(addressParseVO.getCityCode());
            addressRespVO.setDistrictCode(addressParseVO.getCountyCode());
            addressRespVO.setCityName(addressParseVO.getCity());
            addressRespVO.setDistrictName(addressParseVO.getCounty());
            addressRespVO.setProvinceName(addressParseVO.getProvince());
            addressRespVOList.add(addressRespVO);
        }
        return addressRespVOList;
    }

    //用户手动进行复制粘贴多个地址
    @LogRecord(type = ExpressLogRecordConstants.COPE_PASTE_ADDRESS_RESOLUTION,
            fail = "地址解析失败，失败原因：{{#_errorMsg}}",
            success = "接收参数{{#content}}，解析后的结果{{#addressParse}}",
            bizNo = "{{#userId}}")
    public AddressResponse manualParseAddress(AddressListRequest content) {
        log.info("【开始执行用户手动进行复制粘贴多个地址的方法，请求参数为】：{}", content);
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        //调用解析接口
        List<AddressRespVO> addressParseVOS = contentParse(content);
        log.info("【手动解析-调用解析接口返回数据】-----------------：{}", addressParseVOS);
        LogRecordContext.putVariable("addressParse", addressParseVOS);
        // 如果 addressParseVOS 为空，直接返回失败状态
        if (addressParseVOS == null || addressParseVOS.isEmpty()) {
            AddressResponse response = new AddressResponse();
            response.setIsSuccess("0");
            return response;
        }
        String address = content.getAddress();
        Long batchOrderId = null;
        List<AddressRespVO> list = new ArrayList<>();

        AddressResponse addressResponse = new AddressResponse();
        //List<AddressRespVO> addressRespVOS = OrderMapStructMapper.INSTANCE.addressToSaveReqVOList(addressParseVOS);
        addressResponse.setParsedAddresses(addressParseVOS);
        if (addressParseVOS.size() >= 2) {
            //判断解析后的省市区地址返回数据是否为空
            for (AddressRespVO addressParseVO : addressParseVOS) {
                if (addressParseVO.getProvinceName() == null ||
                        addressParseVO.getProvinceName().isEmpty() ||
                        addressParseVO.getCityName() == null ||
                        addressParseVO.getCityName().isEmpty() ||
                        addressParseVO.getDistrictName() == null ||
                        addressParseVO.getDistrictName().isEmpty()) {

                    return addressResponse.setIsSuccess("0");
                }
                // 插入手动解析的批量记录
                BatchOrderDO batchOrderDO = new BatchOrderDO();
                batchOrderDO.setBatchNumber(BatchOrderNumberUtil.generate());
                batchOrderDO.setType(ImportTypeEnum.MANUAL.getValue());
                batchOrderDO.setData(address);
                batchOrderDO.setPersonalId(SecurityFrameworkUtils.getLoginUser().getId());

                batchOrderMapper.insert(batchOrderDO);
                //batchOrderId= batchOrderDO.getId();
                addressResponse.setBatchId(batchOrderDO.getId());
            }
        }
        log.info("【用户手动进行复制粘贴多个地址-调用解析接口返回数据】-----------------：{}", addressResponse);
        return addressResponse;
    }

    /**
     * 物流轨迹回调
     * @param trackCallBackVO
     */
    @Override
    public void trackCallback(TrackCallBackVO trackCallBackVO) {
        if (ObjectUtil.isEmpty(trackCallBackVO)) {
            throw exception(ErrorCodeConstants.PARAMETER_IS_EMPTY);
        }
        //根据快递单号进行查询
        if (orderMapper.selectOrderByTrackingNumber(trackCallBackVO.getExpressSn())) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
    }


    /**
     * 处理Excel文件并同步到SSO服务器
     *
     * @param file Excel文件对象
     * @return
     */
    @LogRecord(type = ExpressLogRecordConstants.EXCEL_ADDRESS_PARSING,
            fail = "Excel地址解析失败，失败原因：{{#_errorMsg}}",
            success = "接收参数{{#file}}，解析后的结果:{{#addressRespVOList}},如果是批量导入批次编号为:{{#batchOrderId}}," +
                    "异步上传文件到SSO服务器,返回url{{#url}}",
            bizNo = "{{#file}}")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public AddressResponse processExcelAndSyncToSSO(MultipartFile file) {
        log.info("【开始处理Excel文件并同步到SSO服务器的方法】-----------------:{}", file);
        LogRecordContext.putVariable("file", file);
        if (file == null || file.isEmpty() || StringUtils.isEmpty(file.getOriginalFilename())) {
            throw exception(ErrorCodeConstants.EXCEL_UPLOAD_ERROR);
        }
        //解析Excel
        List<AddressImportReqVO> importList = null;
        try {
            importList = ExcelUtils.read(file, AddressImportReqVO.class);
        } catch (IOException e) {
            throw exception(ErrorCodeConstants.READ_EXCEL_ERROR, e.getMessage(), e.getMessage());
        }
        //拼接字符串
        StringBuilder str = new StringBuilder();
        for (AddressImportReqVO address : importList) {
            str.append(address.getName()).append(address.getPhone()).append(address.getAddress()).append('\n');
        }
        //调用解析地址的接口
        AddressListRequest addressListRequest = new AddressListRequest();
        addressListRequest.setAddress(String.valueOf(str));
        List<AddressRespVO> addressRespVOList = contentParse(addressListRequest);
        LogRecordContext.putVariable("addressRespVOList", addressRespVOList);
        BatchOrderDO batchOrderDO = BatchOrderDO.builder()
                .batchNumber(BatchOrderNumberUtil.generate())
                .personalId(userId())
                .type(ImportTypeEnum.EXCEL.getValue())
                .build();
        batchOrderMapper.insert(batchOrderDO);
        Long batchOrderId = batchOrderDO.getId();
        log.info("【批量订单id】:--------------------{}", batchOrderDO.getId());
        LogRecordContext.putVariable("batchOrderId", batchOrderId);
        //上传excel文件返回url
        // 异步上传Excel文件，并在上传完成后通过回调更新URL
        CompletableFuture<String> uploadFuture = asyncUploadFile(file);
        uploadFuture.thenAccept(url -> {
            log.info("异步上传文件成功，URL: {}", url);
            LogRecordContext.putVariable("url", url);
            // 更新批量订单，根据batchOrderId更新订单data
            try {
                batchOrderMapper.updateDataById(url, Math.toIntExact(batchOrderId));
            } catch (Exception e) {
                log.error("更新批量订单记录失败，订单ID: {}, 异常信息: {}", batchOrderId, e.getMessage(), e);
            }
            log.info("异步上传完成后更新URL, url: {}", url);
        }).exceptionally(ex -> {
            log.error("异步上传文件失败，异常信息: {}", ex.getMessage(), ex);
            return null;
        });
        AddressResponse addressResponse = new AddressResponse();
        addressResponse.setParsedAddresses(addressRespVOList);
        addressResponse.setBatchId(batchOrderId);
        log.info("【处理Excel文件并同步到SSO服务器的方法结束，响应信息】-----------------:{}", addressResponse);
        return addressResponse;
    }

    /**
     * 异步上传文件的方法
     * @param file
     * @return
     */
    public CompletableFuture<String> asyncUploadFile(MultipartFile file) {
        try {
            File excel = FileUtils.createTempFile(file.getBytes());
            UploadExcelVO uploadExcelVO = new UploadExcelVO();
            uploadExcelVO.setApplicationId(5);
            uploadExcelVO.setFile(excel);
            UploadImageRequest uploadFile = BeanUtils.toBean(uploadExcelVO, UploadImageRequest.class);
            // 使用自定义线程池执行异步任务
            return CompletableFuture.supplyAsync(() -> {
                log.info("开始上传文件");
                try {
                    CommonResult<List<UploadImageResponse>> result = imageApi.uploadImage(uploadFile);
                    log.info("文件上传成功");
                    if (result == null || result.getData() == null || result.getData().isEmpty()) {
                        log.error("文件上传失败，返回结果为空");
                        throw exception(ErrorCodeConstants.UPLOAD_ERROR);
                    }
                    log.info(result.getData().get(0).getImgUrl());
                    return result.getData().get(0).getImgUrl();
                } catch (Exception e) {
                    throw exception(ErrorCodeConstants.UPLOAD_ERROR, e.getMessage());
                }
            }, executor);

        } catch (IOException e) {
            throw exception(ErrorCodeConstants.FILE_TRANSFER_ERROR, e.getMessage());
        }
    }

    /**
     * 获取物流信息
     * @param trackingNumber 订单编号
     * @param id
     * @return
     */
    @LogRecord(type = ExpressLogRecordConstants.TRACKING,
            fail = "Excel地址解析失败，失败原因：{{#_errorMsg}}",
            success = "运单号{{#trackingNumber}}，查询物流结果:{{#orderTrack}}",
            bizNo = "{{#trackingNumber}}")
    public List<ExpressTrackingData> getOrderLogistics(String trackingNumber, Long id) {
        log.info("【开始执行获取物流信息的方法，接收参数】---------------------:运单号：{}", trackingNumber);
        LogRecordContext.putVariable("trackingNumber", trackingNumber);
        if (trackingNumber == null || trackingNumber.isEmpty()) {
            return Collections.emptyList();
        }
        String key = trackingNumber + ":logistics"; // 缓存键
        ListOperations<String, ExpressTrackingData> listOperations = redisTemplate.opsForList();
        try {
            List<ExpressTrackingData> orderTrack = null;
            // 检查缓存键是否存在
            Boolean hasKey = redisTemplate.hasKey(key);
            if (hasKey != null && hasKey) {
                orderTrack = listOperations.range(key, 0, -1); // 从缓存中获取数据
                if (orderTrack != null && !orderTrack.isEmpty()) {
                    return orderTrack;
                }
            }
            // 缓存为空，重新获取物流信息数据
            synchronized (this) { // 防止缓存击穿
                hasKey = redisTemplate.hasKey(key);
                if (hasKey != null && hasKey) {
                    orderTrack = listOperations.range(key, 0, -1);
                    if (orderTrack != null && !orderTrack.isEmpty()) {
                        return orderTrack;
                    }
                }
                // 如果缓存中没有数据，重新获取
                orderTrack = fetchDataFromSource(trackingNumber);
                LogRecordContext.putVariable("orderTrack", orderTrack);
                log.info("重新获取物流信息数据: {}", orderTrack);
                if (orderTrack != null && !orderTrack.isEmpty()) {
                    // 将数据存入缓存
                    listOperations.leftPushAll(key, orderTrack);
                    // 设置缓存过期时间
                    redisTemplate.expire(key, 2, TimeUnit.HOURS);
                    // 更新订单状态
                    updateOrderStatus(orderTrack, key);
                }
            }

            return orderTrack != null ? orderTrack : Collections.emptyList();
        } catch (Exception e) {
            log.error("获取物流信息失败，异常信息: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }


    private void updateOrderStatus(List<ExpressTrackingData> orderTrack, String key) {
        try {
            String latestStatus = getLatestStatus(orderTrack);
            if ("已签收".equals(latestStatus)) {
                latestStatus = OrderStatusTypeEnum.COMPLETED.getStatus();
            }
            OrderSaveReqVO orderSaveReqVO = new OrderSaveReqVO();
            orderSaveReqVO.setTrackingNumber(orderTrack.get(0).getExpressSn());
            orderSaveReqVO.setStatus(latestStatus);
            // 更新订单状态
            orderMapper.updateByOrderId(orderSaveReqVO);
        } catch (Exception e) {
            log.error("更新订单状态失败，异常信息: {}", e.getMessage(), e);
        }
    }

    private List<ExpressTrackingData> fetchDataFromSource(String trackingNumber) {
        log.info("【开始调用物流接口获取物流信息】---------------------:运单号：{}", trackingNumber);
        OrderTrackRequest orderTrackRequest = new OrderTrackRequest();
        orderTrackRequest.setExpressSn(trackingNumber);
        CommonResult<OrderTrackResponse> expressTrack = null;
        try {
            expressTrack = expressApi.queryExpressTrack(orderTrackRequest);
            if (expressTrack.getCode().equals(OrderConstants.CODE)) {
                log.info("【调用物流接口返回物流信息】：" + expressTrack.getData());
            }
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR, e.getMessage());
        }
        ArrayList<Object> trackingObjects = new ArrayList<>();
        trackingObjects.add(expressTrack.getData());
        Object object = trackingObjects.get(0);
        List<ExpressTrackingData> data = (List<ExpressTrackingData>) object;
        log.info("【执行调用物流接口获取物流信息结束】---------------------:运单号：{}", trackingNumber);
        return data;
    }

    /**
     * 取出最新一条的物流状态
     *
     * @param orderTrack
     * @return
     */
    public String getLatestStatus(List<ExpressTrackingData> orderTrack) {
        //定义最新一条的数据
        ExpressTrackingData latestRecord = null;
        LocalDateTime latestTime = null;
        //DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (ExpressTrackingData record : orderTrack) {
            LocalDateTime currentTime = LocalDateTime.parse(record.getCreateTime());
            if (latestTime == null || currentTime.isAfter(latestTime)) {
                latestTime = currentTime;
                latestRecord = record;
            }
        }
        return latestRecord.getStatus();

    }

    /**
     * 接收订单消息
     * @param request 消息接收请求参数对象
     * @return
     */
    @Override
    public String receiveOrderMessage(ReceiveMessageRequest request) {
        //TODO
        //模板未定义,待定
        smsService.sendOrderChangeMessage(null);
        return null;
    }

    /**
     * 查询金额
     * @param startTime
     * @param endTime
     * @return
     */
    @LogRecord(type = ExpressLogRecordConstants.GET_PRICE_QUERY,
            fail = "查询金额失败，失败原因：{{#_errorMsg}}",
            success = "已支付金额{{#paidAmount}}，未支付金额:{{#unpaidAmount}}",
            bizNo = "{{#personalId}}")
    @Override
    public PaymentStatusAmountReqVO getStatusPay(String startTime, String endTime) {
        log.info("【开始执行查询订单金额的方法】---------------------:开始时间：{} 结束时间：{}", startTime, endTime);
        //获取当前登录用户的id
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Long personalId = loginUser.getId();
        LogRecordContext.putVariable("personalId", personalId);
        //已支付
        List<String> paidStatus = Arrays.asList(OrderStatusTypeEnum.PAID.getStatus(),
                OrderStatusTypeEnum.WAIT.getStatus(),
                OrderStatusTypeEnum.PICKUP_PENDING.getStatus(),
                OrderStatusTypeEnum.IN_TRANSIT.getStatus(),
                OrderStatusTypeEnum.PARTIAL_PAYMENT_PENDING.getStatus(),
                OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus(),
                OrderStatusTypeEnum.REFUND_PROCESSING.getStatus(),
                OrderStatusTypeEnum.COMPLETED.getStatus(),
                OrderStatusTypeEnum.REFUNDED.getStatus());

        // 动态处理时间范围
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDateTime startOfDay = null;
        LocalDateTime endOfDay = null;
        // 处理起始时间
        if (startTime != null && !startTime.trim().isEmpty()) {
            LocalDate startDate = LocalDate.parse(startTime, dateFormatter);
            startOfDay = startDate.atStartOfDay(); // 00:00:00
        }
        // 处理结束时间
        if (startTime != null && !startTime.trim().isEmpty()) {
            LocalDate endDate = LocalDate.parse(endTime, dateFormatter);
            endOfDay = endDate.atTime(23, 59, 59); // 23:59:59
        }

        Integer paidAmounts = orderMapper.selectStatusOrders(startOfDay, endOfDay, personalId, paidStatus);
        log.info("【已支付金额】---------------------:{} ", paidAmounts);
        LogRecordContext.putVariable("paidAmount", paidAmounts);

        //未支付
        List<String> unPaidStatus = Arrays.asList(OrderStatusTypeEnum.CREATED.getStatus());

        Integer unpaidAmounts = orderMapper.selectStatusOrders(startOfDay, endOfDay, personalId, unPaidStatus);
        log.info("【未支付金额】---------------------:{} ", unpaidAmounts);
        LogRecordContext.putVariable("unpaidAmount", unpaidAmounts);

        PaymentStatusAmountReqVO amounts = PaymentStatusAmountReqVO.builder()
                .paidAmount(paidAmounts)
                .unpaidAmount(unpaidAmounts)
                .build();
        log.info("【执行查询订单金额的方法结束，最终响应结果】---------------------:{} ", amounts);
        return amounts;
    }

    /**
     * 查询订单列表
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public OrderListRespVO getOrderList(String startTime, String endTime) {
        log.info("【开始执行查询订单列表的方法】---------------------:开始时间：{} 截止时间：{}", startTime, endTime);
        TimeSelectionVO timeSelectionVO = new TimeSelectionVO();
        timeSelectionVO.setStartTime(startTime);
        timeSelectionVO.setEndTime(endTime);
        timeSelectionVO.setPersonalId(SecurityFrameworkUtils.getLoginUserId());
        List<OrderDO> orderDOList = orderMapper.selectOrderList(timeSelectionVO);
        log.info("查询订单集合：{}", orderDOList);
        //已支付列表状态
        List<String> paidStatuses = List.of(
                OrderStatusTypeEnum.PAID.getStatus(),
                OrderStatusTypeEnum.WAIT.getStatus(),
                OrderStatusTypeEnum.PICKUP_PENDING.getStatus(),
                OrderStatusTypeEnum.IN_TRANSIT.getStatus(),
                OrderStatusTypeEnum.PARTIAL_PAYMENT_PENDING.getStatus(),
                OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus(),
                OrderStatusTypeEnum.REFUND_PROCESSING.getStatus(),
                OrderStatusTypeEnum.COMPLETED.getStatus(),
                OrderStatusTypeEnum.REFUNDED.getStatus(),
                OrderStatusTypeEnum.INTERCEPTED.getStatus()
        );
        List<OrderDO> paidOrdersList = orderDOList.stream()
                .filter(order -> paidStatuses.contains(order.getStatus()))
                .collect(Collectors.toList());
        //转换为List<OrderRespVO>
        List<OrderRespVO> paidOrderRespVOList = OrderMapStructMapper.INSTANCE.doListToVoList(paidOrdersList);

        //未支付列表状态
        List<String> unpaidStatuses = List.of(
                OrderStatusTypeEnum.CREATED.getStatus(),
                OrderStatusTypeEnum.NOTPAY.getStatus(),
                OrderStatusTypeEnum.PENDING_ADDITIONAL_FEE.getStatus()
        );

        List<OrderDO> unpaidOrdersList = orderDOList.stream()
                .filter(order -> unpaidStatuses.contains(order.getStatus()))
                .collect(Collectors.toList());
        List<OrderRespVO> unpaidOrderRespVOList = OrderMapStructMapper.INSTANCE.doListToVoList(unpaidOrdersList);

        OrderListRespVO orderListRespVO = new OrderListRespVO();
        orderListRespVO.setPaidOrderList(paidOrderRespVOList);
        orderListRespVO.setUnpaidOrderList(unpaidOrderRespVOList);
        log.info("【执行查询订单列表的方法结束，最终响应结果】---------------------:{} ", orderListRespVO);
        return orderListRespVO;
    }


    /**
     * 查询未支付订单列表
     * @param pageReqVO 分页查询条件参数对象
     * @return
     */
    @Override
    public PageResult<OrderDO> unpaidOrders(OrderPageReqVO pageReqVO) {
        log.info("【开始执行查询未支付订单列表的方法】---------------------:{} ", pageReqVO);
        pageReqVO.setPersonalId(SecurityFrameworkUtils.getLoginUserId());
        PageResult<OrderDO> pageResult = orderMapper.selectUnpaidList(pageReqVO);
        log.info("【查询未支付订单列表的方法结束，最终响应结果】---------------------:{} ", pageResult);
        return pageResult;
    }

    /**
     * 地址导入
     * @param importAddress 待导入的地址解析对象列表
     * @param updateSupport      是否允许更新已存在地址
     * @return
     */
    @Override
    public List<AddressImportReqVO> importAddressList(List<AddressImportReqVO> importAddress, Boolean updateSupport) {
        List<AddressImportReqVO> list = new ArrayList<>();
        for (AddressImportReqVO address : importAddress) {
        }
        return importAddress;
    }

    /**
     * 获取订单费用详情
     * @param orderSaveReqVO 订单保存请求参数对象
     * @return
     */
    @Override
    public List<FeeDetailReqVO> getFeeDetail(OrderSaveReqVO orderSaveReqVO) {
        //TODO
        //省市名称没有地方存，QueryExpressPriceRequest()注解后的内容未填全
        ObjectMapper mapper = new ObjectMapper();
        List<QueryExpressPriceResponse> list = new ArrayList<>();
        List<FeeDetailReqVO> feeDetailReqVOList = OrderMapStructMapper.INSTANCE.ToFeeDetailReqVOList(list);

        try {
            String json = mapper.writeValueAsString(orderSaveReqVO);
            QueryExpressPriceRequest queryExpressPriceRequest = mapper.readValue(json, QueryExpressPriceRequest.class);
            CommonResult<List<QueryExpressPriceResponse>> listCommonResult = expressApi.queryExpressPrice(queryExpressPriceRequest);
            List<QueryExpressPriceResponse> expressPriceItemList = listCommonResult.getData();
            list.addAll(expressPriceItemList);
        } catch (JsonProcessingException e) {
            throw exception(ErrorCodeConstants.JSON_ZH_ERROR, e.getMessage());
        }
        return feeDetailReqVOList;
    }

    /**
     * 获取物流公司列表
     * @param request 物流公司名称模糊匹配关键字
     * @return
     */
    @Override
    public ExpressListResponseVO getFastTrackLogistics(ExpressListRequestVO request) {
        request.setName("");
        ExpressListRequest expressListRequest = BeanUtils.toBean(request, ExpressListRequest.class);
        //调用快递公司接口
        CommonResult<ExpressListResponse> expressList = null;
        try {
            expressList = expressApi.listExpress(expressListRequest);
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR, e.getMessage());
        }
        return BeanUtils.toBean(expressList, ExpressListResponseVO.class);
    }


    /**
     * 获取订单数量
     *
     * @param
     * @return
     */
    @Override
    public OrdersCountQeqVO getOrdersCount() {
        log.info("【开始执行服务统计数据（发单量、已到达、待付款、进行中、已取消）订单数量】---------------------");
        long usrId = userId();
        return orderMapper.getOrdersCount(usrId);
    }

    /**
     * 今日累计订单量
     * @return
     */
    @Override
    public Long todayCountOrdersCount() {

        log.info("【开始执行（今日累计订单量）的方法】---------------------");
        //当天的时间范围
        LocalDateTime startTime = LocalDate.now().atStartOfDay();
        LocalDateTime endTime = startTime.plusDays(1);
        // 创建一个DateTimeFormatter对象，定义所需的格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 使用formatter格式化LocalDateTime对象
        String startTime1 = startTime.format(formatter);
        String endTime1 = endTime.format(formatter);
        long id = userId();
        log.info("【结束执行（今日累计订单量）的方法，返回结果】---------------------:{} ", orderMapper.selectOrderCount(startTime1, endTime1, id));
        return orderMapper.selectOrderCount(startTime1, endTime1, id);
    }

    /**
     * 查询未付款的订单数量
     * @return
     */
    @Override
    public Long counts() {
        log.info("【开始执行（查询未付款的订单数量）的方法】---------------------");
        return orderMapper.selectCountOrders(SecurityFrameworkUtils.getLoginUserId());
    }

    /**
     * 获取当前登录用户id
     *
     * @return
     */
    public long userId() {
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        Long userId = loginUser.getId();
        return userId;
    }


    /**
     * 获取订单管理分页
     * @param status 分页查询
     * @return
     */
    @Override
    public PageResult<OrderDO> getOrderPages(String status) {
        log.info("【开始执行根据订单状态获得订单管理分页的方法】：{}", status);
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) {
            return PageResult.empty();
        }
        Long id = loginUser.getId();
        PageParam pageParam = new PageParam();
        pageParam.setPageNo(1);
        pageParam.setPageSize(10);
        List<String> statusList = null;
        switch (status) {
            //进行中的状态集合
            case "IN_TRANSIT":
                statusList = List.of(
                        OrderStatusTypeEnum.PAID.getStatus(),
                        OrderStatusTypeEnum.WAIT.getStatus(),
                        OrderStatusTypeEnum.PICKUP_PENDING.getStatus(),
                        OrderStatusTypeEnum.PARTIAL_PAYMENT_PENDING.getStatus(),
                        OrderStatusTypeEnum.IN_TRANSIT.getStatus()
                );
                log.info("进行中的状态集合:{}", statusList);
                break;
            //运输中的状态集合
            case "IN_TRANSIT_1":
                statusList = List.of(
                        OrderStatusTypeEnum.IN_TRANSIT.getStatus()

                );
                log.info("进行中的状态集合:{}", statusList);
                break;
            //已完成的状态集合
            case "COMPLETED":
                statusList = List.of(
                        OrderStatusTypeEnum.COMPLETED.getStatus()
                );
                break;
            //已签收的状态集合
            case "COMPLETED1":
                statusList = List.of(
                        OrderStatusTypeEnum.COMPLETED.getStatus()
                );
                break;
            //已取消的状态集合
            case "CANCELED_AFTER_PAYMENT":
                statusList = List.of(
                        OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus(),
                        OrderStatusTypeEnum.CANCELED.getStatus(),
                        OrderStatusTypeEnum.REFUND_PROCESSING.getStatus()
                );
                break;
            //待取件的状态集合
            case "PICKUP_PENDING":
                statusList = List.of(
                        OrderStatusTypeEnum.PICKUP_PENDING.getStatus()
                );
                break;
        }

        PageResult<OrderDO> order = orderMapper.selectOrderPages(id, statusList, pageParam);
        log.info("【结束执行根据订单状态获得订单管理分页的方法，返回信息】：{}", order);
        return order;
    }
}

