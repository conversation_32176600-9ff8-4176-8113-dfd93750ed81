package com.yunyi.express2b.module.express.mq.consumer;

import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.mq.redis.core.pubsub.AbstractRedisChannelMessageListener;
import com.yunyi.express2b.module.express.controller.admin.orderstatus.vo.OrderStatusSaveReqVO;
import com.yunyi.express2b.module.express.mq.message.OrderStatusRefreshMessage;
import com.yunyi.express2b.module.express.service.orderstatus.OrderStatusService;
import com.yunyi.express2b.module.express.enums.OrderStatusTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.ORDER_STATUS_INVALID;

/**
 * 订单状态消费者
 * 可根据业务进行多种实现，如对支付状态的消费者，对物流状态的消费者等。
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/29 12:10
 */
@Slf4j
@Component
public class OrderStatusConsumer extends AbstractRedisChannelMessageListener<OrderStatusRefreshMessage> {

    @Resource
    private OrderStatusService orderStatusService;

    @Override
    public void onMessage(OrderStatusRefreshMessage message) {
        log.info("[OrderStatusConsumer][消息内容({})]", message);
        //结束状态
        String statusEnd = message.getStatusEnd();
        OrderStatusTypeEnum orderStatusTypeEnum = OrderStatusTypeEnum.valueOf(statusEnd);
        //初始状态
        String statusStart = message.getStatusStart();
        OrderStatusTypeEnum StatusTypeEnum = OrderStatusTypeEnum.valueOf(statusStart);
        OrderStatusSaveReqVO orderStatusDO = BeanUtils.toBean(message, OrderStatusSaveReqVO.class);

        switch (orderStatusTypeEnum) {
            // 创建
            case CREATED -> {
                //TODO 创建订单
            }
            // 已支付
            case PAID -> {
                //TODO 支付订单
            }
            // 待接单,已作废,待取件,运输中,已结算
            case WAIT,CANCELED,PICKUP_PENDING,IN_TRANSIT,PARTIAL_PAYMENT_PENDING -> {
                //TODO 待接单
                orderStatusService.createOrderStatus(orderStatusDO);
            }
            // 已取消
            case CANCELED_AFTER_PAYMENT -> {
                //订单状态记录
                switch (StatusTypeEnum){
                    //已支付
                    case PAID -> {
                        orderStatusService.createOrderStatus(orderStatusDO);
                    }
                    //已取消
                    case CANCELED_AFTER_PAYMENT -> {
                        orderStatusService.createOrderStatus(orderStatusDO);
                    }
                    // 待接单
                    case WAIT -> {
                        orderStatusService.createOrderStatus(orderStatusDO);
                    }
                    //待取件
                    case PICKUP_PENDING -> {
                        orderStatusService.createOrderStatus(orderStatusDO);
                    }
                }
            }
            // 退款中
            case REFUND_PROCESSING -> {
                //订单状态记录
                switch (StatusTypeEnum){
                    //已取消
                    case CANCELED_AFTER_PAYMENT -> {
                        orderStatusService.createOrderStatus(orderStatusDO);
                    }
                }
            }
            // 已完成
            case COMPLETED -> {
                //TODO 已完成
            }
            // 已退款
            case REFUNDED -> {
                //TODO 已退款
            }
            // 拦截中
            case INTERCEPTED -> {
                //TODO 拦截中
                //orderStatusService.createOrderStatus(orderStatusDO);
            }
            case PENDING_ADDITIONAL_FEE ->{
                //TODO 待补运费
                orderStatusService.createOrderStatus(orderStatusDO);
            }
            default -> throw exception(ORDER_STATUS_INVALID);
        }
    }
}
