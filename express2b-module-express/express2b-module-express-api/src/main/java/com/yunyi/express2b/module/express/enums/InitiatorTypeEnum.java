package com.yunyi.express2b.module.express.enums;

import com.yunyi.express2b.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 工单发起方类型枚举
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@AllArgsConstructor
public enum InitiatorTypeEnum implements ArrayValuable<Integer> {

    SYSTEM(1,"系统"),
    USER(0,"用户");

    /**
     * 状态值
     */
    private final Integer code;
    /**
     * 状态名
     */
    private final String name;

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(InitiatorTypeEnum::getCode).toArray(Integer[]::new);
    @Override
    public Integer[] array() {
        return ARRAYS;
    }
}