# 5.钱包模块概要设计文档v1.0

# 钱包模块概要设计文档

## 1\. 模块划分 (Module Division)

### 1.1 功能模块划分

钱包模块 (`express2b-module-wallet`) 作为平台的核心业务模块之一，负责全面管理代理商的资金活动。

### 1.2 模块关系

钱包模块与其他模块以及外部系统的关系如下图所示：

```mermaid
graph LR
    subgraph "核心业务模块"
        Order["订单模块 (express2b-module-order)"]
        Commission["分润模块 (express2b-module-commission)"]
        Agent["代理商模块 (express2b-module-agent)"]
        Pricing["定价模块 (express2b-module-pricing)"]
        Wallet["钱包模块 (express2b-module-wallet)"]
        Payment["支付模块 (express2b-module-payment)"]
    end

    subgraph "外部系统与用户端"
        AgentWeb["代理商Web管理界面 (Frontend App)"]
        AdminWeb["管理后台 (Admin Web)"]
        PaymentChannels["支付渠道 (如微信支付, 支付宝)"]
    end

    AgentWeb -->|"查询余额/流水, 申请提现"| Wallet
    AdminWeb -->|"查询/管理钱包, 处理提现, 调账"| Wallet

    Order -- "订单完成/坏账通知" --> Wallet
    Commission -- "分润计算结果" --> Wallet
    Agent -- "代理商信息" --> Wallet
    Pricing -- "定价信息影响利润计算" --> Order
    Pricing -- "定价信息影响利润计算" --> Commission

    Wallet -->|"获取代理商信息"| Agent
    Wallet -- "发起支付/分账请求" --> Payment
    Wallet -->|"记录订单关联流水"| Order
    Wallet -->|"记录分润关联流水"| Commission

    Payment -- "调用支付渠道" --> PaymentChannels
    Payment -- "支付/分账结果通知" --> Wallet


    classDef module fill:#lightgrey,stroke:#333,stroke-width:2px;
    classDef external fill:#lightblue,stroke:#333,stroke-width:2px;
    class Wallet,Order,Commission,Agent,Pricing,Payment module;
    class AgentWeb,AdminWeb,PaymentChannels external;
```
### 1.3 模块职责

钱包模块主要承担以下职责：

- **账户管理**:
   - 为每个代理商创建和管理唯一的虚拟钱包账户。
   - 记录和维护钱包余额，包括可用余额、冻结余额。
- **资金流入**:
   - 接收并处理来自订单模块的代理商自定义溢价利润。
   - 接收并处理来自分润模块的代理商分润收入。
   - 处理平台手动调账增加的资金。
- **资金流出与扣减**:
   - 处理代理商的提现请求，将支付操作委托给支付模块进行处理。
   - 处理因坏账产生的平台零售价扣款。
   - 处理平台手动调账减少的资金。
- **资金冻结与解冻**:
   - 实现资金的普通冻结期机制。
   - 支持因风险控制等特定场景的资金冻结与解冻。
   - 定时任务自动解冻到期资金。
- **流水管理**:
   - 详细记录所有钱包交易，包括交易类型、金额、时间、关联业务单号、交易后余额等。
   - 提供流水查询功能。
- **坏账处理**:
   - 根据订单模块通知，对坏账订单进行相应的资金扣除（平台零售价）。
   - 在坏账收回后，处理相应的分润补发。
- **提现管理**:
   - 支持代理商发起提现申请。
   - 支持平台运营人员审核或系统自动处理提现。
   - 实现基于"最晚提现日"的自动提现机制。
- **对账**:
   - 提供与支付模块的对账支持能力（基础数据记录），支付模块负责与具体支付渠道对账。
- **查询与统计**:
   - 向代理商（通过Web管理界面）提供钱包余额、流水、提现记录的查询。
   - 向平台管理后台提供全面的钱包数据监控和管理功能。
## 2\. 数据对象使用规范 (Data Object Usage Specification)

### 2.1 领域对象 (Domain Object, DO)

DO 用于 `biz` 模块的 `dal` 层和 `service` 层内部，与数据库表结构一一对应。严禁跨模块传递及在 Controller 层使用。

- `AgentWalletDO`** (代理商钱包表)**
   - `id`: BIGINT, 主键
   - `agentId`: BIGINT, 代理商ID (关联代理商模块)
   - `availableBalance`: INT, 可用余额 (单位: 分)
   - `frozenBalance`: INT, 冻结余额 (单位: 分)
   - `totalProfitEarned`: INT, 累计利润收入 (统计用, 单位: 分)
   - `totalCommissionEarned`: INT, 累计分润收入 (统计用, 单位: 分)
   - `totalWithdrawnAmount`: INT, 累计已提现金额 (统计用, 单位: 分)
   - `status`: TINYINT, 钱包状态 (具体取值见字典说明)
   - `creator`: VARCHAR(64), 创建者ID
   - `createTime`: DATETIME, 创建时间
   - `updater`: VARCHAR(64), 更新者ID
   - `updateTime`: DATETIME, 更新时间
   - `deleted`: BOOLEAN, 逻辑删除标志
   - `tenantId`: BIGINT, 租户编号
- `WalletTransactionDO`** (钱包流水表)**
   - `id`: BIGINT, 主键
   - `walletId`: BIGINT, 钱包ID (关联 `AgentWalletDO`)
   - `agentId`: BIGINT, 代理商ID
   - `transactionId`: VARCHAR(64), 唯一交易流水号 (平台生成)
   - `transactionType`: VARCHAR(50), 交易类型 (具体取值见字典说明)
   - `amount`: INT, 交易金额 (正数表示增加，负数表示减少, 单位: 分)
   - `balanceBeforeTransaction`: INT, 交易前余额 (冗余，可选，便于追踪, 单位: 分)
   - `balanceAfterTransaction`: INT, 交易后余额 (单位: 分)
   - `availableBalanceAfterTransaction`: INT, 交易后可用余额 (单位: 分)
   - `frozenBalanceAfterTransaction`: INT, 交易后冻结余额 (单位: 分)
   - `relatedOrderId`: VARCHAR(64), 关联订单号 (可选)
   - `relatedWithdrawalId`: BIGINT, 关联提现申请ID (可选)
   - `relatedEventId`: VARCHAR(64), 关联风险事件ID或其他业务ID (可选)
   - payOrderSn：VARCHAR(64),统一登陆平台的订单支付编号
   - `status`: VARCHAR(30), 流水状态 (具体取值见字典说明)
   - `description`: VARCHAR(255), '交易描述'
   - `availableAt`: DATETIME, 资金预计可用时间 (针对冻结资金)
   - `remark`: VARCHAR(512), '备注信息 (如操作人,调账原因)'
   - `creator`: VARCHAR(64), 创建者ID
   - `createTime`: DATETIME, 创建时间
   - `updater`: VARCHAR(64), 更新者ID
   - `updateTime`: DATETIME, 更新时间
   - `deleted`: BOOLEAN, 逻辑删除标志
   - `tenantId`: BIGINT, 租户编号
- `WithdrawalRequestDO`** (提现申请表)**
   - `id`: BIGINT, 主键
   - `walletId`: BIGINT, 钱包ID
   - `agentId`: BIGINT, 代理商ID
   - `requestNo`: VARCHAR(64), 提现申请唯一编号 (平台生成)
   - `amount`: INT, 申请提现金额 (单位: 分)
   - `actualAmount`: INT, 实际到账金额 (可选, 考虑手续费等, 单位: 分)
   - `feeAmount`: INT, 手续费金额 (单位: 分)
   - `status`: VARCHAR(30), 提现状态 (具体取值见字典说明)
   - `channel`: VARCHAR(30), 提现渠道 (例如：`WECHAT_PROFIT_SHARING`)
   - `channelAccountInfo`: VARCHAR(255), '渠道账户信息 (如微信OpenID, 商户号)'
   - `channelTransactionId`: VARCHAR(128), '渠道交易号 (微信分账订单号/批次号)'
   - `failureReason`: VARCHAR(255), '失败原因'
   - `auditRemark`: VARCHAR(255), '审核备注'
   - `auditorId`: BIGINT, 审核员ID (可选)
   - `requestedAt`: DATETIME, 申请时间
   - `auditedAt`: DATETIME, 审核时间 (可选)
   - `processingStartedAt`: DATETIME, 开始处理时间（调用三方接口）
   - `completedAt`: DATETIME, 完成时间 (可选)
   - `isAutoTriggered`: BOOLEAN, 是否自动提现触发
   - `creator`: VARCHAR(64), 创建者ID
   - `createTime`: DATETIME, 创建时间
   - `updater`: VARCHAR(64), 更新者ID
   - `updateTime`: DATETIME, 更新时间
   - `deleted`: BOOLEAN, 逻辑删除标志
   - `tenantId`: BIGINT, 租户编号
### 2.2 数据传输对象 (Data Transfer Object, DTO)

DTO 用于 `api` 子模块定义的接口参数和返回值，以及模块间通信。

- `WalletBalanceUpdateDTO`** (钱包余额更新DTO)**: 用于其他模块通知钱包模块更新余额。
   - `agentId`: Long, 代理商ID
   - `amount`: Integer, 变动金额 (正负表示增减, 单位: 分)
   - `transactionType`: String, 交易类型 (枚举)
   - `relatedOrderId`: String (可选), 关联订单ID
   - `description`: String, 描述
   - `isFrozen`: Boolean (可选, default true), 是否直接进入冻结状态
   - `freezeDays`: Integer (可选), 如果 `isFrozen` 为 true, 指定冻结天数 (优先于默认冻结期)
   - `idempotencyKey`: String, 幂等键
- `WalletQueryDTO`** (钱包查询DTO)**: 用于查询钱包信息。
   - `agentId`: Long, 代理商ID
- `WithdrawalRequestCreateDTO`** (创建提现申请DTO)**: 代理商发起提现时使用。
   - `agentId`: Long, 代理商ID
   - `amount`: Integer, 提现金额 (单位: 分)
   - `channel`: String (可选, 默认微信), 提现渠道
- `WithdrawalProcessDTO`** (提现处理DTO)**: 管理后台处理提现时使用。
   - `withdrawalRequestId`: Long, 提现申请ID
   - `approved`: Boolean, 是否批准
   - `auditorId`: Long (可选), 审核员ID
   - `remark`: String (可选), 备注
- `WalletTransactionRecordDTO`** (钱包交易记录DTO)**: 钱包模块对外提供交易记录时使用。
   - `transactionId`: String:流水表ID
   - `transactionType`: String：流水类型
   - `amount`: Integer, 交易金额 (单位: 分)
   - `balanceAfterTransaction`: Integer, 交易后余额 (单位: 分)
   - `status`: String
   - `description`: String
   - `createTime`: LocalDateTime
   - `availableAt`: LocalDateTime (可选)
- `WalletDetailsDTO`** (钱包详情DTO)**: 供其他服务获取钱包综合信息。
   - `agentId`: Long
   - `availableBalance`: Integer, 可用余额 (单位: 分)
   - `frozenBalance`: Integer, 冻结余额 (单位: 分)
### 2.3 视图对象 (View Object, VO)

VO 用于 `controller` 层的方法参数和返回值，面向前端展示（包括代理商Web管理界面和平台管理后台）。

- `WalletBalanceRespVO`** (钱包余额响应VO)**: 代理商Web管理界面展示代理商钱包余额。
   - `availableBalance`: Integer, 可用余额 (单位: 分)
   - `frozenBalance`: Integer, 冻结余额 (单位: 分)
   - `totalWithdrawnAmount`: Integer, 累计已提现 (单位: 分)
- `WalletTransactionDetailRespVO`** (钱包流水详情响应VO)**: 代理商Web管理界面展示单条流水详情。
   - `transactionId`: String, 交易流水号
   - `transactionType`: String, 交易类型 (中文描述)
   - `amount`: Integer, 交易金额 (单位: 分)
   - `balanceAfterTransaction`: Integer, 交易后余额 (单位: 分)
   - `status`: String, 状态 (中文描述)
   - `description`: String, 描述
   - `createTime`: String, ("yyyy-MM-dd HH:mm:ss")
   - `availableAt`: String (可选, "yyyy-MM-dd HH:mm:ss"), 解冻时间
   - `relatedOrderId`: String (可选)
- `WithdrawalRequestCreateReqVO`** (创建提现申请请求VO)**: 代理商Web管理界面提交提现申请。
   - `amount`: Integer, 提现金额 (单位: 分)
   - `paymentPassword`: String (可选, 如果需要支付密码)
- `WithdrawalRequestRecordRespVO`** (提现记录响应VO)**: 代理商Web管理界面展示提现记录。
   - `requestNo`: String, 申请编号
   - `amount`: Integer, 申请金额 (单位: 分)
   - `status`: String, 状态 (中文描述)
   - `channel`: String, 提现渠道 (中文描述)
   - `requestedAt`: String, ("yyyy-MM-dd HH:mm:ss")
   - `completedAt`: String (可选, "yyyy-MM-dd HH:mm:ss")
   - `failureReason`: String (可选)
- `WalletManualAdjustmentReqVO`** (手动调账请求VO)**: 平台管理后台手动调账参数。
   - `agentId`: Long
   - `amount`: Integer, 金额 (正负表示增减, 单位: 分)
   - `transactionType`: String (必须是 `ADJUSTMENT_IN` 或 `ADJUSTMENT_OUT`)
   - `description`: String (调账原因，必填)
   - `remark`: String (操作备注，必填)
- `WalletDetailsRespVO`** (钱包详情响应VO)**: 平台管理后台展示代理商钱包综合信息。
   - `agentId`: Long, 代理商ID
   - `agentName`: String, 代理商名称 (可能需要联查Agent模块)
   - `availableBalance`: Integer, 可用余额 (单位: 分)
   - `frozenBalance`: Integer, 冻结余额 (单位: 分)
   - `totalProfitEarned`: Integer, 累计利润总额 (单位: 分)
   - `totalCommissionEarned`: Integer, 累计分润总额 (单位: 分)
   - `totalWithdrawnAmount`: Integer, 累计已提现总额 (单位: 分)
   - `status`: Integer, 钱包状态 (数值)
   - `statusName`: String, 钱包状态 (中文描述)
   - `createTime`: String, ("yyyy-MM-dd HH:mm:ss")
   - `updateTime`: String, ("yyyy-MM-dd HH:mm:ss")
- `WalletTransactionPageReqVO`** (钱包流水分页查询请求VO)**: 代理商Web管理界面分页查询流水时的参数。
   - 继承自: `com.yunyi.express2b.framework.common.pojo.PageParam`
   - `transactionType`: String (可选)
   - `status`: String (可选)
   - `startDate`: String (可选, "yyyy-MM-dd")
   - `endDate`: String (可选, "yyyy-MM-dd")
- `WithdrawalRequestPageReqVO`** (提现申请分页查询请求VO)**: 平台管理后台分页查询提现申请的参数。
   - 继承自: `com.yunyi.express2b.framework.common.pojo.PageParam`
   - `agentId`: Long (可选, 代理商ID)
   - `status`: String (可选, 提现状态筛选)
   - `requestNo`: String (可选, 申请编号)
   - `startDate`: String (可选, "yyyy-MM-dd", 申请开始日期)
   - `endDate`: String (可选, "yyyy-MM-dd", 申请结束日期)
- `WalletPageReqVO`** (钱包列表分页查询请求VO)**: 平台管理后台分页查询代理商钱包列表的参数。
   - 继承自: `com.yunyi.express2b.framework.common.pojo.PageParam`
   - `agentId`: Long (可选, 代理商ID)
   - `agentName`: String (可选, 代理商名称)
   - `status`: Integer (可选, 钱包状态)
   - `minAvailableBalance`: Integer (可选, 最小可用余额, 单位: 分)
   - `maxAvailableBalance`: Integer (可选, 最大可用余额, 单位: 分)
- `WithdrawalRequestClientPageReqVO`** (客户端提现记录分页查询请求VO)**: 代理商Web管理界面分页查询提现记录的参数。
   - 继承自: `com.yunyi.express2b.framework.common.pojo.PageParam`
   - `status`: String (可选, 提现状态筛选)
   - `startDate`: String (可选, "yyyy-MM-dd", 申请开始日期)
   - `endDate`: String (可选, "yyyy-MM-dd", 申请结束日期)
## 2.4 字典说明 (Dictionary Explanation)

本节说明钱包模块中主要状态字段的取值和含义。

### 2.4.1 钱包状态 (`AgentWalletDO.status`)

| 取值 | 含义       | 说明                                                |
|------|------------|-----------------------------------------------------|
| 1    | "正常"     | 钱包账户状态正常，可进行所有合规操作。                |
| 2    | "冻结提现" | 钱包账户提现功能被冻结，资金可能正常流入，但不可提现。 |
| 3    | "已注销"   | 钱包账户已注销，不可再进行任何操作。                  |

### 2.4.2 流水状态 (`WalletTransactionDO.status`)

| 取值      | 含义     | 说明                                               |
|-----------|----------|----------------------------------------------------|
| FROZEN    | "冻结中" | 资金已进入钱包，但处于冻结期，不可用。                |
| AVAILABLE | "可用"   | 资金已度过冻结期，可供提现或冲抵负余额。             |
| COMPLETED | "已完成" | 交易已成功完成并影响余额（如提现成功扣款，坏账扣款）。 |
| FAILED    | "失败"   | 交易尝试失败（如提现分账失败）。                      |
| CANCELLED | "已取消" | 交易已取消（如提现申请取消）。                        |

### 2.4.3 提现状态 (`WithdrawalRequestDO.status`)

| 取值            | 含义     | 说明                                           |
|-----------------|----------|------------------------------------------------|
| REQUESTED       | "已申请" | 代理商已提交提现申请，等待处理。                 |
| APPROVE         | "审批中" | 平台已接收申请，正在进行内部处理或调用渠道接口。 |
| APPROVED        | "已批准" | 平台已审核通过，等待渠道处理。                   |
| REJECTED        | "已拒绝" | 提现申请被平台拒绝。                            |
| COMPLETED       | "已完成" | 提现已成功打款至代理商账户。                    |
| FAILED          | "失败"   | 提现打款失败（渠道返回失败）。                    |
| CANCELLED       | "已取消" | 提现申请被代理商或平台取消。                    |
| AUTO_PROCESSING | "处理中" | 自动提现任务触发，正在处理中。                   |

### 2.4.4 交易类型 (`WalletTransactionDO.transaction_type`)

| 取值                  | 含义               | 说明                                                                                                                                 |
|-----------------------|--------------------|--------------------------------------------------------------------------------------------------------------------------------------|
| PROFIT_EARNED         | "溢价利润收入"     | 代理商通过终端销售价与平台零售价差额获得的利润。                                                                                      |
| COMMISSION_EARNED     | "分润收入"         | 代理商从下级订单中获得的分润。                                                                                                        |
| BAD_DEBT_DEDUCTION    | "坏账扣款"         | 因坏账订单从代理商钱包扣除的平台零售价。                                                                                              |
| BAD_DEBT_REFUND       | "坏账返还扣款"     | 坏账收回后返还之前扣除的平台零售价（此类型在v1.0规划中已调整为不返还到钱包，仅作为历史记录或被COMMISSION_RECOVERY替代，具体实现需确认）。 |
| COMMISSION_RECOVERY   | "坏账收回补发分润" | 坏账收回后重新计算并补发的分润。                                                                                                      |
| WITHDRAWAL_REQUESTED  | "提现申请"         | 代理商发起提现申请的流水记录。                                                                                                        |
| WITHDRAWAL_COMPLETED  | "提现成功"         | 提现成功打款到代理商账户的扣款流水。                                                                                                  |
| WITHDRAWAL_FAILED     | "提现失败"         | 提现打款失败的记录流水。                                                                                                              |
| ADJUSTMENT_IN         | "平台调账(增加)"   | 平台手动为代理商钱包增加金额。                                                                                                        |
| ADJUSTMENT_OUT        | "平台调账(减少)"   | 平台手动从代理商钱包扣减金额。                                                                                                        |
| RISK_CONTROL_FREEZE   | "风险控制冻结"     | 因风险事件触发的资金冻结。                                                                                                            |
| RISK_CONTROL_UNFREEZE | "风险控制解冻"     | 风险事件解除后的资金解冻。                                                                                                            |

## 3\. 接口设计 (Interface Design)

### 3.1. 模块间接口 (`WalletApi`)

定义在 `express2b-module-wallet-api` 模块，供其他内部模块调用。

- **接口名称**: `WalletApi.java`
- **定义位置**: `com.yunyi.express2b.module.wallet.api.WalletApi`

```java
public interface WalletApi {

    /**
     * 获取代理商钱包详情 (包含可用余额和冻结余额)
     *
     * @param agentId 代理商ID
     * @return 钱包详情DTO
     */
    CommonResult<WalletDetailsDTO> getWalletDetails(Long agentId);

    /**
     * 增加钱包余额 (通用入账接口)
     *
     * @param updateDTO 余额更新信息
     * @return 操作结果，成功时data为新生成的交易流水ID
     */
    CommonResult<String> credit(WalletBalanceUpdateDTO updateDTO);

    /**
     * 扣减钱包余额 (通用出账接口)
     *
    * @param updateDTO 余额更新信息 (amount应为正数，表示扣减的额度)
     * @return 操作结果，成功时data为新生成的交易流水ID
     */
    CommonResult<String> debit(WalletBalanceUpdateDTO updateDTO);

    /**
     * 冻结指定金额
     *
     * @param agentId 代理商ID
     * @param amount 需要冻结的金额 (从可用余额转入冻结余额)
     * @param transactionType 冻结类型 (如 RISK_CONTROL_FREEZE)
     * @param relatedEventId 关联事件ID
     * @param description 描述
     * @return 操作结果，成功时data为新生成的交易流水ID
     */
    CommonResult<String> freezeAmount(Long agentId, BigDecimal amount, String transactionType, String relatedEventId, String description);

    /**
     * 解冻指定金额
     *
     * @param agentId 代理商ID
     * @param amount 需要解冻的金额 (从冻结余额转入可用余额)
     * @param transactionType 解冻类型 (如 RISK_CONTROL_UNFREEZE)
     * @param relatedFreezeTransactionId 关联的原冻结流水ID (可选)
     * @param description 描述
     * @return 操作结果，成功时data为新生成的交易流水ID
     */
    CommonResult<String> unfreezeAmount(Long agentId, BigDecimal amount, String transactionType, String relatedFreezeTransactionId, String description);

    /**
     * 处理订单坏账扣款
     *
     * @param agentId 产生坏账订单的代理商ID
     * @param orderId 坏账订单ID
     * @param deductionAmount 需扣除的平台零售价金额
     * @return 操作结果
     */
    CommonResult<Void> processBadDebtDeduction(Long agentId, String orderId, BigDecimal deductionAmount);

    /**
     * 处理坏账收回后的分润补发 (仅创建"可用"状态的分润流水)
     *
     * @param agentId 获得补发分润的代理商ID
     * @param orderId 坏账已收回的订单ID
     * @param commissionAmount 补发的分润金额
     * @return 操作结果，成功时data为新生成的交易流水ID
     */
    CommonResult<String> processBadDebtRecoveryCommission(Long agentId, String orderId, BigDecimal commissionAmount);
}
```
 


### 3.2. 对外接口 (Controller 设计)

#### 3.2.1. 面向代理商的接口 (`app-api`)

供代理商通过其Web管理界面调用。

- **包路径**: `com.yunyi.express2b.module.wallet.controller.app.v1`

1. **获取我的钱包余额**

   - **接口分类**: 前端接口 (供代理商Web管理界面使用)
   - **接口路径**: `/wallet/balance`
   - **HTTP 方法**: GET
   - **功能描述**: 查询当前登录代理商的钱包余额信息。
   - **请求参数**: 无
   - **响应**:
      - **响应体类型**: JSON
      - **数据结构**: `CommonResult<WalletBalanceRespVO>`
         - `code`: Integer (成功: 0, 具体错误码见错误码管理)
         - `data`: `WalletBalanceRespVO`
            - `availableBalance`: Integer, 可用余额 (单位: 分)
            - `frozenBalance`: Integer, 冻结余额 (单位: 分)
            - `totalWithdrawnAmount`: Integer, 累计已提现 (单位: 分)
         - `msg`: String (失败时)
   - **错误处理**:
      - `GlobalErrorCodeConstants.UNAUTHORIZED` (用户未登录)
      - `WalletErrorCodeConstants.WALLET_NOT_FOUND`
   - **认证/授权要求**: 需要用户Token认证。
2. **查询我的钱包流水**

   - **接口分类**: 前端接口 (供代理商Web管理界面使用)
   - **接口路径**: `/wallet/transactions`
   - **HTTP 方法**: GET
   - **功能描述**: 分页查询当前登录代理商的钱包流水记录。
   - **请求参数**:
      - 查询参数 (`WalletTransactionPageReqVO`):
         - `transactionType`: String (可选, 交易类型筛选)
         - `status`: String (可选, 流水状态筛选)
         - `startDate`: String (可选, "yyyy-MM-dd", 开始日期)
         - `endDate`: String (可选, "yyyy-MM-dd", 结束日期)
         - 分页参数由 `PageParam` 父类提供
   - **响应**:
      - **响应体类型**: JSON
      - **数据结构**: `CommonResult<PageResult<WalletTransactionDetailRespVO>>` (假设 `PageResult` 是分页结果封装类)
         - `code`: Integer
         - `data`: `PageResult`
            - `list`: List<`WalletTransactionDetailRespVO`\>
            - `total`: Long, 总记录数
         - `msg`: String
   - **错误处理**:
      - `GlobalErrorCodeConstants.UNAUTHORIZED`
      - `GlobalErrorCodeConstants.BAD_REQUEST` (参数校验失败)
      - `WalletErrorCodeConstants.WALLET_NOT_FOUND`
   - **认证/授权要求**: 需要用户Token认证。
3. **发起提现申请**

   - **接口分类**: 前端接口 (供代理商Web管理界面使用)
   - **接口路径**: `/wallet/withdrawal-requests`
   - **HTTP 方法**: POST
   - **功能描述**: 代理商通过Web管理界面提交提现申请。
   - **请求参数**:
      - 请求体 (Request Body): `WithdrawalRequestCreateReqVO`
         - `amount`: Integer (必须, 提现金额)
         - `paymentPassword`: String (可选, 根据安全策略决定是否需要)
   - **响应**:
      - **响应体类型**: JSON
      - **数据结构**: `CommonResult<String>` (data为提现申请编号 `requestNo`)
         - `code`: Integer
         - `data`: String (提现申请编号)
         - `msg`: String
   - **错误处理**:
      - `GlobalErrorCodeConstants.UNAUTHORIZED`
      - `GlobalErrorCodeConstants.BAD_REQUEST` (参数校验失败, 如金额小于最低提现额)
      - `WalletErrorCodeConstants.WALLET_NOT_FOUND`
      - `WalletErrorCodeConstants.INSUFFICIENT_AVAILABLE_BALANCE`
      - `WalletErrorCodeConstants.WITHDRAWAL_AMOUNT_TOO_LOW`
      - `WalletErrorCodeConstants.WITHDRAWAL_REQUEST_PENDING_EXISTS` (已有处理中的提现申请)
   - **认证/授权要求**: 需要用户Token认证。
4. **查询我的提现记录**

   - **接口分类**: 前端接口 (供代理商Web管理界面使用)
   - **接口路径**: `/wallet/withdrawal-requests`
   - **HTTP 方法**: GET
   - **功能描述**: 分页查询当前登录代理商的提现记录。
   - **请求参数**:
      - 查询参数 (`WithdrawalRequestClientPageReqVO`):
         - `status`: String (可选, 提现状态筛选)
         - `startDate`: String (可选, "yyyy-MM-dd", 申请开始日期)
         - `endDate`: String (可选, "yyyy-MM-dd", 申请结束日期)
         - 分页参数由 `PageParam` 父类提供
   - **响应**:
      - **响应体类型**: JSON
      - **数据结构**: `CommonResult<PageResult<WithdrawalRequestRecordRespVO>>`
         - `code`: Integer
         - `data`: `PageResult`
            - `list`: List<`WithdrawalRequestRecordRespVO`\>
            - `total`: Long, 总记录数
         - `msg`: String
   - **错误处理**:
      - `GlobalErrorCodeConstants.UNAUTHORIZED`
      - `GlobalErrorCodeConstants.BAD_REQUEST`
   - **认证/授权要求**: 需要用户Token认证。
#### 3.2.2. 后端接口 (`admin-api`)

供平台管理后台调用。

- **包路径**: `com.yunyi.express2b.module.wallet.controller.admin`

1. **查询代理商钱包列表 (管理后台)**

   - **接口分类**: 后端接口
   - **接口路径**: `/wallet/list`
   - **HTTP 方法**: GET
   - **功能描述**: 分页查询代理商钱包信息。
   - **请求参数**:
      - 查询参数 (`WalletPageReqVO`):
         - `agentId`: Long (可选, 代理商ID)
         - `agentName`: String (可选, 代理商名称)
         - `status`: Integer (可选, 钱包状态)
         - `minAvailableBalance`: Integer (可选, 最小可用余额, 单位: 分)
         - `maxAvailableBalance`: Integer (可选, 最大可用余额, 单位: 分)
         - 分页参数由 `PageParam` 父类提供
   - **响应**:
      - **响应体类型**: JSON
      - **数据结构**: `CommonResult<PageResult<WalletDetailsRespVO>>`
   - **错误处理**: `GlobalErrorCodeConstants.BAD_REQUEST`
   - **认证/授权要求**: 管理员Token认证，需要 "wallet:query" 权限。
2. **查询指定代理商钱包流水 (管理后台)**

   - **接口分类**: 后端接口
   - **接口路径**: `/wallet/{agentId}/transactions`
   - **HTTP 方法**: GET
   - **功能描述**: 分页查询指定代理商的钱包流水。
   - **请求参数**:
      - 路径参数: `agentId`: Long (必须)
      - 查询参数 (`WalletTransactionQueryReqVO`): (同前端查询)
   - **响应**: `CommonResult<PageResult<WalletTransactionDetailRespVO>>`
   - **错误处理**: `GlobalErrorCodeConstants.BAD_REQUEST`, `WalletErrorCodeConstants.AGENT_WALLET_NOT_FOUND`
   - **认证/授权要求**: 管理员Token认证，需要 "wallet:transaction:query" 权限。
3. **查询提现申请列表 (管理后台)**

   - **接口分类**: 后端接口
   - **接口路径**: `/wallet/withdrawal-requests/list`
   - **HTTP 方法**: GET
   - **功能描述**: 分页查询提现申请。
   - **请求参数**:
      - 查询参数 (`WithdrawalRequestPageReqVO`):
         - `agentId`: Long (可选, 代理商ID)
         - `status`: String (可选, 提现状态筛选)
         - `requestNo`: String (可选, 申请编号)
         - `startDate`: String (可选, "yyyy-MM-dd", 申请开始日期)
         - `endDate`: String (可选, "yyyy-MM-dd", 申请结束日期)
         - 分页参数由 `PageParam` 父类提供
   - **响应**: `CommonResult<PageResult<WithdrawalRequestRecordRespVO>>`
   - **错误处理**: `GlobalErrorCodeConstants.BAD_REQUEST`
   - **认证/授权要求**: 管理员Token认证，需要 "wallet:withdrawal:query" 权限。
4. **处理提现申请 (审核/打款标记) (管理后台)**

   - **接口分类**: 后端接口
   - **接口路径**: `/wallet/withdrawal-requests/process`
   - **HTTP 方法**: POST
   - **功能描述**: 审核或标记提现申请的处理状态。实际打款通过微信分账API异步完成，这里主要更新状态。
   - **请求参数**:
      - 请求体: `WithdrawalProcessDTO`
         - `withdrawalRequestId`: Long (必须)
         - `approved`: Boolean (必须, true为通过/打款成功, false为拒绝/打款失败)
         - `auditorId`: Long (可选), 审核员ID
         - `remark`: String (必须, 审核备注或失败原因)
   - **响应**: `CommonResult<Void>`
   - **错误处理**: `GlobalErrorCodeConstants.BAD_REQUEST`, `WalletErrorCodeConstants.WITHDRAWAL_REQUEST_NOT_FOUND`, `WalletErrorCodeConstants.WITHDRAWAL_REQUEST_STATUS_INVALID`
   - **认证/授权要求**: 管理员Token认证，需要 "wallet:withdrawal:process" 权限。
5. **手动调账 (管理后台)**

   - **接口分类**: 后端接口
   - **接口路径**: `/wallet/adjust`
   - **HTTP 方法**: POST
   - **功能描述**: 为代理商钱包手动增加或扣除金额。
   - **请求参数**:
      - 请求体: `WalletManualAdjustmentReqVO`
         - `agentId`: Long (必须)
         - `amount`: Integer (必须, 正数增加, 负数扣减)
         - `transactionType`: String (必须, `ADJUSTMENT_IN` 或 `ADJUSTMENT_OUT`)
         - `description`: String (必须, 调账原因)
         - `remark`: String (必须, 操作员备注)
   - **响应**: `CommonResult<String>` (data为新生成的交易流水ID)
   - **错误处理**: `GlobalErrorCodeConstants.BAD_REQUEST`, `WalletErrorCodeConstants.AGENT_WALLET_NOT_FOUND`, `WalletErrorCodeConstants.ADJUSTMENT_AMOUNT_INVALID`
   - **认证/授权要求**: 管理员Token认证，需要 "wallet:adjust" 权限。
### 3.3. 接口契约

- **稳定性**: API接口一旦发布，应保持稳定。参数和返回值的修改需谨慎，并通过版本管理。
- **版本管理**: 面向代理商的接口 (`app-api`) 通过URL路径中的 `v1`, `v2` 进行版本控制。
- **RESTful设计**: 严格遵循RESTful风格。
- **幂等性**: 关键的写操作（如 `credit`, `debit`, `processBadDebtDeduction`, `adjust`）应通过 `idempotencyKey` 或业务自身的唯一标识（如 `relatedOrderId` 结合 `transactionType`）来保证幂等性。提现申请创建也应有幂等性校验。
- **Swagger文档**: 所有Controller层接口使用Swagger注解 (`@ApiOperation`, `@ApiImplicitParams` 等) 自动生成API文档，包含详细的参数说明、响应示例和错误码。

#### 3.3.1 幂等性设计

为确保钱包模块资金操作的安全性和准确性，所有资金相关操作必须实现幂等性。幂等性通过`idempotencyKey`(幂等键)实现。

**幂等键的作用**:

- 防止网络波动导致的重复请求执行多次
- 确保资金操作（入账、扣款、提现等）只被执行一次
- 防止生成重复的流水记录
- 避免错误的余额计算

**幂等键格式规范**:

- 幂等键必须唯一，建议使用 UUID 或遵循特定业务规则生成
- 长度不超过64个字符
- 不允许包含特殊字符（仅支持字母、数字、下划线、中划线）

**业务场景幂等键生成规则**:

1. **分润入账**: `COMMISSION_{orderId}_{agentId}`

   - 例如：`COMMISSION_ORD2023112800001_10086`
2. **利润入账**: `PROFIT_{orderId}_{agentId}`

   - 例如：`PROFIT_ORD2023112800001_10086`
3. **坏账扣款**: `BAD_DEBT_{orderId}_{agentId}`

   - 例如：`BAD_DEBT_ORD2023112800001_10086`
4. **坏账补发分润**: `BAD_DEBT_RECOVERY_{orderId}_{agentId}`

   - 例如：`BAD_DEBT_RECOVERY_ORD2023112800001_10086`
5. **提现申请**: `WITHDRAWAL_REQUEST_{timestamp}_{agentId}`

   - 例如：`WITHDRAWAL_REQUEST_20231128143022_10086`
6. **平台调账**: `ADJUSTMENT_{operationId}_{timestamp}`

   - 例如：`ADJUSTMENT_OP20231128001_20231128143022`
**幂等键处理机制**:

1. 系统维护一个已处理幂等键的记录表，包含：幂等键、处理结果、处理时间、失效时间
2. 每次请求先查询幂等键是否已存在：

   - 如已存在且未过期，直接返回上次的处理结果
   - 如不存在或已过期，执行业务逻辑并记录结果
3. 幂等键默认有效期为7天，过期后自动失效

**注意事项**:

- 调用方必须在重要资金操作请求中提供幂等键
- 对于无法生成业务相关幂等键的场景，可使用UUID作为幂等键
- 相同的业务操作必须使用相同的幂等键，以确保幂等性

## 4\. 数据流和交互 (Data Flow and Interaction)

### 4.1. 关键业务流程数据流图

#### 4.1.1. 资金入账流程 (利润/分润到账)

```mermaid
graph LR
    Start([订单完成]) -- "1. 用户付款完成（接受到支付回调为准）" --> O
    O[订单模块]
    W[分润模块]
    O -- "2.计算分润" --> W
    W -- "3.分润计算完成通知 (CommissionShareDTO)" --> O
    O-- "4.订单完成通知 (OrderInfoDTO)" --> WalletService
    
    subgraph "Wallet Module"
        WalletService["WalletService.credit()"] -- "7. 构造 WalletBalanceUpdateDTO" --> WalletService
    end
    
    subgraph "Database Operations"
        WalletService -- "5. 创建 WalletTransactionDO" --> DB1[(wallet_transactions 表)]
        WalletService -- "6. 更新 AgentWalletDO (冻结/可用余额)" --> DB2[(agent_wallets 表)]
    end

    WalletService -- "8. 返回操作结果 (WalletBalanceUpdateDTO)" --> O
```
**数据对象说明**:

- **OrderInfoDTO** 包含:
   - agentId - 代理商ID
   - orderId - 订单ID
   - profitAmount - 代理商利润
   - isBadDebt - 是否坏账
- **CommissionShareDTO** 包含:
   - agentId - 获得分润的代理商
   - orderId - 订单ID
   - commissionAmount - 分润金额
- **WalletBalanceUpdateDTO** 包含:
   - agentId - 代理商ID
   - amount - 利润或分润金额（正数）
   - transactionType - 交易类型(PROFIT_EARNED/COMMISSION_EARNED)
   - relatedOrderId - 关联订单ID
   - isFrozen - 是否进入冻结期
   - freezeDays - 冻结天数(如有特定冻结期)
   - idempotencyKey - 幂等键
#### 4.1.2. 坏账扣款流程

- 创建一条类型为 `BAD_DEBT_DEDUCTION` 的钱包流水，金额为 **负** 的该订单对应的平台零售价（首重+续重），状态为“已完成”，记录关联订单号。
- **直接扣减** 代理商钱包余额。**优先扣减可用余额，若不足，则继续扣减冻结余额，允许最终可用余额变为负数**。

```mermaid
graph TD
    OrderModule["订单模块"] -- "1. 订单标记坏账通知 (BadDebtInfoDTO)" --> WalletApiImpl["WalletApiImpl.processBadDebtDeduction()"]

    subgraph Wallet Module
        WalletApiImpl -- "2. 构造 WalletBalanceUpdateDTO" --> WalletService["WalletService.debit()"]
        WalletService -- "3. 创建 WalletTransactionDO (BAD_DEBT_DEDUCTION)" --> DB1[(wallet_transactions 表)]
        WalletService -- "4. 更新 AgentWalletDO (扣减可用/冻结余额)" --> DB2[(agent_wallets 表)]
    end

    WalletApiImpl -- "5. 返回操作结果" --> OrderModule
```
**数据对象说明**:

- **BadDebtInfoDTO** 包含:
   - agentId - 产生坏账订单的代理商
   - orderId - 订单ID
   - platformRetailPrice - 需扣除的平台零售价
- **WalletBalanceUpdateDTO** 包含:
   - agentId - 代理商ID
   - amount - 扣款金额(platformRetailPrice, 负数)
   - transactionType - 交易类型(BAD_DEBT_DEDUCTION)
   - relatedOrderId - 关联订单ID
   - idempotencyKey - 幂等键
#### 4.1.3. 代理商提现流程 (手动/申请触发)

序列号5缺失了，不用理会。

```mermaid
graph LR
    Start([业务起点]) --> AgentWeb["代理商Web管理界面"]
    AgentWeb -- "1提交提现申请" --> WalletAppController["WalletAppController.requestWithdrawal()"]

    subgraph WalletModule
        WalletAppController -- "2调用WalletService" --> WalletService["WalletService.requestWithdrawal()"]
        WalletService -- "3创建WithdrawalRequestDO" --> Database
        WalletService -- "4预冻结可用余额" --> Database
    end
    WalletService -- "6返回申请结果" --> WalletAppController
    WalletAppController -- "7响应前端" --> AgentWeb

    AdminPortal["管理后台"] -- "8审核员查询待处理申请" --> WalletAdminController["WalletAdminController.listWithdrawalRequests()"]
    WalletAdminController -- "9返回提现申请列表" --> AdminPortal

    AdminPortal -- "10提交审核结果" --> WalletAdminControllerProcess["WalletAdminController.processWithdrawal()"]

    subgraph WalletModuleProcessing
        WalletAdminControllerProcess -- "11调用WalletService" --> WalletServiceProcess["WalletService.processWithdrawalApproval()"]
        WalletServiceProcess -- "12更新WithdrawalRequestDO" --> Database
        WalletServiceProcess -- "13调用支付模块进行分账" --> PaymentModuleAPI["支付模块API (PaymentModule.executePayout())"]
        PaymentModuleAPI -- "14分账结果通知" --> WalletServiceProcess
    end

    subgraph SuccessPath
        WalletServiceProcess --> 分账成功其他子任务["分账成功其他子任务"]
        分账成功其他子任务 -- "15a分账成功" --> Database
        分账成功其他子任务 -- "16a扣减可用余额" --> Database
        分账成功其他子任务 -- "17a更新提现状态为COMPLETED" --> Database
    end

    subgraph FailurePath
        WalletServiceProcess --> 分账失败其他子任务["分账失败其他子任务"]
        分账失败其他子任务 -- "15b分账失败" --> Database
        分账失败其他子任务 -- "16b可选回滚预冻结金额" --> Database
    end

    WalletServiceProcess -- "18返回处理结果" --> WalletAdminControllerProcess
    WalletAdminControllerProcess -- "19响应管理后台" --> AdminPortal

    subgraph Database
        DB_WR[(withdrawal_requests)]
        DB_AW[(agent_wallets)]
        DB_WT[(wallet_transactions)]
    end
```
**数据对象说明**:

- **WithdrawalRequestCreateReqVO**: 代理商提交的提现申请参数
- **WithdrawalProcessDTO**: 管理后台提交的提现审核结果
- **WithdrawalRequestDO**: 提现申请领域对象
- **WalletTransactionDO**: 钱包流水领域对象
- **AgentWalletDO**: 代理商钱包领域对象

### 4.2. 交互时序图

#### 4.2.1. 资金自动解冻时序图

```mermaid
sequenceDiagram
    participant Scheduler as 定时任务
    participant WalletService as WalletService
    participant WalletTransactionRepo as WalletTransactionRepository
    participant AgentWalletRepo as AgentWalletRepository

    Scheduler->>+WalletService: triggerAutoUnfreezeFunds()
    WalletService->>+WalletTransactionRepo: findExpiredFrozenTransactions(now)
    Note right of WalletTransactionRepo: 查询 available_at <= now 且 status = "FROZEN" 的 WalletTransactionDO 列表
    WalletTransactionRepo-->>-WalletService: List<WalletTransactionDO> frozenTransactions
    loop 对每个 frozenTransaction
        WalletService->>WalletService: processUnfreezing(frozenTransaction)
        WalletService->>+WalletTransactionRepo: updateStatus(transactionId, "AVAILABLE")
        WalletTransactionRepo-->>-WalletService: void
        WalletService->>+AgentWalletRepo: unfreezeBalance(agentId, amount)
        Note right of AgentWalletRepo: AgentWalletDO.frozen_balance -= amount <br/> AgentWalletDO.available_balance += amount
        AgentWalletRepo-->>-WalletService: void
        WalletService->>WalletService: createUnfreezeLogIfNeeded()
    end
    WalletService-->>Scheduler: Unfreezing process completed
```
- **数据对象说明**:
   - `WalletTransactionDO`: 领域对象，存储钱包流水信息。字段已更正为驼峰命名。
   - `AgentWalletDO`: 领域对象，存储代理商钱包余额信息。字段已更正为驼峰命名。
#### 4.2.2. 坏账收回并补发分润时序图

```mermaid
sequenceDiagram
    participant OrderModule as 订单模块
    participant WalletApi as WalletApi (钱包模块接口)
    participant WalletService as WalletService
    participant WalletTransactionRepo as WalletTransactionRepository
    participant AgentWalletRepo as AgentWalletRepository

    OrderModule->>+WalletApi: processBadDebtRecoveryCommission(BadDebtRecoveryDTO)
    Note left of WalletApi: BadDebtRecoveryDTO 包含: <br/> agentId (收分润者), orderId, commissionAmount

    WalletApi->>+WalletService: recordBadDebtRecoveryCommission(dto.agentId, dto.orderId, dto.commissionAmount)
    WalletService->>WalletService: createTransactionForRecovery(agentId, orderId, amount)
    Note right of WalletService: 创建一条新的 WalletTransactionDO: <br/> type = "COMMISSION_RECOVERY_BAD_DEBT" / "COMMISSION_EARNED" <br/> status = "AVAILABLE" (直接可用) <br/> amount = commissionAmount (正)
    WalletService->>+WalletTransactionRepo: save(newTransactionDO)
    WalletTransactionRepo-->>-WalletService: savedTransactionDO (包含新流水ID)
    WalletService->>+AgentWalletRepo: creditAvailableBalance(agentId, commissionAmount)
    Note right of AgentWalletRepo: AgentWalletDO.available_balance += commissionAmount
    AgentWalletRepo-->>-WalletService: void
    WalletService-->>-WalletApi: success(savedTransactionDO.transactionId)
    WalletApi-->>OrderModule: CommonResult<String> (流水ID)
```
- **数据对象说明**:
   - `BadDebtRecoveryDTO`: DTO, 订单模块调用钱包模块时传递。
   - `WalletTransactionDO`: 领域对象。字段已更正为驼峰命名。
   - `AgentWalletDO`: 领域对象。字段已更正为驼峰命名。
## 5\. 数据库设计概要 (Database Design Overview)

### 5.1. 主要数据表

1. `agent_wallets`** (代理商钱包表)**

   - `id` BIGINT PK AI COMMENT '主键ID'
   - `agent_id` BIGINT NOT NULL COMMENT '代理商用户ID'
   - `available_balance` INT NOT NULL DEFAULT 0 COMMENT '可用余额 (单位: 分)'
   - `frozen_balance` INT NOT NULL DEFAULT 0 COMMENT '冻结余额 (单位: 分)'
   - `total_profit_earned` INT NOT NULL DEFAULT 0 COMMENT '累计利润总额 (单位: 分)'
   - `total_commission_earned` INT NOT NULL DEFAULT 0 COMMENT '累计分润总额 (单位: 分)'
   - `total_withdrawn_amount` INT NOT NULL DEFAULT 0 COMMENT '累计已提现总额 (单位: 分)'
   - `status` TINYINT NOT NULL DEFAULT 1 COMMENT '钱包状态 (具体取值见字典说明)'
   - `last_withdrawal_deadline` DATE COMMENT '最晚提现日 (针对最早一笔可用资金)'
   - `creator` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建者ID'
   - `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
   - `updater` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '更新者ID'
   - `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
   - `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标志'
   - `tenant_id` BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号'
   - `version` INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号'
   - UNIQUE INDEX `uk_agent_id` (`agent_id`)
2. `wallet_transactions`** (钱包流水表)**

   - `id` BIGINT PK AI COMMENT '主键ID'
   - `wallet_id` BIGINT NOT NULL COMMENT '钱包ID'
   - `agent_id` BIGINT NOT NULL COMMENT '代理商用户ID'
   - `pay_order_sn` VARCHAR(64) NOT NULL COMMENT '统一登陆平台的订单支付编号'
   - `transaction_id` VARCHAR(64) NOT NULL COMMENT '平台唯一交易流水号'
   - `transaction_type` VARCHAR(50) NOT NULL COMMENT '交易类型 (具体取值见字典说明)'
   - `amount` INT NOT NULL COMMENT '交易金额 (正为收入,负为支出) (单位: 分)'
   - `balance_before_transaction` INT COMMENT '交易前账户总余额 (可用+冻结) (单位: 分)'
   - `balance_after_transaction` INT NOT NULL COMMENT '交易后账户总余额 (可用+冻结) (单位: 分)'
   - `available_balance_after_transaction` INT COMMENT '交易后可用余额 (单位: 分)'
   - `frozen_balance_after_transaction` INT COMMENT '交易后冻结余额 (单位: 分)'
   - `related_order_id` VARCHAR(64) COMMENT '关联订单号'
   - `related_withdrawal_id` BIGINT COMMENT '关联提现申请ID'
   - `related_event_id` VARCHAR(64) COMMENT '关联风险事件ID或其他业务ID'
   - `status` VARCHAR(30) NOT NULL COMMENT '流水状态 (具体取值见字典说明)'
   - `description` VARCHAR(255) COMMENT '交易描述'
   - `available_at` DATETIME COMMENT '资金预计可用时间 (解冻时间)'
   - `remark` VARCHAR(512) COMMENT '备注信息 (如操作人,调账原因)'
   - `creator` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建者ID'
   - `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
   - `updater` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '更新者ID'
   - `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
   - `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标志'
   - `tenant_id` BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号'
   - `version` INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号'
   - UNIQUE INDEX `uk_transaction_id` (`transaction_id`)
   - INDEX `idx_agent_id_type_status_created` (`agent_id`, `transaction_type`, `status`, `create_time`)
   - INDEX `idx_available_at_status` (`available_at`, `status`)
3. `withdrawal_requests`** (提现申请表)**

   - `id` BIGINT PK AI COMMENT '主键ID'
   - `wallet_id` BIGINT NOT NULL COMMENT '钱包ID'
   - `agent_id` BIGINT NOT NULL COMMENT '代理商用户ID'
   - `request_no` VARCHAR(64) NOT NULL COMMENT '提现申请唯一编号'
   - `amount` INT NOT NULL COMMENT '申请提现金额 (单位: 分)'
   - `actual_amount` INT COMMENT '实际到账金额 (扣除手续费后) (单位: 分)'
   - `fee_amount` INT DEFAULT 0 COMMENT '手续费金额 (单位: 分)'
   - `status` VARCHAR(30) NOT NULL COMMENT '提现状态 (具体取值见字典说明)'
   - `channel` VARCHAR(30) NOT NULL DEFAULT 'WECHAT_PROFIT_SHARING' COMMENT '提现渠道 (WECHAT_PROFIT_SHARING)'
   - `channel_account_info` VARCHAR(255) COMMENT '渠道账户信息 (如微信OpenID, 商户号)'
   - `channel_transaction_id` VARCHAR(128) COMMENT '渠道交易号 (微信分账订单号/批次号)'
   - `failure_reason` VARCHAR(255) COMMENT '失败原因'
   - `audit_remark` VARCHAR(255) COMMENT '审核备注'
   - `auditor_id` BIGINT COMMENT '审核员ID'
   - `requested_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间'
   - `audited_at` DATETIME COMMENT '审核时间'
   - `processing_started_at` DATETIME COMMENT '开始处理时间（调用三方接口）'
   - `completed_at` DATETIME COMMENT '完成时间/失败时间'
   - `is_auto_triggered` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否自动提现触发'
   - `creator` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建者ID'
   - `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
   - `updater` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '更新者ID'
   - `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
   - `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标志'
   - `tenant_id` BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号'
   - `version` INT NOT NULL DEFAULT 0 COMMENT '乐观锁版本号'
   - UNIQUE INDEX `uk_request_no` (`request_no`)
   - INDEX `idx_agent_id_status_requested_at` (`agent_id`, `status`, `requested_at`)
4. `idempotency_records`** (幂等性记录表)**

   - `id` BIGINT PK AI COMMENT '主键ID'
   - `idempotency_key` VARCHAR(64) NOT NULL COMMENT '幂等键'
   - `resource_type` VARCHAR(30) NOT NULL COMMENT '资源类型 (如 transaction, withdrawal)'
   - `resource_id` VARCHAR(64) COMMENT '资源ID (如交易流水号, 提现申请编号)'
   - `status` VARCHAR(20) NOT NULL COMMENT '处理状态 (SUCCESS, FAILED)'
   - `expiry_time` DATETIME NOT NULL COMMENT '幂等键过期时间'
   - `response_data` TEXT COMMENT '上次请求的响应数据 (JSON格式, 用于重复请求时返回)'
   - `error_code` INT COMMENT '错误码 (如果处理失败)'
   - `error_message` VARCHAR(255) COMMENT '错误信息 (如果处理失败)'
   - `creator` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '创建者ID'
   - `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
   - `updater` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '更新者ID'
   - `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
   - `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标志'
   - `tenant_id` BIGINT NOT NULL DEFAULT 0 COMMENT '租户编号'
   - UNIQUE INDEX `uk_idempotency_key` (`idempotency_key`)
   - INDEX `idx_expiry_time` (`expiry_time`)
### 5.2. 表关系

- `agent_wallets` (1) <-> (N) `wallet_transactions` (通过 `wallet_id` 和 `agent_id`)
- `agent_wallets` (1) <-> (N) `withdrawal_requests` (通过 `wallet_id` 和 `agent_id`)
- `wallet_transactions` (N) <-> (1) `withdrawal_requests` (通过 `related_withdrawal_id`, 可选关联，比如提现成功/失败流水会关联)
- `idempotency_records` (独立表) <-> (可选关联) `wallet_transactions` 和 `withdrawal_requests` (通过 `resource_id` 可选关联到对应资源)

### 5.3 数据库ER图

```mermaid
erDiagram
    agent_wallets ||--o{ wallet_transactions : "包含"
    agent_wallets ||--o{ withdrawal_requests : "发起"
    wallet_transactions }o--o| withdrawal_requests : "关联"
    idempotency_records |o--o{ wallet_transactions : "可选关联"
    idempotency_records |o--o{ withdrawal_requests : "可选关联"

    agent_wallets {
        bigint id "主键ID" 
        bigint agent_id "代理商ID"
        int available_balance "可用余额 (单位: 分)"
        int frozen_balance "冻结余额 (单位: 分)"
        int total_profit_earned "累计利润总额 (单位: 分)"
        int total_commission_earned "累计分润总额 (单位: 分)"
        int total_withdrawn_amount "累计已提现总额 (单位: 分)"
        tinyint status "钱包状态"
        date last_withdrawal_deadline "最晚提现日"
        varchar creator "创建者ID"
        datetime create_time "创建时间"
        varchar updater "更新者ID"
        datetime update_time "更新时间"
        bit deleted "逻辑删除标志"
        bigint tenant_id "租户编号"
        int version "乐观锁版本号"
    }

    wallet_transactions {
        bigint id "主键ID"
        bigint wallet_id "钱包ID"
        bigint agent_id "代理商ID"
        varchar transaction_id "交易流水号"
        varchar transaction_type "交易类型"
        int amount "交易金额 (单位: 分)"
        int balance_before_transaction "交易前余额 (单位: 分)"
        int balance_after_transaction "交易后余额 (单位: 分)"
        int available_balance_after_transaction "交易后可用余额 (单位: 分)"
        int frozen_balance_after_transaction "交易后冻结余额 (单位: 分)"
        varchar related_order_id "关联订单号"
        bigint related_withdrawal_id "关联提现ID"
        varchar related_event_id "关联事件ID"
        varchar status "流水状态"
        varchar description "交易描述"
        datetime available_at "资金可用时间"
        varchar remark "备注信息"
        varchar creator "创建者ID"
        datetime create_time "创建时间"
        varchar updater "更新者ID"
        datetime update_time "更新时间"
        bit deleted "逻辑删除标志"
        bigint tenant_id "租户编号"
        int version "乐观锁版本号"
    }

    withdrawal_requests {
        bigint id "主键ID"
        bigint wallet_id "钱包ID"
        bigint agent_id "代理商ID"
        varchar request_no "提现申请编号"
        int amount "申请提现金额 (单位: 分)"
        int actual_amount "实际到账金额 (单位: 分)"
        int fee_amount "手续费金额 (单位: 分)"
        varchar status "提现状态"
        varchar channel "提现渠道"
        varchar channel_account_info "渠道账户信息"
        varchar channel_transaction_id "渠道交易号"
        varchar failure_reason "失败原因"
        varchar audit_remark "审核备注"
        bigint auditor_id "审核员ID"
        datetime requested_at "申请时间"
        datetime audited_at "审核时间"
        datetime processing_started_at "处理开始时间"
        datetime completed_at "完成时间"
        bit is_auto_triggered "是否自动提现"
        varchar creator "创建者ID"
        datetime create_time "创建时间"
        varchar updater "更新者ID"
        datetime update_time "更新时间"
        bit deleted "逻辑删除标志"
        bigint tenant_id "租户编号"
        int version "乐观锁版本号"
    }

    idempotency_records {
        bigint id "主键ID"
        varchar idempotency_key "幂等键"
        varchar resource_type "资源类型"
        varchar resource_id "资源ID"
        varchar status "处理状态"
        datetime expiry_time "过期时间"
        text response_data "响应数据"
        int error_code "错误码"
        varchar error_message "错误信息"
        varchar creator "创建者ID"
        datetime create_time "创建时间"
        varchar updater "更新者ID"
        datetime update_time "更新时间"
        bit deleted "逻辑删除标志"
        bigint tenant_id "租户编号"
    }
```
### 5.4 数据访问策略

- **ORM框架**: 使用 MyBatis Plus 进行数据访问操作，简化CRUD。
- **复杂查询**: 复杂的统计和关联查询通过自定义 Mapper XML 实现。
- **事务管理**: Service 层通过 `@Transactional` 注解管理事务，确保资金操作的原子性。
- **索引优化**: 根据查询频率和条件为关键字段建立索引，如 `agent_id`, `transaction_type`, `status`, `create_time`, `available_at` 等。
- **数据一致性**: 强一致性要求。资金操作必须保证准确无误。
- **缓存策略**:
   - 代理商钱包余额信息 (`AgentWalletDO`) 可考虑使用缓存 (如 Redis)，但需注意缓存更新与失效策略，保证与数据库一致性。
   - 流水查询等非核心实时数据可适当放宽缓存要求或不使用缓存。
- **分库分表**: 初期阶段不考虑，若未来数据量巨大，可按 `agent_id` 进行分库分表。

---

**错误码引用说明**:

- 项目级/共享错误码: 定义在 `com.yunyi.express2b.framework.common.exception.GlobalErrorCodeConstants`
   - 示例: `GlobalErrorCodeConstants.UNAUTHORIZED`, `GlobalErrorCodeConstants.BAD_REQUEST`, `GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR`
- 模块特有错误码: 定义在 `com.yunyi.express2b.module.wallet.api.enums.WalletErrorCodeConstants`
   - 示例: `WalletErrorCodeConstants.WALLET_NOT_FOUND`, `WalletErrorCodeConstants.INSUFFICIENT_AVAILABLE_BALANCE`, `WalletErrorCodeConstants.WITHDRAWAL_REQUEST_NOT_FOUND` 等。
**配置项**:

- `wallet.default.freeze-days`: 默认资金冻结天数 (例如: 3)
- `wallet.withdrawal.min-amount`: 最低提现金额 (例如: 1.00)
- `wallet.withdrawal.max-days-before-auto`: 最晚提现日 (多少天内未提现则自动提现, 例如: 10)
- `wallet.withdrawal.auto-enabled`: 是否开启自动提现功能 (true/false)
- `wallet.withdrawal.fee-rate`: 提现手续费率 (例如: 0.001 表示千分之一, 可选)
- `wallet.withdrawal.fee-fixed`: 固定提现手续费 (例如: 0.5 元, 可选)

## 金额处理说明

为确保金额计算的精确性和统一性，钱包模块采用以下金额处理策略：

1. **存储规则**:

   - 所有金额在数据库中存储为 INT 类型，单位为分
   - 在 Java 代码中使用 Integer 类型表示金额
   - 例如：¥100.50 在数据库和Java中均存储为 10050
2. **金额转换**:

   - 前端展示时，需将分转换为元，并保留两位小数
   - 后端接收前端输入时，需将元乘以100转为分进行存储
   - 服务间通信时，统一使用分作为单位，避免小数精度问题
3. **计算规则**:

   - 金额相关计算全部基于整数进行，避免浮点数计算带来的精度问题
   - 如需进行除法运算，须先完成乘法计算后再除以相应倍数
4. **代码示例**:

```java
// 前端接收到的金额（元）转换为分
Integer amountInCents = (int)(amountInYuan * 100);

// 数据库中的金额（分）转换为元，用于API响应
BigDecimal amountInYuan = new BigDecimal(amountInCents).divide(new BigDecimal(100));
```
 
5. **展示格式**:

   - Web界面展示时，应格式化为带千分位的金额，例如：¥1,234.56
   - API接口返回时，保持Integer类型（单位：分），由前端负责格式化