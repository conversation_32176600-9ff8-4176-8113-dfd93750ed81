package com.yunyi.express2b.module.pricing.service.selectedpricingtemplate;

import java.util.*;
import jakarta.validation.*;
import com.yunyi.express2b.module.pricing.controller.admin.selectedpricingtemplate.vo.*;
import com.yunyi.express2b.module.pricing.dal.dataobject.selectedpricingtemplate.SelectedPricingTemplateDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;

/**
 * 代理商为下级选择的价格模板记录 Service 接口
 *
 * <AUTHOR>
 */
public interface SelectedPricingTemplateService {

    /**
     * 创建代理商为下级选择的价格模板记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSelectedPricingTemplate(@Valid SelectedPricingTemplateSaveReqVO createReqVO);

    /**
     * 更新代理商为下级选择的价格模板记录
     *
     * @param updateReqVO 更新信息
     */
    void updateSelectedPricingTemplate(@Valid SelectedPricingTemplateSaveReqVO updateReqVO);

    /**
     * 删除代理商为下级选择的价格模板记录
     *
     * @param id 编号
     */
    void deleteSelectedPricingTemplate(Long id);

    /**
     * 获得代理商为下级选择的价格模板记录
     *
     * @param id 编号
     * @return 代理商为下级选择的价格模板记录
     */
    SelectedPricingTemplateDO getSelectedPricingTemplate(Long id);

    /**
     * 获得代理商为下级选择的价格模板记录分页
     *
     * @param pageReqVO 分页查询
     * @return 代理商为下级选择的价格模板记录分页
     */
    PageResult<SelectedPricingTemplateDO> getSelectedPricingTemplatePage(SelectedPricingTemplatePageReqVO pageReqVO);

}