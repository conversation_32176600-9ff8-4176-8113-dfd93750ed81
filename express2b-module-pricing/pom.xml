<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.yunyi.express2b</groupId>
        <artifactId>express2b</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <modules>
        <module>express2b-module-pricing-api</module>
        <module>express2b-module-pricing-biz</module>
    </modules>
    <artifactId>express2b-module-pricing</artifactId>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>
    <description>
        定价管理模块，负责平台各渠道（如不同快递公司）、各区域、各客户类型的零售价模板、成本价模板、以及代理商的个性化定价策略的管理和应用。
    </description>

</project> 