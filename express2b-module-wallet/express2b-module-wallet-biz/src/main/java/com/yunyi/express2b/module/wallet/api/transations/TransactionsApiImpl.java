package com.yunyi.express2b.module.wallet.api.transations;

import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.wallet.api.TransationsApi.TransactionsApi;
import com.yunyi.express2b.module.wallet.api.TransationsApi.dto.TransactionsDetailsDTO;
import com.yunyi.express2b.module.wallet.dal.dataobject.transactions.TransactionsDO;
import com.yunyi.express2b.module.wallet.service.transactions.TransactionsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.AGENT_NOT_EXISTS;
@Slf4j
@Service
public class TransactionsApiImpl implements TransactionsApi {
    @Resource
    private TransactionsService transactionsService;
//    根据代理商id获取钱包流水表
    @Override
    public TransactionsDetailsDTO getTransactionsDetailsByAgentId(Long agentId) {
        // 1. 参数校验
        if (agentId == null) {
            throw exception(AGENT_NOT_EXISTS);
        }
//        查询钱包流水信息
       TransactionsDO transactionsDOs= transactionsService.getTransactionsDetailsByAgentId(agentId);
        // 3. 转换为DTO
        TransactionsDetailsDTO transactionsDetailsDTO= BeanUtils.toBean(transactionsDOs, TransactionsDetailsDTO.class);
        // 4. 返回结果
        return transactionsDetailsDTO;
    }
}
