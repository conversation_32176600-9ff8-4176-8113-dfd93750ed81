package com.yunyi.express2b.module.commission.api;


import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;

import com.yunyi.express2b.module.commission.api.dto.CommissionDetailDTO;
import com.yunyi.express2b.module.commission.api.vo.DetailSaveReqVO;
import com.yunyi.express2b.module.commission.controller.admin.detail.vo.DetailPageReqVO;
import com.yunyi.express2b.module.commission.convert.CommissionMapStructMapper;
import com.yunyi.express2b.module.commission.dal.dataobject.detail.DetailDO;
import com.yunyi.express2b.module.commission.dal.mysql.detail.DetailMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;


/**
 *  Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CommissionServiceImpl implements CommissionApi {


    @Resource
    private DetailMapper detailMapper;

    /**
     * 创建分润明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    @Override
    public Long createDetail(DetailSaveReqVO createReqVO) {
        // 插入
        DetailDO detail = BeanUtils.toBean(createReqVO, DetailDO.class);
        detailMapper.insert(detail);
        // 返回
        return detail.getId();
    }



    @Override
    public PageResult<CommissionDetailDTO> queryShareData(DetailSaveReqVO detailSaveReqVO) {
        PageResult<DetailDO> detailDOPageResult = detailMapper.selectPage(BeanUtils.toBean(detailSaveReqVO, DetailPageReqVO.class));
        List<CommissionDetailDTO> commissionDetailDTOList=null;
        if (detailDOPageResult != null) {
            commissionDetailDTOList = CommissionMapStructMapper.INSTANCE.convertDTOList(detailDOPageResult.getList());
        }
        return new PageResult<CommissionDetailDTO>().setList(commissionDetailDTOList);
    }


}