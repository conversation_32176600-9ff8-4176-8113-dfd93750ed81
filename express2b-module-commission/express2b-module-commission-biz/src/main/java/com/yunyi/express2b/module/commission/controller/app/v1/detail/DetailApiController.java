package com.yunyi.express2b.module.commission.controller.app.v1.detail;

import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.commission.controller.admin.detail.vo.DetailPageReqVO;
import com.yunyi.express2b.module.commission.controller.admin.detail.vo.DetailSaveReqVO;
import com.yunyi.express2b.module.commission.dal.dataobject.detail.DetailDO;
import com.yunyi.express2b.module.commission.service.detail.DetailService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;

import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;


@Tag(name = "小程序 - 分润明细")
@RestController
@RequestMapping("/v1/commission/detail")
@Validated
public class DetailApiController {

    @Resource
    private DetailService detailService;

    /**
     * 我可以查询分润明细，按时间、来源、下级等维度统计
     * @param pageReqVO
     * @return
     */
    @GetMapping("/commission-details")
    @Operation(summary = "我可以查询分润明细，按时间、来源、下级等维度统计")
    public CommonResult<PageResult<DetailPageReqVO>> getcommissiondetails(@Valid DetailPageReqVO pageReqVO) {
        PageResult<DetailDO> pageResult = detailService.getcommissiondetails(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DetailPageReqVO.class));
    }
    @PostMapping ("/commission-insert")
    @Operation(summary = "我可以为后续分润计算保存分润明细，记录来源、金额、关联订单、上下级关系等")
    public  CommonResult<Long> insertcommission(@Valid DetailSaveReqVO detailSaveReqVO){

        return success(detailService.createDetail(detailSaveReqVO));
    }



}