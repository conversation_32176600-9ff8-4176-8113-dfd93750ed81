package com.yunyi.express2b.module.agent.controller.admin.dailyperformancestat.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import jakarta.validation.constraints.*;
/**
 * 代理商日业绩统计新增/修改VO实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 * @since 2025/5/15 17:56
 */
@Schema(description = "管理后台 - 代理商日业绩统计新增/修改 Request VO")
@Data
public class DailyPerformanceStatSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "11286")
    private Long id;

    @Schema(description = "代理商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27505")
    @NotNull(message = "代理商ID不能为空")
    private Long agentId;

    @Schema(description = "统计日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "统计日期不能为空")
    private LocalDate statDate;

    @Schema(description = "当日个人完成订单数", requiredMode = Schema.RequiredMode.REQUIRED, example = "2209")
    @NotNull(message = "当日个人完成订单数不能为空")
    private Integer personalOrderCount;

    @Schema(description = "当日团队完成订单总数", requiredMode = Schema.RequiredMode.REQUIRED, example = "27203")
    @NotNull(message = "当日团队完成订单总数不能为空")
    private Integer teamOrderCount;

    @Schema(description = "当日个人订单总金额（终端销售价，单位：分）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "当日个人订单总金额（终端销售价，单位：分）不能为空")
    private Integer personalOrderAmount;

    @Schema(description = "当日团队订单总金额（单位：分）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "当日团队订单总金额（单位：分）不能为空")
    private Integer teamOrderAmount;

    @Schema(description = "当日个人利润（终端销售价-平台零售价-红包，单位：分）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "当日个人利润（终端销售价-平台零售价-红包，单位：分）不能为空")
    private Integer personalProfitAmount;

    @Schema(description = "当日团队利润（下级订单带来的利润，单位：分）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "当日团队利润（下级订单带来的利润，单位：分）不能为空")
    private Integer teamProfitAmount;

    @Schema(description = "当日获得的分润收入（单位：分）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "当日获得的分润收入（单位：分）不能为空")
    private Integer commissionIncomeAmount;

    @Schema(description = "当日坏账订单数", requiredMode = Schema.RequiredMode.REQUIRED, example = "10292")
    @NotNull(message = "当日坏账订单数不能为空")
    private Integer badDebtCount;

    @Schema(description = "当日坏账总金额（单位：分）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "当日坏账总金额（单位：分）不能为空")
    private Integer badDebtAmount;

    @Schema(description = "当日红包核销金额（单位：分）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "当日红包核销金额（单位：分）不能为空")
    private Integer couponDeductAmount;

    @Schema(description = "当日平台补贴金额（如有，单位：分）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "当日平台补贴金额（如有，单位：分）不能为空")
    private Integer subsidyAmount;

    @Schema(description = "当日提现金额（如有，单位：分）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "当日提现金额（如有，单位：分）不能为空")
    private Integer withdrawalAmount;

    @Schema(description = "当日新增客户数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1507")
    @NotNull(message = "当日新增客户数不能为空")
    private Integer newCustomerCount;

    @Schema(description = "当日活跃客户数", requiredMode = Schema.RequiredMode.REQUIRED, example = "17927")
    @NotNull(message = "当日活跃客户数不能为空")
    private Integer activeCustomerCount;

    @Schema(description = "当日复购客户数", requiredMode = Schema.RequiredMode.REQUIRED, example = "13167")
    @NotNull(message = "当日复购客户数不能为空")
    private Integer repeatCustomerCount;

}