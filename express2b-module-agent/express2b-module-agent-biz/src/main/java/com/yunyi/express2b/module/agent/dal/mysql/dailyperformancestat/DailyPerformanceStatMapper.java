package com.yunyi.express2b.module.agent.dal.mysql.dailyperformancestat;

import java.util.*;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.module.agent.dal.dataobject.dailyperformancestat.DailyPerformanceStatDO;
import org.apache.ibatis.annotations.Mapper;
import com.yunyi.express2b.module.agent.controller.admin.dailyperformancestat.vo.*;

/**
 * 代理商日业绩统计 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DailyPerformanceStatMapper extends BaseMapperX<DailyPerformanceStatDO> {

    default PageResult<DailyPerformanceStatDO> selectPage(DailyPerformanceStatPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DailyPerformanceStatDO>()
                .eqIfPresent(DailyPerformanceStatDO::getAgentId, reqVO.getAgentId())
                .betweenIfPresent(DailyPerformanceStatDO::getStatDate, reqVO.getStatDate())
                .eqIfPresent(DailyPerformanceStatDO::getPersonalOrderCount, reqVO.getPersonalOrderCount())
                .eqIfPresent(DailyPerformanceStatDO::getTeamOrderCount, reqVO.getTeamOrderCount())
                .eqIfPresent(DailyPerformanceStatDO::getPersonalOrderAmount, reqVO.getPersonalOrderAmount())
                .eqIfPresent(DailyPerformanceStatDO::getTeamOrderAmount, reqVO.getTeamOrderAmount())
                .eqIfPresent(DailyPerformanceStatDO::getPersonalProfitAmount, reqVO.getPersonalProfitAmount())
                .eqIfPresent(DailyPerformanceStatDO::getTeamProfitAmount, reqVO.getTeamProfitAmount())
                .eqIfPresent(DailyPerformanceStatDO::getCommissionIncomeAmount, reqVO.getCommissionIncomeAmount())
                .eqIfPresent(DailyPerformanceStatDO::getBadDebtCount, reqVO.getBadDebtCount())
                .eqIfPresent(DailyPerformanceStatDO::getBadDebtAmount, reqVO.getBadDebtAmount())
                .eqIfPresent(DailyPerformanceStatDO::getCouponDeductAmount, reqVO.getCouponDeductAmount())
                .eqIfPresent(DailyPerformanceStatDO::getSubsidyAmount, reqVO.getSubsidyAmount())
                .eqIfPresent(DailyPerformanceStatDO::getWithdrawalAmount, reqVO.getWithdrawalAmount())
                .eqIfPresent(DailyPerformanceStatDO::getNewCustomerCount, reqVO.getNewCustomerCount())
                .eqIfPresent(DailyPerformanceStatDO::getActiveCustomerCount, reqVO.getActiveCustomerCount())
                .eqIfPresent(DailyPerformanceStatDO::getRepeatCustomerCount, reqVO.getRepeatCustomerCount())
                .betweenIfPresent(DailyPerformanceStatDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DailyPerformanceStatDO::getId));
    }


}