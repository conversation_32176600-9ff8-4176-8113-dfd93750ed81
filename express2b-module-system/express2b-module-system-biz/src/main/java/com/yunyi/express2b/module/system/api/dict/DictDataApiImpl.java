package com.yunyi.express2b.module.system.api.dict;

import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.system.api.dict.dto.DictDataRespDTO;
import com.yunyi.express2b.module.system.dal.dataobject.dict.DictDataDO;
import com.yunyi.express2b.module.system.service.dict.DictDataService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 字典数据 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class DictDataApiImpl implements DictDataApi {

    @Resource
    private DictDataService dictDataService;

    /**
     * 校验字典数据是否存在
     * @param dictType 字典类型
     * @param values   字典数据值的数组
     */
    @Override
    public void validateDictDataList(String dictType, Collection<String> values) {
        dictDataService.validateDictDataList(dictType, values);
    }

    /**
     * 获得字典数据
     * @param dictType  字典类型
     * @param value 字典数据值
     * @return
     */
    @Override
    public DictDataRespDTO getDictData(String dictType, String value) {
        DictDataDO dictData = dictDataService.getDictData(dictType, value);
        return BeanUtils.toBean(dictData, DictDataRespDTO.class);
    }

    /**
     * 解析字典数据
     * @param dictType  字典类型
     * @param label 字典数据标签
     * @return
     */
    @Override
    public DictDataRespDTO parseDictData(String dictType, String label) {
        DictDataDO dictData = dictDataService.parseDictData(dictType, label);
        return BeanUtils.toBean(dictData, DictDataRespDTO.class);
    }

    /**
     * 获得字典数据列表
     * @param dictType 字典类型
     * @return
     */
    @Override
    public List<DictDataRespDTO> getDictDataList(String dictType) {
        List<DictDataDO> list = dictDataService.getDictDataListByDictType(dictType);
        return BeanUtils.toBean(list, DictDataRespDTO.class);
    }

}
