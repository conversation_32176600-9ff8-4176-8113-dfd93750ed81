---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: true
---
# Java 测试开发规范 (dsj-inventory)

本规范旨在指导如何在 `dsj-inventory` 项目中编写单元测试和服务集成测试。

## 1. 核心测试设置与基类

-   **测试基类**: 所有涉及数据库交互的单元测试或集成测试都应继承 `[BaseDbUnitTest.java](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/core/BaseDbUnitTest.java)`。
    -   该基类自动激活 `test` Spring Profile，使用 `[application-test.yml](mdc:inventory/src/test/resources/application-test.yml)` 中的配置。
    -   它会在每个测试方法执行后运行 `[clean.sql](mdc:inventory/src/test/resources/clean.sql)` 来清理数据库，保证测试的独立性。
    -   它会在每个测试方法执行前调用 `TestContextHelper.setDefaultUserContext()` 设置默认用户上下文，并在测试方法执行后调用 `TestContextHelper.clearContext()` 清理上下文。

-   **测试上下文**: 使用 `[TestContextHelper.java](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/core/TestContextHelper.java)` 来管理和模拟不同的用户上下文或组织上下文。
    -   `TestContextHelper.setDefaultUserContext()`: 设置默认的药店用户上下文。
    -   `TestContextHelper.setPlatformContext(...)`: 设置平台或其他特定类型的组织上下文。

-   **数据库**:
    -   测试使用 H2 内存数据库，配置见 `[application-test.yml](mdc:inventory/src/test/resources/application-test.yml)`。
    -   数据库表结构由 `[schema.sql](mdc:inventory/src/test/resources/schema.sql)` 定义。
    -   初始测试数据可以放入 `[data.sql](mdc:inventory/src/test/resources/data.sql)`。

## 2. Service 层测试规范

参考示例: `[ShelfServiceImplTest.java](mdc:inventory/src/test/java/com/dsj/inventory/bussiness/inventory/service/impl/ShelfServiceImplTest.java)`

-   **注解**:
    -   在测试类上使用 `@Import({YourServiceImpl.class})` 来将被测试的 Service 实现类加载到 Spring 测试上下文中。
    -   使用 `@ActiveProfiles("test")` (通常已在基类中定义)。

-   **依赖注入**:
    -   使用 `@Autowired` 注入被测试的 Service 实现，例如 `private YourServiceImpl yourService;`。
    -   使用 `@MockBean` 来 Mock 该 Service 所依赖的其他 Service、Mapper 或外部组件。

-   **测试方法结构**:
    -   使用 JUnit 5 注解: `@Test`, `@DisplayName`, `@BeforeEach`。
    -   `@BeforeEach` 方法:
        -   进行通用的测试数据准备。
        -   设置 Mock 对象行为 (使用 `Mockito.when(...).thenReturn(...)`)。
        -   如有必要，调用 `TestContextHelper` 设置特定的上下文。
    -   `@Test` 方法:
        -   遵循 Arrange-Act-Assert (AAA) 模式。
        -   **Arrange**: 准备特定测试场景的输入数据和 Mock 行为。
        -   **Act**: 调用被测试 Service 的方法。
        -   **Assert**: 使用 JUnit 5 的断言方法 (如 `assertEquals`, `assertTrue`, `assertNotNull`, `assertThrows`) 验证结果或行为。
        -   **Verify**: 使用 `Mockito.verify(...)` 验证 Mock 对象的方法是否按预期被调用。

-   **Mocking 实践**:
    -   精确 Mock 依赖项的行为，只 Mock 当前测试逻辑所必需的交互。
    -   对于 Mapper 层的方法调用，通常 Mock `selectCount`, `selectById`, `insert`, `update` 等方法的返回结果。
    -   对于分页查询，可以 Mock `mapper.queryYourPage(any(Page.class), any(YourQueryParam.class))` 并返回一个包含测试数据的 `Page` 对象。

-   **命名约定**:
    -   测试类名: `YourServiceImplTest.java`。
    -   测试方法名: 使用 `@DisplayName` 提供清晰的描述，例如 `@DisplayName("queryMethod - Scenari X - Expected Behavior Y")`。

## 3. 测试数据管理

-   **通用数据**: 可以在 `[data.sql](mdc:inventory/src/test/resources/data.sql)` 中定义所有测试可能用到的基础数据。
-   **特定场景数据**: 在测试方法的 Arrange 部分通过代码创建，或通过 Mock Mapper 返回。
-   **数据清理**: `[clean.sql](mdc:inventory/src/test/resources/clean.sql)` 负责在每个测试后删除相关表的数据。确保 DELETE 语句的正确性以避免数据残留。

## 4. 注意事项

-   **最小化上下文加载**: `[BaseDbUnitTest.Application.class](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/core/BaseDbUnitTest.java)` 中的 `@ComponentScan` 配置了严格的扫描策略，旨在只加载测试必要的组件（主要是Entity和MyBatis Plus核心配置），以加快测试启动速度。避免在测试中加载不必要的 Controller、全局配置等。
-   **事务**: Service 层方法通常有 `@Transactional` 注解。测试默认在事务中运行，并在结束后回滚 (由 `@Sql` 在 `BaseDbUnitTest` 中执行 `clean.sql` 保证数据清理，而非依赖 Spring Test 的默认回滚行为)。
-   **H2兼容性**: `schema.sql` 中定义的表结构应与 MySQL 生产环境兼容，同时适配 H2 语法。`[H2SchemaGenerator.java](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/util/H2SchemaGenerator.java)` 可以辅助生成基于 Entity 的 H2 DDL，但最终的 `schema.sql` 可能需要手动调整。

## 5. 关键文件引用

-   测试基类: `[BaseDbUnitTest.java](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/core/BaseDbUnitTest.java)`
-   测试上下文工具: `[TestContextHelper.java](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/core/TestContextHelper.java)`
-   测试配置文件: `[application-test.yml](mdc:inventory/src/test/resources/application-test.yml)`
-   数据库表结构: `[schema.sql](mdc:inventory/src/test/resources/schema.sql)`
-   初始测试数据: `[data.sql](mdc:inventory/src/test/resources/data.sql)`
-   数据清理脚本: `[clean.sql](mdc:inventory/src/test/resources/clean.sql)`
-   Service测试示例: `[ShelfServiceImplTest.java](mdc:inventory/src/test/java/com/dsj/inventory/bussiness/inventory/service/impl/ShelfServiceImplTest.java)`
-   通用测试入口: `[InventoryApplicationTests.java](mdc:inventory/src/test/java/com/dsj/inventory/InventoryApplicationTests.java)` (可用于测试整体上下文加载)

