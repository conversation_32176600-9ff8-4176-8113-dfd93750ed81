package com.yunyi.express2b.module.express.controller.app.v1.invoice;

import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.UnInvoicedOrderDTO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.UnInvoicedOrderVO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.service.invoice.AppInvoiceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

@Slf4j
@Tag(name = "用户端发票模块")
@RestController
@RequestMapping("/v1/express/invoice")
public class AppInvoiceController {
    @Resource
    private AppInvoiceService appInvoiceService;

    @GetMapping("/uninvoiced-orders")
    @Operation(summary = "获取未开票订单分页列表")
    public CommonResult<PageResult<UnInvoicedOrderVO>> getUnInvoicedOrderPage(@RequestBody(required = false) UnInvoicedOrderDTO unInvoicedOrderDTO) {
        PageResult<OrderDO> pageResult = appInvoiceService.getUnInvoicedOrderPage(unInvoicedOrderDTO);
        return success(BeanUtils.toBean(pageResult, UnInvoicedOrderVO.class));
    }

}
