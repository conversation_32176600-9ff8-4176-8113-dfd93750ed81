package com.yunyi.express2b.module.wallet.controller.admin.wallets.vo;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 代理商Web管理界面展示代理商钱包余额")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WalletBalanceRespVO extends PageParam {


    @Schema(description = "可用余额 (单位: 分)")
    private Integer availableBalance;

    @Schema(description = "冻结余额 (单位: 分)")
    private Integer frozenBalance;

    @Schema(description = "累计利润总额 (单位: 分)")
    private Integer totalProfitEarned;


}