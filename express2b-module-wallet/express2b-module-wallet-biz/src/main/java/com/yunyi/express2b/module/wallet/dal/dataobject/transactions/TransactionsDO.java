package com.yunyi.express2b.module.wallet.dal.dataobject.transactions;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 钱包流水 DO
 *
 * <AUTHOR>
 */
@TableName("wallet_transactions")
@KeySequence("wallet_transactions_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionsDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 钱包ID
     */
    private Long walletId;
    /**
     * 代理商用户ID
     */
    private Long agentId;
    /**
     * 统一登陆平台的订单支付编号
     */
    private String payOrderSn;
    /**
     * 平台唯一交易流水号
     */
    private String transactionId;
    /**
     * 交易类型 (具体取值见字典说明)
     */
    private String transactionType;
    /**
     * 交易金额 (正为收入,负为支出) (单位: 分)
     */
    private Integer amount;
    /**
     * 交易前账户总余额 (可用+冻结) (单位: 分)
     */
    private Integer balanceBeforeTransaction;
    /**
     * 交易后账户总余额 (可用+冻结) (单位: 分)
     */
    private Integer balanceAfterTransaction;
    /**
     * 交易后可用余额 (单位: 分)
     */
    private Integer availableBalanceAfterTransaction;
    /**
     * 交易后冻结余额 (单位: 分)
     */
    private Integer frozenBalanceAfterTransaction;
    /**
     * 关联订单号
     */
    private String relatedOrderId;
    /**
     * 关联提现申请ID
     */
    private Long relatedWithdrawalId;
    /**
     * 关联风险事件ID或其他业务ID
     */
    private String relatedEventId;
    /**
     * 流水状态 (具体取值见字典说明)
     */
    private String status;
    /**
     * 交易描述
     */
    private String description;
    /**
     * 资金预计可用时间 (解冻时间)
     */
    private LocalDateTime availableAt;
    /**
     * 备注信息 (如操作人,调账原因)
     */
    private String remark;


}