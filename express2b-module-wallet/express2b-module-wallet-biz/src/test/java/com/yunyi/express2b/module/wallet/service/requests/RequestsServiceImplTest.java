package com.yunyi.express2b.module.wallet.service.requests;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.module.wallet.controller.admin.requests.vo.RequestsPageReqVO;
import com.yunyi.express2b.module.wallet.controller.admin.requests.vo.RequestsSaveReqVO;
import com.yunyi.express2b.module.wallet.dal.dataobject.requests.RequestsDO;
import com.yunyi.express2b.module.wallet.dal.mysql.requests.RequestsMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import static com.yunyi.express2b.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static com.yunyi.express2b.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertPojoEquals;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertServiceException;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomLongId;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomPojo;
import static com.yunyi.express2b.module.wallet.enums.ErrorCodeConstants.REQUESTS_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link RequestsServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(RequestsServiceImpl.class)
public class RequestsServiceImplTest extends BaseDbUnitTest {

    @Resource
    private RequestsServiceImpl requestsService;

    @Resource
    private RequestsMapper requestsMapper;

    @Test
    public void testCreateRequests_success() {
        // 准备参数
        RequestsSaveReqVO createReqVO = randomPojo(RequestsSaveReqVO.class).setId(null);

        // 调用
        Long requestsId = requestsService.createRequests(createReqVO);
        // 断言
        assertNotNull(requestsId);
        // 校验记录的属性是否正确
        RequestsDO requests = requestsMapper.selectById(requestsId);
        assertPojoEquals(createReqVO, requests, "id");
    }

    @Test
    public void testUpdateRequests_success() {
        // mock 数据
        RequestsDO dbRequests = randomPojo(RequestsDO.class);
        requestsMapper.insert(dbRequests);// @Sql: 先插入出一条存在的数据
        // 准备参数
        RequestsSaveReqVO updateReqVO = randomPojo(RequestsSaveReqVO.class, o -> {
            o.setId(dbRequests.getId()); // 设置更新的 ID
        });

        // 调用
        requestsService.updateRequests(updateReqVO);
        // 校验是否更新正确
        RequestsDO requests = requestsMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, requests);
    }

    @Test
    public void testUpdateRequests_notExists() {
        // 准备参数
        RequestsSaveReqVO updateReqVO = randomPojo(RequestsSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> requestsService.updateRequests(updateReqVO), REQUESTS_NOT_EXISTS);
    }

    @Test
    public void testDeleteRequests_success() {
        // mock 数据
        RequestsDO dbRequests = randomPojo(RequestsDO.class);
        requestsMapper.insert(dbRequests);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbRequests.getId();

        // 调用
        requestsService.deleteRequests(id);
        // 校验数据不存在了
        assertNull(requestsMapper.selectById(id));
    }

    @Test
    public void testDeleteRequests_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> requestsService.deleteRequests(id), REQUESTS_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetRequestsPage() {
        // mock 数据
        RequestsDO dbRequests = randomPojo(RequestsDO.class, o -> { // 等会查询到
            o.setWalletId(null);
            o.setAgentId(null);
            o.setRequestNo(null);
            o.setAmount(null);
            o.setActualAmount(null);
            o.setFeeAmount(null);
            o.setStatus(null);
            o.setChannel(null);
            o.setChannelAccountInfo(null);
            o.setChannelTransactionId(null);
            o.setFailureReason(null);
            o.setAuditRemark(null);
            o.setAuditorId(null);
            o.setRequestedAt(null);
            o.setAuditedAt(null);
            o.setProcessingStartedAt(null);
            o.setCompletedAt(null);
            o.setIsAutoTriggered(null);
            o.setCreateTime(null);
        });
        requestsMapper.insert(dbRequests);
        // 测试 walletId 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setWalletId(null)));
        // 测试 agentId 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setAgentId(null)));
        // 测试 requestNo 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setRequestNo(null)));
        // 测试 amount 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setAmount(null)));
        // 测试 actualAmount 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setActualAmount(null)));
        // 测试 feeAmount 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setFeeAmount(null)));
        // 测试 status 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setStatus(null)));
        // 测试 channel 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setChannel(null)));
        // 测试 channelAccountInfo 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setChannelAccountInfo(null)));
        // 测试 channelTransactionId 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setChannelTransactionId(null)));
        // 测试 failureReason 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setFailureReason(null)));
        // 测试 auditRemark 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setAuditRemark(null)));
        // 测试 auditorId 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setAuditorId(null)));
        // 测试 requestedAt 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setRequestedAt(null)));
        // 测试 auditedAt 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setAuditedAt(null)));
        // 测试 processingStartedAt 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setProcessingStartedAt(null)));
        // 测试 completedAt 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setCompletedAt(null)));
        // 测试 isAutoTriggered 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setIsAutoTriggered(null)));
        // 测试 createTime 不匹配
        requestsMapper.insert(cloneIgnoreId(dbRequests, o -> o.setCreateTime(null)));
        // 准备参数
        RequestsPageReqVO reqVO = new RequestsPageReqVO();
        reqVO.setWalletId(null);
        reqVO.setAgentId(null);
        reqVO.setRequestNo(null);
        reqVO.setAmount(null);
        reqVO.setActualAmount(null);
        reqVO.setFeeAmount(null);
        reqVO.setStatus(null);
        reqVO.setChannel(null);
        reqVO.setChannelAccountInfo(null);
        reqVO.setChannelTransactionId(null);
        reqVO.setFailureReason(null);
        reqVO.setAuditRemark(null);
        reqVO.setAuditorId(null);
        reqVO.setRequestedAt(null);
        reqVO.setAuditedAt(null);
        reqVO.setProcessingStartedAt(null);
        reqVO.setCompletedAt(null);
        reqVO.setIsAutoTriggered(null);
        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

        // 调用
        PageResult<RequestsDO> pageResult = requestsService.getRequestsPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbRequests, pageResult.getList().get(0));
    }

}