package com.yunyi.express2b.module.pricing.dal.dataobject.template;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 定价模板 DO
 *
 * <AUTHOR>
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PricingTemplateDetailDTO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 加价类型 (1-比例加价，2-固定金额加价-默认)
     */
    private Integer markupType;
    /**
     * 首重加价值 (固定金额，单位:分)
     */
    private Integer firstWeightMarkup;
    /**
     * 续重加价值 (固定金额，单位:分)
     */
    private Integer additionalWeightMarkup;
    /**
     * 平台零售首重价 (单位:分)
     */
    private Integer platformFirstPrice;
    /**
     * 平台零售续重价 (单位:分)
     */
    private Integer platformAdditionalPrice;
    /**
     * 状态 (0-禁用，1-启用)
     */
    private Integer status;
    /**
     * 描述
     */
    private String description;

    private String agentId;
    /**
     * 代理商名字
     */
    private String name;
    /**
     * 级别id
     */
    private String levelId;


}