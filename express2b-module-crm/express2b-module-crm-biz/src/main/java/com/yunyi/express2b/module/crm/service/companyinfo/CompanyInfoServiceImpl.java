package com.yunyi.express2b.module.crm.service.companyinfo;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.crm.controller.admin.companyinfo.vo.CompanyInfoPageReqVO;
import com.yunyi.express2b.module.crm.controller.admin.companyinfo.vo.CompanyInfoSaveReqVO;
import com.yunyi.express2b.module.crm.dal.dataobject.companyaudithistory.CompanyAuditHistoryDO;
import com.yunyi.express2b.module.crm.dal.dataobject.companyinfo.CompanyInfoDO;
import com.yunyi.express2b.module.crm.dal.mysql.companyaudithistory.CompanyAuditHistoryMapper;
import com.yunyi.express2b.module.crm.dal.mysql.companyinfo.CompanyInfoMapper;
import com.yunyi.express2b.module.crm.dal.mysql.personalinfo.PersonalInfoMapper;
import com.yunyi.express2b.module.crm.enums.AuditStateEnum;
import com.yunyi.express2b.module.express.api.message.SmsApi;
import com.yunyi.express2b.module.express.api.message.dto.OrderChangeDTO;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import com.yunyi.framework.api.login.api.message.MessageApi;
import com.yunyi.framework.api.login.api.message.vo.MessageRequest;
import com.yunyi.framework.api.login.api.message.vo.MessageResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.crm.enums.ErrorCodeConstants.*;


/**
 * 公司信息审核 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class CompanyInfoServiceImpl implements CompanyInfoService {

    @Resource
    private CompanyInfoMapper companyInfoMapper;
    @Resource
     private CompanyAuditHistoryMapper companyAuditHistoryMapper;
    @Resource
    private PersonalInfoMapper personalInfoMapper;
    @Resource
    private SmsApi smsApi;
    @Resource
    private MessageApi messageApi;

    /**
     * 创建公司信息审核
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Long createCompanyInfo(CompanyInfoSaveReqVO createReqVO) {
        // 插入
        CompanyInfoDO companyInfo = BeanUtils.toBean(createReqVO, CompanyInfoDO.class);
        companyInfoMapper.insert(companyInfo);
        // 返回
        return companyInfo.getId();
    }

    /**
     * 更新公司信息审核
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateCompanyInfo(CompanyInfoSaveReqVO updateReqVO) {
        // 校验存在
        validateCompanyInfoExists(updateReqVO.getId());
        // 更新
        CompanyInfoDO updateObj = BeanUtils.toBean(updateReqVO, CompanyInfoDO.class);
        companyInfoMapper.updateById(updateObj);
    }

    /**
     * 删除公司信息审核
     * @param id 公司信息审核id
     */
    @Override
    public void deleteCompanyInfo(Long id) {
        // 校验存在
        validateCompanyInfoExists(id);
        // 删除
        companyInfoMapper.deleteById(id);
    }

    /**
     * 校验公司信息
     * @param id
     */
    private void validateCompanyInfoExists(Long id) {
        if (companyInfoMapper.selectById(id) == null) {
            throw exception(COMPANY_INFO_NOT_EXISTS);
        }
    }

    /**
     * 获取用户公司信息
     * @param memberId 个人id
     * @return
     */
    @Override
    public CompanyInfoDO getUserCompanyInfo(Long memberId) {
        if (companyInfoMapper.selectById(memberId) == null) {
            throw exception(COMPANY_INFO_NOT_EXISTS);
        }
        return companyInfoMapper.selectByMemberId(memberId);
    }

    /**
     * 获取公司信息审核分页
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<CompanyInfoDO> getCompanyInfoPage(CompanyInfoPageReqVO pageReqVO) {
        return companyInfoMapper.selectPage(pageReqVO);
    }

    /**
     * 评价公司信息审核
     * @param id 公司信息审核id
     * @param auditState 审核状态
     * @return
     */
    @Override
    public Boolean appraiseCompanyInfo(Long id, String auditState) {
        if (AuditStateEnum.AUDIT_APPROVED.getCode().equals(auditState)) {
            // 更新状态为审核通过
            companyInfoMapper.updateAppraiseById(id);
            // 将数据存到express2b_company_audit_history表中
            CompanyInfoDO companyInfoDO = companyInfoMapper.selectById(id);
            if (companyInfoDO == null) {
                throw exception(COMPANY_INFO_NOT_EXISTS);
            }
            CompanyAuditHistoryDO companyAuditHistoryDO = new CompanyAuditHistoryDO();
            companyAuditHistoryDO.setCompanyId(companyInfoDO.getCompanyId());
            companyAuditHistoryDO.setCompanyName(companyInfoDO.getCompanyName());
            companyAuditHistoryDO.setStatus(Integer.valueOf(AuditStateEnum.AUDIT_APPROVED.getCode()));
            companyAuditHistoryMapper.insert(companyAuditHistoryDO);

            //发送消息模板审核成功
            companyInfoDO.setRemarks("审核成功");
            sendGoodsStatusUpdateMessage(companyInfoDO);

        } else if (AuditStateEnum.AUDIT_REJECTED.getCode().equals(auditState)) {
            // 更新状态为审核不通过
            companyInfoMapper.updateAppraiseById(id);
            // 将数据存到express2b_company_audit_history表中
            CompanyInfoDO companyInfoDO = companyInfoMapper.selectById(id);
            if (companyInfoDO == null) {
                throw exception(COMPANY_INFO_NOT_EXISTS);
            }
            CompanyAuditHistoryDO companyAuditHistoryDO = new CompanyAuditHistoryDO();
            companyAuditHistoryDO.setCompanyId(companyInfoDO.getCompanyId());
            companyAuditHistoryDO.setCompanyName(companyInfoDO.getCompanyName());
            companyAuditHistoryDO.setStatus(Integer.valueOf(AuditStateEnum.AUDIT_REJECTED.getCode()));
            companyAuditHistoryMapper.insert(companyAuditHistoryDO);
            // 删除数据
            companyInfoMapper.deleteById(id);

            //发送消息模板审核失败
            companyInfoDO.setRemarks("审核失败");
            sendGoodsStatusUpdateMessage(companyInfoDO);

        } else {
            // 如果auditState不符合任何已知状态，抛出异常或记录日志
            throw exception(COMPANY_AUDIT_FAIL);
        }
        return true;
    }


    /**
     * 发送公司信息的审核状态
     */
    private void sendGoodsStatusUpdateMessage(CompanyInfoDO companyInfoDO)  {
        // 获取当前日期和时间
        Date currentDate = new Date();
        // 定义日期时间格式化模式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 格式化当前日期和时间
        String formattedDateTime = sdf.format(currentDate);

        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        String yunyiorderSn = generateOrderNumber("yunyi", loginUserId + "", "100");
        // 若要获取当前时间的时间戳（秒），可直接使用以下代码
        long timestampSeconds = Instant.now().getEpochSecond();
        // 创建一个 Map 来存储数据
        Map<String, String> data = new HashMap<>();
        data.put("time",formattedDateTime);
        data.put("status",companyInfoDO.getRemarks());
        data.put("tip","公司信息的审核状态发送");
        data.put("mobile",companyInfoDO.getContactPhone());
        // 使用 Jackson 转换为 JSON 字符串
        ObjectMapper objectMapper = new ObjectMapper();
        String json = null;
        try {
            json = objectMapper.writeValueAsString(data);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        // 查询用户用户订阅记录
        OrderChangeDTO orderChangeDTO = new OrderChangeDTO();
        orderChangeDTO.setMemberId(loginUserId);
        orderChangeDTO.setTempType(71);
        // 查询用户是否订阅订单变更通知
        Integer subscribe = smsApi.sendOrderMessage(orderChangeDTO);
        if (subscribe > 0){
            // 调用消息发送模板 发送货物状态更新消息
            // 组装请求参数
            MessageRequest request = MessageRequest.builder()
                    .applicationId(7)
                    .clientId(16)
                    .tempType(1)
                    .sendTime(timestampSeconds)
                    .orderSn(yunyiorderSn) //消息订单编号
                    .user(loginUserId)
                    .data(json)
                    .waStatus(1)
                    .subscribe(1)
                    .build();
            CommonResult<MessageResponse> sendMessage = messageApi.sendMessage(request);
            if (sendMessage.getCode() != 00000 || !sendMessage.isSuccess()) {
                log.error("发送货物状态更新消息失败，结果: {}", request);
                throw exception(ErrorCodeConstants.SENDING_ORDER_PAYMENT, "发送货物状态更新提醒失败");
            }
            log.info("发送货物状态更新消息成功，结果: {}", sendMessage);
        }
    }


    /**
     * 生成订单编号
     * @param businessType
     * @param userId
     * @param brandId
     * @return
     */
    public static String generateOrderNumber(String businessType, String userId, String brandId) {
        // 1. 业务类型前缀（2位）
        String businessPrefix = businessType.toUpperCase();
        // 2. 时间戳（6位） - 当前日期格式为YYMMDD
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyMMdd");
        String timestamp = dateFormat.format(new Date());
        // 3. 随机数（6位）
        Random random = new Random();
        String randomPart = String.format("%06d", random.nextInt(1000000));
        // 4. 用户标志（2位） - 用户ID的最后2位
        String userMarker = userId.length() >= 2 ? userId.substring(userId.length() - 2) : userId;
        // 5. 商户标志（2位） - 商户标志的最后2位
        String merchantMarker = brandId.length() >= 2 ? brandId.substring(brandId.length() - 2) : brandId;
        // 拼接前5部分
        StringBuilder orderNumberWithoutCheck = new StringBuilder();
        orderNumberWithoutCheck.append(businessPrefix)
                .append(timestamp)
                .append(randomPart)
                .append(userMarker)
                .append(merchantMarker);
        // 最终订单号(不含检验码)
        return orderNumberWithoutCheck.toString();
    }


    /**
     * 获取公司信息
     * @param id 公司的唯一标识符
     * @return
     */
    @Override
    public CompanyInfoDO getCompanyInfo(Long id) {
        return companyInfoMapper.selectById(id);

    }

    /**
     * 删除公司信息
     * @param id 公司的唯一标识符，用于定位要删除的公司对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCompany(Long id) {
        if (id == null || id <= 0) {
            throw exception(COMPANY_INFO_NOT_EXISTS);
        }
        /*
          1.删除公司信息
          2.删除公司旗下的所有人的信息
         */
        try {
        companyInfoMapper.deleteById(id);
        personalInfoMapper.deleteByCompanyId(id);
        } catch (Exception e) {
            throw exception(COMPANY_DELETE_FAILED);
        }
    }

}