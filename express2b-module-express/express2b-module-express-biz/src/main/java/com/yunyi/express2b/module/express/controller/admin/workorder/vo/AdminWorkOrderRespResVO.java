package com.yunyi.express2b.module.express.controller.admin.workorder.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.yunyi.express2b.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

@Schema(description = "后台管理 - 工单 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@ExcelIgnoreUnannotated
public class AdminWorkOrderRespResVO extends BaseWorkOrderResVO {


    @Schema(description = "工单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("工单编号")
    private String workOrderSn;

    @Schema(description = "SSO用户ID", example = "3349")
    @ExcelProperty("SSO用户ID")
    private Long memberId;

    @Schema(description = "操作员ID", example = "24634")
    @ExcelProperty("操作员ID")
    private Long operatorId;

    @Schema(description = "关联订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("关联订单编号")
    private String orderSn;

    @Schema(description = "类型", example = "1")
    @ExcelProperty("类型")
    private Integer type;

    @Schema(description = "是否需要客服介入")
    @ExcelProperty(value = "是否需要客服介入", converter = DictConvert.class)
    private Integer needCustomerService;

    @Schema(description = "问题描述")
    @ExcelProperty("问题描述")
    private String passengerQuestion;

    @Schema(description = "工单状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "工单状态", converter = DictConvert.class)
    private Integer workOrderStatus;

    @Schema(description = "客服工号")
    @ExcelProperty("客服工号")
    private String customerServiceNo;

    @Schema(description = "客服受理时间")
    @ExcelProperty("客服受理时间")
    private LocalDateTime customerServiceTime;

    @Schema(description = "客服完成时间")
    @ExcelProperty("客服完成时间")
    private LocalDateTime customerServiceFinishTime;

    @Schema(description = "客服回复")
    @ExcelProperty("客服回复")
    private String customerServiceReply;

    @Schema(description = "工单备注")
    @ExcelProperty("工单备注")
    private String workNotes;

    @Schema(description = "紧急程度", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "紧急程度", converter = DictConvert.class)
    private Integer emergencyLevel;

    @Schema(description = "一级工单类型编码", example = "2")
    @ExcelProperty("一级工单类型编码")
    private String firstWorkType;

    @Schema(description = "二级工单类型编码", example = "2")
    @ExcelProperty("二级工单类型编码")
    private String secondWorkType;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "三方平台Id", example = "1")
    private Long tripleId;

}