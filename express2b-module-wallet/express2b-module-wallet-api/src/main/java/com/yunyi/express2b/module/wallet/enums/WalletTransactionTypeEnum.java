package com.yunyi.express2b.module.wallet.enums;

import com.yunyi.express2b.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/24 11:35
 */
@Getter
@AllArgsConstructor
public enum WalletTransactionTypeEnum implements ArrayValuable<String> {
// 交易类型
    PROFIT_EARNED("PROFIT_EARNED", "溢价利润收入"),
    COMMISSION_EARNED("COMMISSION_EARNED", "分润收入"),
    BAD_DEBT_DEDUCTION("BAD_DEBT_DEDUCTION", "坏账扣款"),
    BAD_DEBT_REFUND("BAD_DEBT_REFUND", "坏账返还扣款"),
    COMMISSION_RECOVERY("COMMISSION_RECOVERY", "坏账收回补发分润"),
    WITHDRAWAL_REQUESTED("WITHDRAWAL_REQUESTED", "提现申请"),
    WITHDRAWAL_COMPLETED("WITHDRAWAL_COMPLETED", "提现成功"),
    WITHDRAWAL_FAILED("WITHDRAWAL_FAILED", "提现失败"),
    ADJUSTMENT_IN("ADJUSTMENT_IN", "平台调账(增加)"),
    ADJUSTMENT_OUT("ADJUSTMENT_OUT", "平台调账(减少)"),
    RISK_CONTROL_FREEZE("RISK_CONTROL_FREEZE", "风险控制冻结"),
    RISK_CONTROL_UNFREEZE("RISK_CONTROL_UNFREEZE", "风险控制解冻"),
    WALLET_INIT("WALLET_INIT", "钱包初始化");
    private final String status;
    private final String name;
    /**
     * 根据 status 获取对应的中文名称
     * @param status 枚举的英文标识
     * @return 对应的中文名称，如果没有找到则返回 null 或默认值
     */
    public static String getNameByStatus(String status) {
        for (WalletTransactionTypeEnum type : WalletTransactionTypeEnum.values()) {
            if (type.getStatus().equals(status)) {
                return type.getName();
            }
        }
        return null; // 或者返回 "未知状态"
    }

    public static final String[] ARRAYS = Arrays.stream(values()).map(WalletTransactionTypeEnum::getStatus).toArray(String[]::new);

    @Override
    public String[] array() {
        return ARRAYS;
    }
}
