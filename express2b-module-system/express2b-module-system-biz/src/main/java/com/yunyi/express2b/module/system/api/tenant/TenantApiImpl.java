package com.yunyi.express2b.module.system.api.tenant;

import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.system.api.tenant.dto.TenantPackageRespDTO;
import com.yunyi.express2b.module.system.api.tenant.dto.TenantReqDTO;
import com.yunyi.express2b.module.system.api.tenant.dto.TenantSaveReqDTO;
import com.yunyi.express2b.module.system.controller.admin.tenant.vo.tenant.TenantSaveReqVO;
import com.yunyi.express2b.module.system.dal.dataobject.tenant.TenantDO;
import com.yunyi.express2b.module.system.dal.dataobject.tenant.TenantPackageDO;
import com.yunyi.express2b.module.system.service.tenant.TenantPackageServiceImpl;
import com.yunyi.express2b.module.system.service.tenant.TenantService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 多租户的 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class TenantApiImpl implements TenantApi {

    @Resource
    private TenantService tenantService;
    @Resource
    private TenantPackageServiceImpl tenantPackageService;

    @Override
    public List<Long> getTenantIdList() {
        return tenantService.getTenantIdList();
    }

    @Override
    public void validateTenant(Long id) {
        tenantService.validTenant(id);
    }

    @Override
    public TenantPackageRespDTO getTenantPackage(String name) {
        TenantPackageDO tenantPaceageByName = tenantPackageService.getTenantPaceageByName(name);
        return BeanUtils.toBean(tenantPaceageByName, TenantPackageRespDTO.class);
    }

    @Override
    public TenantReqDTO getTenant(Long id) {
        TenantDO tenant = tenantService.getTenant(id);
        return BeanUtils.toBean(tenant, TenantReqDTO.class);
    }

    @Override
    public Long createTenant(TenantSaveReqDTO createReqVO) {
        return tenantService.createTenant(BeanUtils.toBean(createReqVO, TenantSaveReqVO.class));
    }


}
