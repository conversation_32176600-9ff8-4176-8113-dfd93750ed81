package com.yunyi.express2b.module.crm.controller.admin.personalinfo.vo;

import com.yunyi.express2b.framework.common.validation.InEnum;
import com.yunyi.express2b.module.crm.enums.AuditStateEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 消息推送
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/26 17:02
 */

@Data
public class SendMessageVo {

    @Schema(description = "账户id", example = "3203")
    private Long memberId;

    @Schema(description = "姓名", example = "李四")
    private String memberName;

    @Schema(description = "联系方式")
    private String memberPhone;

    @Schema(description = "状态 0-待审核 1- 审核成功 2- 审核失败")
    @InEnum(value = AuditStateEnum.class)
    private Integer state;

    @Schema(description = "审核备注（失败原因）")
    private String remarks;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "域名")
    private String website;

    @Schema(description = "租户套餐编号")
    private Long PackageId;

    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

    @Schema(description = "账号数量")
    private Integer accountCount;

    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否删除")
    private Integer deleted=0;


}
