package com.yunyi.express2b.module.express.service.orderscheduledtime;

import java.util.*;

import com.yunyi.express2b.module.express.controller.admin.orderscheduledtime.vo.AvailableDate;
import com.yunyi.express2b.module.express.controller.admin.orderscheduledtime.vo.OrderScheduledTimePageReqVO;
import com.yunyi.express2b.module.express.controller.admin.orderscheduledtime.vo.OrderScheduledTimeSaveReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.brandscheduledconfig.BrandScheduledConfigDO;
import jakarta.validation.*;
import com.yunyi.express2b.module.express.dal.dataobject.orderscheduledtime.OrderScheduledTimeDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;

/**
 * 订单预约取件时间 Service 接口
 *
 * <AUTHOR>
 */
public interface OrderScheduledTimeService {

    /**
     * 创建订单预约取件时间
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createOrderScheduledTime(@Valid OrderScheduledTimeSaveReqVO createReqVO);

    /**
     * 更新订单预约取件时间
     *
     * @param updateReqVO 更新信息
     */
    void updateOrderScheduledTime(@Valid OrderScheduledTimeSaveReqVO updateReqVO);

    /**
     * 删除订单预约取件时间
     *
     * @param id 编号
     */
    void deleteOrderScheduledTime(Long id);

    /**
     * 获得订单预约取件时间
     *
     * @param id 编号
     * @return 订单预约取件时间
     */
    OrderScheduledTimeDO getOrderScheduledTime(Long id);

    /**
     * 获得订单预约取件时间分页
     *
     * @param pageReqVO 分页查询
     * @return 订单预约取件时间分页
     */
    PageResult<OrderScheduledTimeDO> getOrderScheduledTimePage(OrderScheduledTimePageReqVO pageReqVO);

    List<AvailableDate> getBrandScheduledConfigTime(String brandKey);
}