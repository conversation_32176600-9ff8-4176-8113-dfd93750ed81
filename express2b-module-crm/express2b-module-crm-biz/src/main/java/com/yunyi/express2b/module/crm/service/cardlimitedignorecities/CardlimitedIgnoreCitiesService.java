package com.yunyi.express2b.module.crm.service.cardlimitedignorecities;
import com.yunyi.express2b.module.crm.dal.dataobject.cardlimited.CardlimitedIgnoreCitiesDO;
import jakarta.validation.*;
import com.yunyi.express2b.module.crm.controller.admin.cardlimitedignorecities.vo.*;
import com.yunyi.express2b.framework.common.pojo.PageResult;


/**
 * 卡种禁用地区 Service 接口
 *
 * <AUTHOR>
 */
public interface CardlimitedIgnoreCitiesService {

    /**
     * 创建卡种禁用地区
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCardlimitedIgnoreCities(@Valid CardlimitedIgnoreCitiesSaveReqVO createReqVO);

    /**
     * 更新卡种禁用地区
     *
     * @param updateReqVO 更新信息
     */
    void updateCardlimitedIgnoreCities(@Valid CardlimitedIgnoreCitiesSaveReqVO updateReqVO);

    /**
     * 删除卡种禁用地区
     *
     * @param id 编号
     */
    void deleteCardlimitedIgnoreCities(Long id);

    /**
     * 获得卡种禁用地区
     *
     * @param id 编号
     * @return 卡种禁用地区
     */
    CardlimitedIgnoreCitiesDO getCardlimitedIgnoreCities(Long id);

    /**
     * 获得卡种禁用地区分页
     *
     * @param pageReqVO 分页查询
     * @return 卡种禁用地区分页
     */
    PageResult<CardlimitedIgnoreCitiesDO> getCardlimitedIgnoreCitiesPage(CardlimitedIgnoreCitiesPageReqVO pageReqVO);

}