package com.yunyi.express2b.module.express.controller.app.v1.dict;

import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.module.system.api.dict.DictDataApi;
import com.yunyi.express2b.module.system.api.dict.dto.DictDataRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/26 16:38
 */
@Tag(name = "用户 App - 字典数据")
@RestController()
@RequestMapping("/v1/express/dict")
@Validated
public class AppDictController {
    @Resource
    DictDataApi dictDataApi;

    /**
     * 验证字典类型中是否包含指定值
     *
     * @param dictType
     * @return
     */
    @GetMapping("validate-dict")
    @PermitAll
    @Operation(summary = "验证字典类型中是否包含指定值")
    public CommonResult<String> validateDictDataList(@RequestParam("type") String dictType, @RequestParam("data") Collection<String> list) {
        dictDataApi.validateDictDataList(dictType, list);
        return CommonResult.success("success");
    }


    /**
     * 获取字典标签下的所有数据列表
     *
     * @param dictType
     * @return
     */
    @GetMapping("get-dict-data-list")
    @PermitAll
    @Operation(summary = "获取字典标签下的所有数据列表")
    public CommonResult<List<DictDataRespDTO>> getDictDataList(String dictType) {
        return CommonResult.success(dictDataApi.getDictDataList(dictType));
    }
}
