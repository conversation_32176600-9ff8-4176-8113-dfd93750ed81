package com.yunyi.express2b.module.express.dal.dataobject.relorderpayment;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 订单与支付订单关联 DO
 *
 * <AUTHOR>
 */
@TableName("express2b_rel_order_payment")
@KeySequence("express2b_rel_order_payment_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RelOrderPaymentDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 订单表ID
     */
    private Long orderId;
    /**
     * 支付订单表ID
     */
    private Long paymentOrderId;
    /**
     * 支付订单关联类型
     */
    private Integer type;

}