package com.yunyi.express2b.module.express.enums;

import com.yunyi.express2b.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 字典数据KEY：INVOICE_STATUS_ENUM
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/6/11 11:49
 */
@Getter
@AllArgsConstructor
public enum InvoiceStatusTypeEnum implements ArrayValuable<String> {

    // 发票状态记录
    NOT_INVOICED("NOT_INVOICED", "未开票"),
    INVOICING("INVOICING", "开票中"),
    INVOICED("INVOICED", "已开票"),
    INVOICE_FAILED("INVOICE_FAILED", "开票失败");

    private final String status;

    private final String name;
    /**
     * 根据 status 获取对应的中文名称
     *
     * @param status 枚举的英文标识
     * @return 对应的中文名称，如果没有找到则返回 null 或默认值
     */
    public static String getNameByStatus(String status) {
        for (InvoiceStatusTypeEnum type : InvoiceStatusTypeEnum.values()) {
            if (type.getStatus().equals(status)) {
                return type.getName();
            }
        }
        return null; // 或者返回 "未知状态"
    }

    public static final String[] ARRAYS = Arrays.stream(values()).map(InvoiceStatusTypeEnum::getStatus).toArray(String[]::new);

    @Override
    public String[] array() {
        return ARRAYS;
    }
}
