package com.yunyi.express2b.module.pricing.dal.mysql.route;

import java.util.*;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.module.pricing.dal.dataobject.route.RouteDO;
import org.apache.ibatis.annotations.Mapper;
import com.yunyi.express2b.module.pricing.controller.admin.route.vo.*;

/**
 * 快递全国运价基础 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RouteMapper extends BaseMapperX<RouteDO> {

    default PageResult<RouteDO> selectPage(RoutePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RouteDO>()
                .eqIfPresent(RouteDO::getBrandKey, reqVO.getBrandKey())
                .eqIfPresent(RouteDO::getLineKey, reqVO.getLineKey())
                .eqIfPresent(RouteDO::getSendProvince, reqVO.getSendProvince())
                .eqIfPresent(RouteDO::getReceiveProvince, reqVO.getReceiveProvince())
                .eqIfPresent(RouteDO::getSendProvinceCode, reqVO.getSendProvinceCode())
                .eqIfPresent(RouteDO::getReceiveProvinceCode, reqVO.getReceiveProvinceCode())
                .eqIfPresent(RouteDO::getProductCode, reqVO.getProductCode())
                .eqIfPresent(RouteDO::getFirstWeight, reqVO.getFirstWeight())
                .eqIfPresent(RouteDO::getAdditionalWeight, reqVO.getAdditionalWeight())
                .eqIfPresent(RouteDO::getCostFirstPrice, reqVO.getCostFirstPrice())
                .eqIfPresent(RouteDO::getCostOverPrice, reqVO.getCostOverPrice())
                .eqIfPresent(RouteDO::getOriginalFirstPrice, reqVO.getOriginalFirstPrice())
                .eqIfPresent(RouteDO::getOriginalOverPrice, reqVO.getOriginalOverPrice())
                .betweenIfPresent(RouteDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(RouteDO::getId));
    }

}