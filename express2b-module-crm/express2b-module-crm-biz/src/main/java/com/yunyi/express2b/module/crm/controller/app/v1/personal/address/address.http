### 获取地址簿列表信息
GET {{appApi}}/v1/personal/address/get-address?pageNo=1&pageSize=10
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

### 获取地址簿列表（按地址类型筛选）
GET {{appApi}}/v1/personal/address/get-address?pageNo=1&pageSize=10&addressType=SHIPPER&isDefault=1
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

### 获取地址簿列表（按时间范围筛选）
GET {{appApi}}/v1/personal/address/get-address?pageNo=1&pageSize=10&createTime[]=2023-01-01 00:00:00&createTime[]=2023-12-31 23:59:59
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

### 批量创建地址
POST {{appApi}}/v1/personal/address/create-list
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

[
  {
    "name": "张三",
    "phone": "13800138000",
    "address": "北京市朝阳区某某路某某号",
    "isDefault": 1,
    "provinceId": 2027,
    "cityId": 26429,
    "districtId": 25255,
    "provinceName": "北京市",
    "cityName": "北京市",
    "districtName": "朝阳区"
  },
  {
    "name": "李四",
    "phone": "13900139000",
    "address": "上海市浦东新区某某路某某号",
    "isDefault": 1,
    "provinceId": 2034,
    "cityId": 26466,
    "districtId": 26487,
    "provinceName": "上海市",
    "cityName": "上海市",
    "districtName": "浦东新区"
  }
]

### 修改地址
POST {{appApi}}/v1/personal/address/update-address
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

{
  "id": 9436,
  "name": "王五",
  "phone": "13700137000",
  "address": "广州市天河区某某路某某号",
  "addressType": "SHIPPER",
  "isDefault": 0,
  "provinceId": 2050,
  "cityId": 26540,
  "districtId": 26551,
  "provinceName": "广东省",
  "cityName": "广州市",
  "districtName": "天河区"
}

### 删除单个地址
POST {{appApi}}/v1/personal/address/delete-address?id=9437
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

### 批量删除地址
POST {{appApi}}/v1/personal/address/del-address-by-ids?ids=9417&ids=9418
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

### 设置为默认寄件地址
POST {{appApi}}/v1/personal/address/default
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

{
  "id": 9419,
  "isDefault": 1
}

### 设置为默认收件地址
POST {{appApi}}/v1/personal/address/default
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

{
  "id": 9420,
  "isDefault": 1
}

### 取消默认地址状态
POST {{appApi}}/v1/personal/address/default
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

{
  "id": 9437,
  "isDefault": 0
}

### 模糊查询地址 - 按手机号查询
GET {{appApi}}/v1/personal/address/fuzzy-search?phone=138&pageNo=1&pageSize=10
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

### 模糊查询地址 - 按姓名查询
GET {{appApi}}/v1/personal/address/fuzzy-search?name=张&pageNo=1&pageSize=10
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

### 模糊查询地址 - 同时按手机号和姓名查询
GET {{appApi}}/v1/personal/address/fuzzy-search?phone=138&name=张&pageNo=1&pageSize=10
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

