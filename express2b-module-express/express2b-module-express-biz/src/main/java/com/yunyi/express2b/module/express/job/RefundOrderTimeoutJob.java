package com.yunyi.express2b.module.express.job;

import com.yunyi.express2b.framework.quartz.core.handler.JobHandler;
import com.yunyi.express2b.framework.tenant.core.aop.TenantIgnore;
import com.yunyi.express2b.module.express.dal.dataobject.refundorder.RefundOrderDO;
import com.yunyi.express2b.module.express.dal.mysql.refundorder.RefundOrderMapper;
import com.yunyi.express2b.module.express.enums.RefundStatusTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 超时退款订单扫描job
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/16 下午2:27
 */
@Component
@Slf4j
public class RefundOrderTimeoutJob implements JobHandler {
    @Resource
    RefundOrderMapper refundOrderMapper;

    @Override
    @TenantIgnore
    public String execute(String param) throws Exception {
        List<RefundOrderDO> refundOrders = refundOrderMapper.selectTimeoutRefundOrders(Duration.ofMinutes(5));
        refundOrders.forEach(refundOrder -> {
            refundOrder.setRefundStatus(RefundStatusTypeEnum.FAILED.getCode());
            refundOrder.setRefundStatusUpdateTime(LocalDateTime.now());
        });
        if(refundOrderMapper.updateBatch(refundOrders)){
            return "扫描超时退款订单成功";
        }
        return "扫描超时退款订单失败";
    }
}
