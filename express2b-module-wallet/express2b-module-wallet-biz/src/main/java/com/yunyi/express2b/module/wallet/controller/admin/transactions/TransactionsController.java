package com.yunyi.express2b.module.wallet.controller.admin.transactions;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;

import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.*;

import com.yunyi.express2b.module.wallet.controller.admin.transactions.vo.*;
import com.yunyi.express2b.module.wallet.dal.dataobject.transactions.TransactionsDO;
import com.yunyi.express2b.module.wallet.service.transactions.TransactionsService;

@Tag(name = "管理后台 - 钱包流水")
@RestController
@RequestMapping("/wallet/transactions")
@Validated
public class TransactionsController {

    @Resource
    private TransactionsService transactionsService;

    @PostMapping("/create")
    @Operation(summary = "创建钱包流水")
    @PreAuthorize("@ss.hasPermission('wallet:transactions:create')")
    public CommonResult<Long> createTransactions(@Valid @RequestBody TransactionsSaveReqVO createReqVO) {
        return success(transactionsService.createTransactions(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新钱包流水")
    @PreAuthorize("@ss.hasPermission('wallet:transactions:update')")
    public CommonResult<Boolean> updateTransactions(@Valid @RequestBody TransactionsSaveReqVO updateReqVO) {
        transactionsService.updateTransactions(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除钱包流水")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('wallet:transactions:delete')")
    public CommonResult<Boolean> deleteTransactions(@RequestParam("id") Long id) {
        transactionsService.deleteTransactions(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得钱包流水")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('wallet:transactions:query')")
    public CommonResult<TransactionsRespVO> getTransactions(@RequestParam("id") Long id) {
        TransactionsDO transactions = transactionsService.getTransactions(id);
        return success(BeanUtils.toBean(transactions, TransactionsRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得钱包流水分页")
    @PreAuthorize("@ss.hasPermission('wallet:transactions:query')")
    public CommonResult<PageResult<TransactionsRespVO>> getTransactionsPage(@Valid TransactionsPageReqVO pageReqVO) {
        PageResult<TransactionsDO> pageResult = transactionsService.getTransactionsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TransactionsRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出钱包流水 Excel")
    @PreAuthorize("@ss.hasPermission('wallet:transactions:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTransactionsExcel(@Valid TransactionsPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TransactionsDO> list = transactionsService.getTransactionsPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "钱包流水.xls", "数据", TransactionsRespVO.class,
                        BeanUtils.toBean(list, TransactionsRespVO.class));
    }

    @GetMapping("/get-freeze-balance")
    @Operation(summary = "获得冻结余额")
    public CommonResult<TransactionsRespVO> getFreezeBalance(@Valid TransactionsRespVO transactionsRespVO) {
        TransactionsDO transactions = transactionsService.getFreezeBalance(transactionsRespVO.getAgentId());
        return success(BeanUtils.toBean(transactions, TransactionsRespVO.class));
    }
    @PutMapping("/update-freeze-balance")
    @Operation(summary = "普通解冻")
    public CommonResult<Boolean> updateFreezeBalance() {
        transactionsService.updateFreezeBalance();
        return success(true);
    }

}