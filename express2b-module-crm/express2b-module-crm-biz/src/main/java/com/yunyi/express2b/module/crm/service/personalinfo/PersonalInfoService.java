package com.yunyi.express2b.module.crm.service.personalinfo;
import java.util.*;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import jakarta.validation.*;
import com.yunyi.express2b.module.crm.controller.admin.personalinfo.vo.*;
import com.yunyi.express2b.module.crm.dal.dataobject.personalinfo.PersonalInfoDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;


/**
 * 个人信息 Service 接口
 *
 * <AUTHOR>
 */
public interface PersonalInfoService {

    /**
     * 创建个人信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPersonalInfo(@Valid PersonalInfoSaveReqVO createReqVO);

    /**
     * 更新个人信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePersonalInfo(@Valid PersonalInfoSaveReqVO updateReqVO);

    /**
     * 删除个人信息
     *
     * @param id 编号
     */
    void deletePersonalInfo(Long id);

    /**
     * 获得个人信息
     *
     * @param memberId 编号
     * @return 个人信息
     */
    PersonalInfoDO getPersonalInfo(Long memberId);

    /**
     * 获得个人信息分页
     *
     * @param pageReqVO 分页查询
     * @return 个人信息分页
     */
    PageResult<PersonalInfoDO> getPersonalInfoPage(PersonalInfoPageReqVO pageReqVO);
    /*
     * @Description: 发送审核
     * @param id
     * @return
     */
    void sendMessage(List<SendMessageVo> sendMessageVo);
    /*
         * @Description: 获取公司信息
         * @param id
         * @return
     */
    CommonResult<String> getCompany(Long id);
}