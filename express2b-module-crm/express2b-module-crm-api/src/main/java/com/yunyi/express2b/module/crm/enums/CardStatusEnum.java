package com.yunyi.express2b.module.crm.enums;

import com.yunyi.express2b.framework.common.core.ArrayValuable;
import lombok.Getter;

/**
 * CardStatusEnum
 * 用户次卡状态枚举
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/16 下午5:04
 */
@Getter
public enum CardStatusEnum implements ArrayValuable<String> {
    /**
     * 可用状态
     */
    ENABLE(0, "可用"),
    /**
     * 不可用状态
     */
    DISABLE(1, "不可用"),
    /**
     * 过期状态
     */
    DELETE(2, "过期");

    private final Integer code;
    private final String description;

    // 构造函数
    CardStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }


    @Override
    public String[] array() {
        return new String[0];
    }
}
