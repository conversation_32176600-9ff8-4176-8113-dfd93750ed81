-- 2025-5-06 苏德华
ALTER TABLE express2b_card_user
ADD COLUMN pay_order_sn VARCHAR(255) DEFAULT NULL COMMENT '支付订单号';

-- 2025-5-10 杨浩
alter table express2b_order
    modify product_code varchar(64) null comment '产品类型' after appointment_time;

alter table express2b_order
    modify card_number varchar(100) null comment '次卡编号' after product_code;

alter table express2b_order
    modify receiver_staff_mobile varchar(64) null comment '快递员电话' after card_number;

alter table express2b_order
    modify receiver_staff_pickup_code varchar(50) null comment '取件码' after receiver_staff_mobile;

-- 2025-5-13 苏德华
alter table express2b_pricing
    modify provincial_discount int not null comment '省内优惠金额--不参与计算';

alter table express2b_pricing
    modify out_of_province_discount int not null comment '省外优惠金额-不参与计算';

alter table express2b_pricing
    add first_weight_discount int null comment '首重优惠金额' after out_of_province_discount;

alter table express2b_pricing
    add first_amount_discount int null comment '单次续重优惠金额' after first_weight_discount;

alter table express2b_pricing
    modify provincial_discount int null comment '省内优惠金额--不参与计算';

alter table express2b_pricing
    modify out_of_province_discount int null comment '省外优惠金额-不参与计算';
update express2b_pricing set first_weight_discount=0 , first_amount_discount = 0 ;
alter table express2b_pricing
    modify first_weight_discount int default 0 not null comment '首重优惠金额';

alter table express2b_pricing
    modify first_amount_discount int default 0 not null comment '单次续重优惠金额';

-- 杨浩
ALTER TABLE express2b_order
MODIFY COLUMN item_weight INTEGER COMMENT '物品重量';

-- 2025-05-16
-- 苏德华，取消非空，后期需要删除这个字段还有company_id
alter table express2b_company_info
    modify credit_code varchar(200) null comment '（企业/组织唯一标识）';

-- 2025-05-23
-- 杨浩，增加快递订单状态字典
INSERT INTO `system_dict_type` (`name`, `type`, `status`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `deleted_time`)
VALUES ('快递订单状态', 'ORDER_STATUS_ENUM', 0, '快递订单相关状态枚举', 'admin', NOW(), 'admin', NOW(), b'0', NULL);
INSERT INTO `system_dict_data` (`id`, `sort`, `label`, `value`, `dict_type`, `status`, `color_type`, `css_class`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`)
VALUES
    (NULL, 0, '无', 'NULL', 'ORDER_STATUS_ENUM', 0, '', '', '', 'admin', NOW(), 'admin', NOW(), b'0'),
    (NULL, 1, '创建', 'CREATED', 'ORDER_STATUS_ENUM', 0, '', '', '', 'admin', NOW(), 'admin', NOW(), b'0'),
    (NULL, 2, '已支付', 'PAID', 'ORDER_STATUS_ENUM', 0, '', '', '', 'admin', NOW(), 'admin', NOW(), b'0'),
    (NULL, 3, '未支付', 'NOTPAY', 'ORDER_STATUS_ENUM', 0, '', '', '', 'admin', NOW(), 'admin', NOW(), b'0'),
    (NULL, 4, '待补运费', 'PENDING_ADDITIONAL_FEE', 'ORDER_STATUS_ENUM', 0, '', '', '', 'admin', NOW(), 'admin', NOW(), b'0'),
    (NULL, 5, '待接单', 'WAIT', 'ORDER_STATUS_ENUM', 0, '', '', '', 'admin', NOW(), 'admin', NOW(), b'0'),
    (NULL, 6, '已作废', 'CANCELED', 'ORDER_STATUS_ENUM', 0, '', '', '', 'admin', NOW(), 'admin', NOW(), b'0'),
    (NULL, 7, '待取件', 'PICKUP_PENDING', 'ORDER_STATUS_ENUM', 0, '', '', '', 'admin', NOW(), 'admin', NOW(), b'0'),
    (NULL, 8, '运输中', 'IN_TRANSIT', 'ORDER_STATUS_ENUM', 0, '', '', '', 'admin', NOW(), 'admin', NOW(), b'0'),
    (NULL, 9, '已结算', 'PARTIAL_PAYMENT_PENDING', 'ORDER_STATUS_ENUM', 0, '', '', '', 'admin', NOW(), 'admin', NOW(), b'0'),
    (NULL, 10, '已取消', 'CANCELED_AFTER_PAYMENT', 'ORDER_STATUS_ENUM', 0, '', '', '', 'admin', NOW(), 'admin', NOW(), b'0'),
    (NULL, 11, '退款中', 'REFUND_PROCESSING', 'ORDER_STATUS_ENUM', 0, '', '', '', 'admin', NOW(), 'admin', NOW(), b'0'),
    (NULL, 12, '已完成', 'COMPLETED', 'ORDER_STATUS_ENUM', 0, '', '', '', 'admin', NOW(), 'admin', NOW(), b'0'),
    (NULL, 13, '已退款', 'REFUNDED', 'ORDER_STATUS_ENUM', 0, '', '', '', 'admin', NOW(), 'admin', NOW(), b'0'),
    (NULL, 14, '拦截中', 'INTERCEPTED', 'ORDER_STATUS_ENUM', 0, '', '', '', 'admin', NOW(), 'admin', NOW(), b'0');


-- 2025-05-28
-- 杨浩，增加批量导入字典
INSERT INTO system_dict_type (name, type, status, remark, creator, create_time, updater, update_time, deleted, deleted_time)
SELECT '批量导入类型', 'Import_Type_Enum', 0, '定义系统中使用的批量导入类型', 'admin', NOW(), 'admin', NOW(), b'0', NULL
FROM DUAL
WHERE NOT EXISTS (
    SELECT 1
    FROM system_dict_type
    WHERE type = 'Import_Type_Enum'
);
INSERT INTO system_dict_data (sort, label, value, dict_type, status, color_type, css_class, remark, creator, create_time, updater, update_time, deleted)
VALUES
    (1, 'Excel导入', '1', 'Import_Type_Enum', 0, '', '', 'Excel文件导入类型', 'admin', NOW(), 'admin', NOW(), b'0'),
    (2, '手动粘贴', '2', 'Import_Type_Enum', 0, '', '', '手动复制粘贴导入类型', 'admin', NOW(), 'admin', NOW(), b'0');


-- 2025-05-29
-- 杨浩，更新快递品牌预约配置表
DELETE FROM `express2b_brand_scheduled_config`;
INSERT INTO `express2b_brand_scheduled_config`
(`brand_key`, `scheduled_days`, `scheduled_time_start`, `scheduled_time_end`, `interval_minutes`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`)
VALUES
    ('JT_BK', 3, '08:00:00', '10:00:00', 120, '1', '2025-05-08 10:28:10', '', '2025-05-09 11:59:13', b'0', 1),
    ('JT_BK', 3, '10:00:00', '12:00:00', 120, '1', '2025-05-08 10:29:05', '', '2025-05-09 11:59:17', b'0', 1),
    ('JT_BK', 3, '13:00:00', '15:00:00', 120, '1', '2025-05-08 10:29:46', '', '2025-05-09 11:59:21', b'0', 1),
    ('YTO_BK', 3, '08:00:00', '10:00:00', 120, '1', '2025-05-08 10:30:21', '', '2025-05-09 11:59:27', b'0', 1),
    ('YTO_BK', 3, '10:00:00', '12:00:00', 120, '1', '2025-05-08 10:31:01', '', '2025-05-09 11:59:38', b'0', 1),
    ('YTO_BK', 3, '13:00:00', '15:00:00', 120, '1', '2025-05-08 10:31:35', '', '2025-05-09 11:59:44', b'0', 1),
    ('JD_BK', 3, '08:00:00', '10:00:00', 120, '1', '2025-05-08 10:32:24', '', '2025-05-09 11:58:22', b'0', 1),
    ('JD_BK', 3, '10:00:00', '12:00:00', 120, '1', '2025-05-08 10:32:56', '', '2025-05-09 11:58:31', b'0', 1),
    ('JD_BK', 3, '13:00:00', '15:00:00', 120, '1', '2025-05-08 10:33:25', '', '2025-05-09 11:59:05', b'0', 1),
    ('STO_INT_BK', 3, '08:00:00', '10:00:00', 120, '1', '2025-05-08 20:00:47', '', '2025-05-28 21:01:03', b'0', 1),
    ('STO_INT_BK', 3, '10:00:00', '12:00:00', 120, '1', '2025-05-08 20:01:31', '', '2025-05-28 21:01:07', b'0', 1),
    ('STO_INT_BK', 3, '13:00:00', '15:00:00', 120, '1', '2025-05-08 20:04:13', '', '2025-05-28 21:01:11', b'0', 1),
    ('YUND_BK', 3, '10:00:00', '12:00:00', 120, '1', '2025-04-29 16:53:43', '', '2025-05-09 11:57:21', b'0', 1),
    ('YUND_BK', 3, '13:00:00', '15:00:00', 120, '1', '2025-04-29 16:54:57', '1', '2025-05-09 11:57:28', b'0', 1),
    ('YUND_BK', 3, '15:00:00', '17:00:00', 120, '1', '2025-05-09 13:56:45', '1', '2025-05-09 13:56:50', b'0', 1),
    ('JD_THS', 3, '08:00:00', '10:00:00', 120, '1', '2025-05-08 10:32:24', '', '2025-05-09 11:58:22', b'0', 1),
    ('JD_THS', 3, '10:00:00', '12:00:00', 120, '1', '2025-05-08 10:32:56', '', '2025-05-09 11:58:31', b'0', 1),
    ('JD_THS', 3, '13:00:00', '15:00:00', 120, '1', '2025-05-08 10:33:25', '', '2025-05-09 11:59:05', b'0', 1),
    ('ZTO_BK', 3, '08:00:00', '10:00:00', 120, '1', '2025-05-08 10:32:24', '', '2025-05-09 11:58:22', b'0', 1),
    ('ZTO_BK', 3, '10:00:00', '12:00:00', 120, '1', '2025-05-08 10:32:56', '', '2025-05-09 11:58:31', b'0', 1),
    ('ZTO_BK', 3, '13:00:00', '15:00:00', 120, '1', '2025-05-08 10:33:25', '', '2025-05-09 11:59:05', b'0', 1);


-- 2025-05-29
-- 杨浩，更新下载模板路径配置
UPDATE `infra_config` SET `category` = 'file', `type` = 2, `name` = '文件模板下载', `config_key` = 'excel', `value` = 'https://img.le-kc.com/download/importExcel.xlsx', `visible` = b'1', `remark` = '统一上传文件模板', `creator` = '1', `create_time` = '2025-04-23 20:32:48', `updater` = '1', `update_time` = '2025-04-23 20:34:24', `deleted` = b'0' WHERE `id` = 15;


-- 2025-05-30
-- 工单表
CREATE TABLE IF NOT EXISTS work_order (
  id BIGINT NOT NULL AUTO_INCREMENT COMMENT'工单id',
	work_order_sn VARCHAR(50) NOT NULL COMMENT'工单编号',
	member_id BIGINT COMMENT'SSO用户ID',
	operator_id  BIGINT COMMENT'操作员ID',
	order_sn VARCHAR(50) NOT NULL COMMENT'关联订单编号',
	type TINYINT COMMENT'类型',
	need_customer_service TINYINT COMMENT'是否需要客服介入：0-不需要 1-需要',
	passenger_question VARCHAR(255) COMMENT'问题描述',
	work_order_status TINYINT default 0 NOT NULL COMMENT'工单状态：0-未处理 （默认）1-未回复 2-已回复 3-已完结',
	customer_service_no VARCHAR(50) COMMENT'客服工号',
	customer_service_time datetime COMMENT'客服受理时间',
	customer_service_finish_time datetime COMMENT'客服完成时间',
	customer_service_reply text COMMENT'客服回复',
	work_notes	VARCHAR(255) COMMENT'工单备注',
	emergency_level TINYINT default 0 NOT NULL COMMENT'紧急程度：0-普通（默认） 1-紧急',
	first_work_type varchar(255) COMMENT'一级工单类型编码',
	second_work_type varchar(255) COMMENT'二级工单类型编码',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (id), -- 主键
  UNIQUE INDEX idx_work_order_sn (work_order_sn),  -- 工单编号唯一索引
  INDEX idx_order_sn (order_sn),  -- 关联订单编号索引
  INDEX idx_user_id (member_id)    -- 发起人ID索引
) COMMENT '工单表';


-- 图片表
CREATE TABLE IF NOT EXISTS work_order_file (
	id BIGINT NOT NULL AUTO_INCREMENT COMMENT'工单附件id',
	file VARCHAR(255) NOT NULL COMMENT'附件网络url地址',
	work_order_id BIGINT NOT NULL COMMENT'工单id',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
	PRIMARY KEY (id), -- 主键
	INDEX idx_work_order_id (work_order_id)  -- 工单ID索引
) COMMENT '工单附件表';

-- 2025-06-03 薛宁
ALTER TABLE work_order
ADD COLUMN tripleId BIGINT COMMENT '三方Id，调用远程服务时返回并存储进来';

-- 2025-06-07 王立阳
ALTER TABLE pricing_template
    -- v1 相关字段重命名
    CHANGE COLUMN markup_type markup_type_v1 tinyint NOT NULL DEFAULT '2' COMMENT '加价类型_v1 (1-比例加价，2-固定金额加价-默认)',
    CHANGE COLUMN first_weight_markup first_weight_markup_v1 int NOT NULL COMMENT '首重加价值_v1 (固定金额，单位:分)',
    CHANGE COLUMN additional_weight_markup additional_weight_markup_v1 int NOT NULL COMMENT '续重加价值_v1 (固定金额，单位:分)',
    CHANGE COLUMN platform_first_price platform_first_price_v1 int NOT NULL COMMENT '平台零售首重价_v1 (单位:分)',
    CHANGE COLUMN platform_additional_price platform_additional_price_v1 int NOT NULL COMMENT '平台零售续重价_v1 (单位:分)',
    -- 添加 v2 版本字段
    ADD COLUMN markup_type_v2 tinyint NOT NULL DEFAULT '2' COMMENT '加价类型_v2 (1-比例加价，2-固定金额加价-默认)' AFTER markup_type_v1,
    ADD COLUMN first_weight_markup_v2 int NOT NULL COMMENT '首重加价值_v2 (固定金额，单位:分)' AFTER markup_type_v2,
    ADD COLUMN additional_weight_markup_v2 int NOT NULL COMMENT '续重加价值_v2 (固定金额，单位:分)' AFTER first_weight_markup_v2,
    ADD COLUMN platform_first_price_v2 int NOT NULL COMMENT '平台零售首重价_v2 (单位:分)' AFTER additional_weight_markup_v2,
    ADD COLUMN platform_additional_price_v2 int NOT NULL COMMENT '平台零售续重价_v2 (单位:分)' AFTER platform_first_price_v2,
    -- 添加 v3 版本字段
    ADD COLUMN markup_type_v3 tinyint NOT NULL DEFAULT '2' COMMENT '加价类型_v3 (1-比例加价，2-固定金额加价-默认)' AFTER platform_additional_price_v2,
    ADD COLUMN first_weight_markup_v3 int NOT NULL COMMENT '首重加价值_v3 (固定金额，单位:分)' AFTER markup_type_v3,
    ADD COLUMN additional_weight_markup_v3 int NOT NULL COMMENT '续重加价值_v3 (固定金额，单位:分)' AFTER first_weight_markup_v3,
    ADD COLUMN platform_first_price_v3 int NOT NULL COMMENT '平台零售首重价_v3 (单位:分)' AFTER additional_weight_markup_v3,
    ADD COLUMN platform_additional_price_v3 int NOT NULL COMMENT '平台零售续重价_v3 (单位:分)' AFTER platform_first_price_v3;

-- 2025-06-09 杨浩
ALTER TABLE express2b_order ADD COLUMN share_amount int NOT NULL COMMENT '分润金额';

-- 2025-06-11 王立阳
ALTER TABLE pricing_template
MODIFY COLUMN first_weight_markup_v1 int NOT NULL COMMENT '首重重量_v1 (固定重量，单位:kg)',
MODIFY COLUMN first_weight_markup_v2 int NOT NULL COMMENT '首重重量_v2 (固定重量，单位:kg)',
MODIFY COLUMN first_weight_markup_v3 int NOT NULL COMMENT '首重重量_v3 (固定重量，单位:kg)',
MODIFY COLUMN additional_weight_markup_v1 int NOT NULL COMMENT '续重重量_v1 (固定重量，单位:kg)',
MODIFY COLUMN additional_weight_markup_v2 int NOT NULL COMMENT '续重重量_v2 (固定重量，单位:kg)',
MODIFY COLUMN additional_weight_markup_v3 int NOT NULL COMMENT '续重重量_v3 (固定重量，单位:kg)',
MODIFY COLUMN markup_type_v1 tinyint NOT NULL DEFAULT '2' COMMENT '加价类型_v1 (固定金额加价-自定义，2-固定金额加价-默认)',
MODIFY COLUMN markup_type_v2 tinyint NOT NULL DEFAULT '2' COMMENT '加价类型_v2 (固定金额加价-自定义，2-固定金额加价-默认)',
MODIFY COLUMN markup_type_v3 tinyint NOT NULL DEFAULT '2' COMMENT '加价类型_v3 (固定金额加价-自定义，2-固定金额加价-默认)';

-- 2025-06-12 薛宁
ALTER TABLE express2b_personal_info
ADD COLUMN member_id_card_encrypted TEXT COMMENT '加密后的身份证号' AFTER member_id_card;

-- 2025-06-13 杨浩
ALTER TABLE express2b_order
ADD COLUMN first_price INT COMMENT '首重价格，单位是分',
ADD COLUMN over_price INT COMMENT '续重价格，单位是分';

-- 2025-06-17 包洪帅
ALTER TABLE `wallet_transactions`
ADD COLUMN `is_unfrozen` tinyint(1)DEFAULT 0 COMMENT '是否解冻 (0:未解冻, 1:已解冻)' AFTER `available_at`;

-- 2025-06-17 杨浩
ALTER TABLE express2b_order
ADD COLUMN prepaid_amount INT COMMENT '代补运费金额';

-- 2025-06-18 杨浩
INSERT INTO `infra_config`(`category`, `type`, `name`, `config_key`, `value`, `visible`, `remark`, `creator`, `create_time`, `updater`, `update_time`, `deleted`) VALUES ('time', 2, '未支付超时时间', 'timeout_period', '5', b'1', '5分钟超时未付款，自动取消订单，重新下单', '1', '2025-06-18 09:13:54', '1', '2025-06-18 17:28:07', b'0');

-- 2025-06-19 解蕾
ALTER TABLE `express2b_order`
ADD COLUMN `bad_debt_checked` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '坏账检查标记(0-未检查 1-已检查)' AFTER `status`;

-- 为了提升查询性能，我们还需要在这个新字段和状态字段上创建一个复合索引
CREATE INDEX `idx_status_bad_debt` ON `express2b_order` (`status`, `bad_debt_checked`);

-- 2025-06-20 杨浩
CREATE TABLE express2b_work_opinion_collection (
    id INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    member_id INT DEFAULT NULL COMMENT '用户ID',
    opinion_content TEXT CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '意见内容',
    submission_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    opinion_status INT DEFAULT 1 COMMENT '意见状态（1:未处理，2:已处理）',
    member_status TINYINT DEFAULT 1 COMMENT '用户状态（1:正常，2:停用，3:注销）',
    remark VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
    type INT DEFAULT 1 COMMENT '类型（1:改善建议，2:问题反馈，3:合作意向）',
    title VARCHAR(255) DEFAULT NULL COMMENT '标题',
    order_sn VARCHAR(255) DEFAULT NULL COMMENT '关联订单表订单号',
    customer_phone VARCHAR(255) DEFAULT NULL COMMENT '用户手机号',
    file_name VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文件名字',
    file_url VARCHAR(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '文件路径',
      `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='意见收集表';

-- 2025-06-21 王立阳
ALTER TABLE `express2b_brand`
ADD COLUMN `lowest_first_price` int DEFAULT NULL COMMENT '该品牌最低首重价格（分）',
ADD COLUMN `lowest_additional_price` int DEFAULT NULL COMMENT '该品牌最低续重价格（分）',
ADD COLUMN `price_update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '价格信息更新时间';
