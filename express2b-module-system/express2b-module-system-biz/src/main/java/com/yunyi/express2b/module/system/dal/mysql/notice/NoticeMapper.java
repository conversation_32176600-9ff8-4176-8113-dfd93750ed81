package com.yunyi.express2b.module.system.dal.mysql.notice;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yunyi.express2b.framework.common.enums.CommonStatusEnum;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.module.system.controller.admin.notice.vo.NoticePageReqVO;
import com.yunyi.express2b.module.system.controller.admin.notice.vo.NoticeRespVO;
import com.yunyi.express2b.module.system.dal.dataobject.notice.NoticeDO;
import com.yunyi.express2b.module.system.enums.notice.NoticeTypeEnum;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface NoticeMapper extends BaseMapperX<NoticeDO> {

    default PageResult<NoticeDO> selectPage(NoticePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NoticeDO>()
                .likeIfPresent(NoticeDO::getTitle, reqVO.getTitle())
                .eqIfPresent(NoticeDO::getStatus, reqVO.getStatus())
                .orderByDesc(NoticeDO::getId));
    }

    /**
     * 获取对应类型notice列表
     * @return List<NoticeRespVO>
     */
    default List<NoticeDO> selectNoticeList(Integer type) {
        LambdaQueryWrapper<NoticeDO> queryWrapper = new LambdaQueryWrapper<NoticeDO>()
                .eq(NoticeDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .eq(NoticeDO::getType, type);

        return selectList(queryWrapper);
    }

}
