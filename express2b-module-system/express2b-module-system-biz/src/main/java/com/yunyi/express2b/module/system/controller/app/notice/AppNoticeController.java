package com.yunyi.express2b.module.system.controller.app.notice;

import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.system.controller.admin.notice.vo.NoticePageReqVO;
import com.yunyi.express2b.module.system.controller.admin.notice.vo.NoticeRespVO;
import com.yunyi.express2b.module.system.dal.dataobject.notice.NoticeDO;
import com.yunyi.express2b.module.system.service.notice.NoticeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

/**
 * app公告获取controller
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/28 上午9:52
 */
@Tag(name = "用户APP - 公告管理")
@RestController
@RequestMapping("/v1/system/notice")
@Validated
@RequiredArgsConstructor
public class AppNoticeController {
    private final NoticeService noticeService;
    @GetMapping("/list")
    @Operation(summary = "获取所有公告列表")
    @PreAuthorize("@ss.hasPermission('system:notice:query')")
    public CommonResult<List<NoticeRespVO>> getNoticeList() {
        List<NoticeRespVO> noticeList = noticeService.getAnnouncementList();
        return success(noticeList);
    }
}
