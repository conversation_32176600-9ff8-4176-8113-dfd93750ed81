package com.yunyi.express2b.module.agent.enums;

import com.yunyi.express2b.framework.common.exception.ErrorCode;

/**
 * Agent 错误码枚举类
 *
 * 代理商系统，使用 1_004_000_000 段
 * 代理商档案模块：1_004_001_xxx
 * 定价规则模块：1_004_002_xxx
 * 价格模板模块：1_004_003_xxx
 * 等级配置模块：1_004_004_xxx
 * 团队关系模块：1_004_005_xxx
 * 代理商基础模块：1_004_006_xxx
 * 业绩统计模块：1_004_007_xxx
 * 页面模板模块：1_004_008_xxx
 */
public interface ErrorCodeConstants {

    // ========== 代理商档案模块 1_004_001_xxx ==========
    ErrorCode PROFILE_NOT_EXISTS = new ErrorCode(1_004_001_000, "代理商档案不存在");
    ErrorCode CUSTOM_TEMPLATE_REQUEST_NOT_EXISTS = new ErrorCode(1_004_001_001, "代理商自定义价格模板申请不存在");
    ErrorCode PAGE_TEMPLATE_NOT_EXISTS = new ErrorCode(1_004_001_002, "页面模板不存在");

    // ========== 定价规则模块 1_004_002_xxx ==========
    ErrorCode PRICING_RULE_NOT_EXISTS = new ErrorCode(1_004_002_000, "定价规则不存在");

    // ========== 价格模板模块 1_004_003_xxx ==========
    ErrorCode TEMPLATE_NOT_EXISTS = new ErrorCode(1_004_003_000, "价格模板不存在");

    // ========== 等级配置模块 1_004_004_xxx ==========
    ErrorCode LEVEL_CONFIG_NOT_EXISTS = new ErrorCode(1_004_004_000, "代理商等级配置不存在");

    // ========== 团队关系模块 1_004_005_xxx ==========
    ErrorCode RELATIONSHIP_NOT_EXISTS = new ErrorCode(1_004_005_000, "代理商团队关系不存在");

    // ========== 代理商基础模块 1_004_006_xxx ==========
    ErrorCode AGENT_NOT_EXISTS = new ErrorCode(1_004_006_000, "代理商不存在");
    ErrorCode AGENT_EXISTS = new ErrorCode(1_004_006_001, "代理商已存在");
    ErrorCode MOBILE_EXISTS = new ErrorCode(1_004_006_002, "手机号已存在");

    // ========== 业绩统计模块 1_004_007_xxx ==========
    ErrorCode DAILY_PERFORMANCE_STAT_NOT_EXISTS = new ErrorCode(1_004_007_000, "代理商日业绩统计不存在");
    ErrorCode DAILY_TEMPLATE_NOT_EXISTS = new ErrorCode(1_004_008_000, "代理商模版状态修改失败");
}