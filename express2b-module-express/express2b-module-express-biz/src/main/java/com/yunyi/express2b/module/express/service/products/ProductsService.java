package com.yunyi.express2b.module.express.service.products;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.module.express.controller.admin.products.vo.ProductsPageReqVO;
import com.yunyi.express2b.module.express.controller.admin.products.vo.ProductsSaveReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.products.ProductsDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 物品信息 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductsService {

    /**
     * 创建物品信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProducts(@Valid ProductsSaveReqVO createReqVO);

    /**
     * 更新物品信息
     *
     * @param updateReqVO 更新信息
     */
    void updateProducts(@Valid ProductsSaveReqVO updateReqVO);

    /**
     * 删除物品信息
     *
     * @param id 编号
     */
    void deleteProducts(Long id);

    /**
     * 获得物品信息
     *
     * @param id 编号
     * @return 物品信息
     */
    ProductsDO getProducts(Long id);

    /**
     * 获得物品信息分页
     *
     * @param pageReqVO 分页查询
     * @return 物品信息分页
     */
    PageResult<ProductsDO> getProductsPage(ProductsPageReqVO pageReqVO);

    List<ProductsDO> selectProductsInfo(Long id);
}