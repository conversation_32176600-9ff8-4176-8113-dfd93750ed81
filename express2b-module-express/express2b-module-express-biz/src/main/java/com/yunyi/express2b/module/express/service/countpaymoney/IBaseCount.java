package com.yunyi.express2b.module.express.service.countpaymoney;

import com.yunyi.express2b.module.express.service.countpaymoney.dto.OrderDetailDTO;

/**
 * 计算运价的基础接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/28 19:52
 */
public interface IBaseCount {
    /**
     * 计算运价
     * @param orderDetail 订单详情
     * @return 应收款金额
     */
    Integer countPayMoney(OrderDetailDTO orderDetail);
}
