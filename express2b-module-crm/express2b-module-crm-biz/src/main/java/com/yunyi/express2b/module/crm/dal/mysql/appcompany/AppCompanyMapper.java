package com.yunyi.express2b.module.crm.dal.mysql.appcompany;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.module.crm.controller.admin.address.vo.AddressPageReqVO;
import com.yunyi.express2b.module.crm.controller.admin.companyinfo.vo.CompanyInfoPageReqVO;
import com.yunyi.express2b.module.crm.dal.dataobject.address.AddressDO;
import com.yunyi.express2b.module.crm.dal.dataobject.companyinfo.CompanyInfoDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 公司审核信息mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/26 11:17
 */
@Mapper
public interface AppCompanyMapper extends BaseMapperX<CompanyInfoDO>{
    default PageResult<CompanyInfoDO> selectPage(CompanyInfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CompanyInfoDO>()
                .eqIfPresent(CompanyInfoDO::getCompanyId, reqVO.getCompanyId())
                .likeIfPresent(CompanyInfoDO::getCompanyName, reqVO.getCompanyName())
                .likeIfPresent(CompanyInfoDO::getContactName, reqVO.getContactName())
                .eqIfPresent(CompanyInfoDO::getContactPhone, reqVO.getContactPhone())
                .eqIfPresent(CompanyInfoDO::getCompanyLicense, reqVO.getCompanyLicense())
                .eqIfPresent(CompanyInfoDO::getCompanyLicense1, reqVO.getCompanyLicense1())
                .eqIfPresent(CompanyInfoDO::getCompanyLicense2, reqVO.getCompanyLicense2())
                .eqIfPresent(CompanyInfoDO::getCompanyType, reqVO.getCompanyType())
                .eqIfPresent(CompanyInfoDO::getStatus, reqVO.getStatus())
                .eqIfPresent(CompanyInfoDO::getRemarks, reqVO.getRemarks())
                .betweenIfPresent(CompanyInfoDO::getAuditTime, reqVO.getAuditTime())
                .betweenIfPresent(CompanyInfoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(CompanyInfoDO::getId));
    }
}

