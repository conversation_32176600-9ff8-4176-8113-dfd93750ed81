package com.yunyi.express2b.module.crm.controller.app.v1.carflimited.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * CardLimited
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/3 下午8:23
 */
@Schema(description = "商城内卡片 VO")
@Data
@ToString(callSuper = true)
public class CardLimitedVO {



    /*
         * id
     */
    private Long id;
    /*
        用于存储卡片名称
     */
    private String cardlimitedName;
    /*
     * 使用次数
     */
    private Integer number;
    /*
     * 品牌可用名
     */
    private  String brandNames;
    /*
     * 描述
     */
    private String description;
    /*
     * 价格
     */
    private Integer price;
    /*
     * 有效期
     */
    private Integer validityPeriod;

}
