package com.yunyi.express2b.module.express.dal.dataobject.orderscheduledtime;

import lombok.*;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 订单预约取件时间 DO
 *
 * <AUTHOR>
 */
@TableName("express2b_order_scheduled_time")
@KeySequence("express2b_order_scheduled_time_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderScheduledTimeDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 快递品牌标识
     */
    private String brandKey;
    /**
     * 预约取件日期
     */
    private LocalDate scheduledDate;
    /**
     * 预约取件时间
     */
    private LocalTime scheduledTime;

}