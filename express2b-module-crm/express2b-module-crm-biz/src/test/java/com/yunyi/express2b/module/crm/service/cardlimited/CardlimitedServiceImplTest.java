package com.yunyi.express2b.module.crm.service.cardlimited;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.module.crm.controller.admin.cardlimited.vo.CardlimitedPageReqVO;
import com.yunyi.express2b.module.crm.controller.admin.cardlimited.vo.CardlimitedSaveReqVO;
import com.yunyi.express2b.module.crm.dal.dataobject.cardlimited.CardlimitedDO;
import com.yunyi.express2b.module.crm.dal.mysql.cardlimited.CardlimitedMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import static com.yunyi.express2b.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static com.yunyi.express2b.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertPojoEquals;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertServiceException;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomLongId;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomPojo;
import static com.yunyi.express2b.module.crm.enums.ErrorCodeConstants.CARDLIMITED_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link CardlimitedServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(CardlimitedServiceImpl.class)
public class CardlimitedServiceImplTest extends BaseDbUnitTest {

    @Resource
    private CardlimitedServiceImpl cardlimitedService;

    @Resource
    private CardlimitedMapper cardlimitedMapper;

    @Test
    public void testCreateCardlimited_success() {
        // 准备参数
        CardlimitedSaveReqVO createReqVO = randomPojo(CardlimitedSaveReqVO.class).setId(null);

        // 调用
        Long cardlimitedId = cardlimitedService.createCardlimited(createReqVO);
        // 断言
        assertNotNull(cardlimitedId);
        // 校验记录的属性是否正确
        CardlimitedDO cardlimited = cardlimitedMapper.selectById(cardlimitedId);
        assertPojoEquals(createReqVO, cardlimited, "id");
    }

    @Test
    public void testUpdateCardlimited_success() {
        // mock 数据
        CardlimitedDO dbCardlimited = randomPojo(CardlimitedDO.class);
        cardlimitedMapper.insert(dbCardlimited);// @Sql: 先插入出一条存在的数据
        // 准备参数
        CardlimitedSaveReqVO updateReqVO = randomPojo(CardlimitedSaveReqVO.class, o -> {
            o.setId(dbCardlimited.getId()); // 设置更新的 ID
        });

        // 调用
        cardlimitedService.updateCardlimited(updateReqVO);
        // 校验是否更新正确
        CardlimitedDO cardlimited = cardlimitedMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, cardlimited);
    }

    @Test
    public void testUpdateCardlimited_notExists() {
        // 准备参数
        CardlimitedSaveReqVO updateReqVO = randomPojo(CardlimitedSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> cardlimitedService.updateCardlimited(updateReqVO), CARDLIMITED_NOT_EXISTS);
    }

    @Test
    public void testDeleteCardlimited_success() {
        // mock 数据
        CardlimitedDO dbCardlimited = randomPojo(CardlimitedDO.class);
        cardlimitedMapper.insert(dbCardlimited);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbCardlimited.getId();

        // 调用
        cardlimitedService.deleteCardlimited(id);
        // 校验数据不存在了
        assertNull(cardlimitedMapper.selectById(id));
    }

    @Test
    public void testDeleteCardlimited_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> cardlimitedService.deleteCardlimited(id), CARDLIMITED_NOT_EXISTS);
    }


}