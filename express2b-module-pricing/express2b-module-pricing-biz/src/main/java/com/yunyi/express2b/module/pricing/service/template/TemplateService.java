package com.yunyi.express2b.module.pricing.service.template;

import java.util.*;

import com.yunyi.express2b.module.pricing.dal.dataobject.template.PricingTemplateDetailDTO;
import jakarta.validation.*;
import com.yunyi.express2b.module.pricing.controller.admin.template.vo.*;
import com.yunyi.express2b.module.pricing.dal.dataobject.template.TemplateDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;

/**
 * 定价模板 Service 接口
 *
 * <AUTHOR>
 */
public interface TemplateService {

    /**
     * 创建定价模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTemplate(@Valid TemplateSaveReqVO createReqVO);

    /**
     * 更新定价模板
     *
     * @param updateReqVO 更新信息
     */
    void updateTemplate(@Valid TemplateSaveReqVO updateReqVO);

    /**
     * 删除定价模板
     *
     * @param id 编号
     */
    void deleteTemplate(Long id);

    /**
     * 获得定价模板
     *
     * @param id 编号
     * @return 定价模板
     */
    TemplateDO getTemplate(Long id);
    /**
     * 获得定价模板详细信息
     *
     * @param  id 编号
     * @return 定价模板
     */
    PricingTemplateDetailDTO getTemplateid(Long id);

    /**
     * 获得定价模板分页
     *
     * @param pageReqVO 分页查询
     * @return 定价模板分页
     */
    PageResult<TemplateDO> getTemplatePage(TemplatePageReqVO pageReqVO);
    /**
     * 作为代理商我可以查询平台提供的定价模板，了解首重/续重价格规则
     * @param
     * @return
     */
    List<PricingTemplateDetailDTO> getTemplatePricingRules(Long templateId);
}