package com.yunyi.express2b.module.express.service.order;

import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.security.core.LoginUser;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.module.crm.api.address.AddressQueryApi;
import com.yunyi.express2b.module.crm.api.address.vo.AddressRespApiVO;
import com.yunyi.express2b.module.crm.api.card.cardstream.CardStreamApi;
import com.yunyi.express2b.module.crm.api.card.cardstream.vo.CardStreamSaveReqVO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.OrderCreateRequest;
import com.yunyi.express2b.module.express.controller.admin.order.vo.OrderSaveReqVO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.RecipientVO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.mysql.batchorder.BatchOrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.paymentorder.PaymentOrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.products.ProductsMapper;
import com.yunyi.express2b.module.express.data.GenerateOrderTestData;
import com.yunyi.express2b.module.express.service.message.SmsService;
import com.yunyi.express2b.module.express.service.orderstatus.OrderStatusService;
import com.yunyi.express2b.module.express.service.pay.PayServiceImpl;
import com.yunyi.express2b.module.express.service.price.PriceService;
import com.yunyi.express2b.module.express.service.products.ProductsService;
import com.yunyi.express2b.module.express.service.pay.RefundService;
import com.yunyi.framework.api.login.api.image.ImageApi;
import com.yunyi.framework.api.logistics.api.express.ExpressApi;
import com.yunyi.framework.api.logistics.api.order.OrderApi;
import com.yunyi.framework.api.logistics.api.order.vo.CreateOrderResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;

import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomPojo;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * {@link OrderServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Slf4j
@Import({OrderServiceImplTest.TestConfig.class, OrderMapper.class, OrderServiceImpl.class, PaymentOrderMapper.class,PayServiceImpl.class})
public class OrderServiceImplTest extends BaseDbUnitTest {

    @Configuration
    static class TestConfig {

        @Bean("myTaskExecutor")
        public Executor myTaskExecutor() {
            ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
            executor.setCorePoolSize(2);
            executor.setMaxPoolSize(4);
            executor.setQueueCapacity(100);
            executor.setThreadNamePrefix("test-executor-");
            executor.initialize();
            return executor;
        }
    }

    @Resource
    private OrderServiceImpl orderService;
    @Resource
    private OrderMapper orderMapper;
    @MockitoBean
    private BatchOrderMapper batchOrderMapper;
    @MockitoBean
    private RefundService refundService;
    @MockitoBean
    private ExpressApi expressApi;
    @MockitoBean
    private OrderStatusService orderStatusService;
    @MockitoBean
    private SmsService smsService;
    @MockitoBean
    private ImageApi imageApi;
    @MockitoBean
    private RedisTemplate redisTemplate;
    @MockitoBean
    private CardStreamApi cardStreamApi;
    @MockitoBean
    private ProductsService productsService;
    @MockitoBean
    private OrderApi orderApi;
    @MockitoBean
    private AddressQueryApi addressQueryApi;
    @MockitoBean
    private PriceService priceService;
    @MockitoBean
    private PayServiceImpl payService;
    @MockitoBean
    private ProductsMapper productsMapper;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testCreateOrder_singleOrder_success() {
        // 设置登录用户
        Long userId = 1L;

        // 模拟登录用户
        LoginUser mockLoginUser = new LoginUser();
        mockLoginUser.setId(userId);
        mockStatic(SecurityFrameworkUtils.class);
        when(SecurityFrameworkUtils.getLoginUser()).thenReturn(mockLoginUser);
        when(SecurityFrameworkUtils.getLoginUserNickname()).thenReturn("testUser");

        // 准备参数
        OrderSaveReqVO orderSaveReqVO = randomPojo(OrderSaveReqVO.class);
        orderSaveReqVO.setPersonalId(userId);
        RecipientVO recipientVO = randomPojo(RecipientVO.class);
        List<RecipientVO> recipientVOList = new ArrayList<>();
        recipientVOList.add(recipientVO);

        // 模拟 cardStreamApi
        when(cardStreamApi.createCardStream(any(CardStreamSaveReqVO.class))).thenReturn(1L);

        // 调用方法
        //List<Long> orderIds = orderService.createOrder(orderSaveReqVO, recipientVOList);

        // 断言
//        assertNotNull(orderIds);
//        assertEquals(1, orderIds.size());
//        assertNotNull(orderIds.get(0));

        // 校验订单属性是否正确
        // OrderDO order = orderMapper.selectById(orderIds.get(0));
//        assertNotNull(order);
//        assertEquals(orderSaveReqVO.getPersonalId(), order.getPersonalId());
//        assertEquals(OrderStatusTypeEnum.CREATED.getStatus(), order.getStatus());
    }


    @Test
    public void testValidateOrderExistsByOrderNo_orderExists() {
        // 准备参数
        String orderNo = "TEST_ORDER_001";

        OrderDO orderDO = randomPojo(OrderDO.class);
        orderDO.setOrderNo(orderNo);
        orderMapper.insert(orderDO);
        // 调用方法
        boolean result = orderMapper.validateOrderExistsByOrderNo(orderNo);

        // 断言
        assertFalse(result, "订单存在时应该返回false");
    }

    @Test
    public void testValidateOrderExistsByOrderNo_orderNotExists() {
        // 准备参数
        String orderNo = "NON_EXISTENT_ORDER";

        // 调用方法
        boolean result = orderMapper.validateOrderExistsByOrderNo(orderNo);

        // 断言
        assertTrue(result, "订单不存在时应该返回true");
    }

    @Test
    public void orderCreate() {
        System.out.println("订单创建");
        // 设置登录用户
        Long userId = 1L;
        System.out.println("userId" + userId);
        // 模拟登录用户
        LoginUser mockLoginUser = new LoginUser();
        mockLoginUser.setId(userId);
        mockStatic(SecurityFrameworkUtils.class);
        when(SecurityFrameworkUtils.getLoginUser()).thenReturn(mockLoginUser);
        when(SecurityFrameworkUtils.getLoginUserNickname()).thenReturn("testUser");

        // 准备参数
        //OrderSaveReqVO orderSaveReqVO = randomPojo(OrderSaveReqVO.class);
        //orderSaveReqVO.setPersonalId(userId);
        OrderDO orderDO = GenerateOrderTestData.generateOrderSuccess();
        OrderCreateRequest orderCreateRequest = BeanUtils.toBean(orderDO, OrderCreateRequest.class);
        orderCreateRequest.setPersonalId(1L);
        try {
            CreateOrderResponse trackingNumber = orderService.createTracking(orderCreateRequest);
            System.out.println(trackingNumber);
        } catch (Exception e) {
            log.debug("创建订单失败", e.getMessage());
        }

    }

    @Test
    public void requestPrice() {
//        PriceQueryReqVO priceQueryReqVO= PriceQueryReqVO.builder()
//                .senderProvince("北京")
//                .senderCity("北京")
//                .receiverProvince("北京")
//                .receiverCity("北京")
//                .expressCode("YD")
//                .weight(2)
//                .useSubCard(1)
//                .build();
        orderService.queryPrice(null, null);

    }

    @Test
    public void OrderCreateRequest() {
        OrderCreateRequest orderCreateRequest = GenerateOrderTestData.yunliOrder();
        try {

            // 模拟
            AddressRespApiVO addressRespApiVO = randomPojo(AddressRespApiVO.class);
            AddressRespApiVO addressRespApiVO1 = randomPojo(AddressRespApiVO.class);
            List<AddressRespApiVO> addressRespVOList = new ArrayList<>();
            addressRespVOList.add(addressRespApiVO1);
            addressRespVOList.add(addressRespApiVO);
            when(addressQueryApi.getAddress(any())).thenReturn(addressRespVOList);

            CreateOrderResponse tracking = orderService.createTracking(orderCreateRequest);
            log.debug("tracking" + tracking);
        } catch (Exception e) {
            log.error("下单失败", e.getMessage());
        }
    }

}