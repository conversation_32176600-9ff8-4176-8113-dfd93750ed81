package com.yunyi.express2b.module.crm.service.address;

import com.yunyi.express2b.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.security.core.util.CrmSecurityFrameworkUtils;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.crm.controller.admin.address.vo.AddressPageReqVO;
import com.yunyi.express2b.module.crm.controller.admin.address.vo.AddressRespVO;
import com.yunyi.express2b.module.crm.controller.admin.address.vo.AddressSaveReqVO;
import com.yunyi.express2b.module.crm.controller.app.v1.personal.address.vo.AddressDefaultReqVO;
import com.yunyi.express2b.module.crm.controller.app.v1.personal.address.vo.AddressFuzzyQueryVO;
import com.yunyi.express2b.module.crm.controller.app.v1.personal.address.vo.AddressUploadReqVO;
import com.yunyi.express2b.module.crm.dal.dataobject.address.AddressDO;
import com.yunyi.express2b.module.crm.dal.mysql.address.AddressMapper;
import com.yunyi.express2b.module.crm.enums.AddressDefaultEnum;
import com.yunyi.express2b.module.crm.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.crm.mapstruct.AddressMapStructMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 地址簿管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AddressServiceImpl implements AddressService {

    @Resource
    private AddressMapper addressMapper;


    /**
     * 创建地址簿管理
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Long createAddress(AddressSaveReqVO createReqVO) {
        // 插入
        AddressDO address = BeanUtils.toBean(createReqVO, AddressDO.class);
        addressMapper.insert(address);
        // 返回
        return address.getId();
    }

    /**
     * 更新地址簿管理
     * @param updateReqVO
     */
    @Override
    public void updateAddress(AddressSaveReqVO updateReqVO) {
        // 校验存在
        AddressDO addressDo = validateAddressExists(updateReqVO.getId());
        if (!addressDo.getMemberId().equals(CrmSecurityFrameworkUtils.getLoginUserId())) {
            throw exception(GlobalErrorCodeConstants.FORBIDDEN);
        }
        // 更新
        AddressDO updateObj = BeanUtils.toBean(updateReqVO, AddressDO.class);
        addressMapper.updateById(updateObj);
    }

    /**
     * 更新地址簿管理
     * @param uploadReqVOReqVO 更新信息
     */
    @Override
    public void appupdateAddress(AddressUploadReqVO uploadReqVOReqVO) {
        // 校验存在
        AddressDO addressDo = validateAddressExists(uploadReqVOReqVO.getId());
        if (!addressDo.getMemberId().equals(CrmSecurityFrameworkUtils.getLoginUserId())) {
            throw exception(GlobalErrorCodeConstants.FORBIDDEN);
        }
        // 更新
        AddressDO updateObj = BeanUtils.toBean(uploadReqVOReqVO, AddressDO.class);
        addressMapper.updateById(updateObj);
    }

    /**
     * 删除地址簿管理
     * @param id 编号
     */
    @Override
    public void deleteAddress(Long id) {
        // 校验存在
        validateAddressExists(id);
        // 删除
        addressMapper.deleteById(id);
    }

    /**
     * 校验地址是否存在
     * @param id
     * @return
     */
    private AddressDO validateAddressExists(Long id) {
        AddressDO addressDO = addressMapper.selectById(id);
        if (addressDO == null) {
            throw exception(ErrorCodeConstants.ADDRESS_NOT_EXISTS);
        }
        return addressDO;
    }

    /**
     * 查询地址
     * @param id 编号
     * @return
     */
    @Override
    public AddressDO getAddress(Long id) {
        return addressMapper.selectById(id);
    }

    /**
     * 分页查询地址簿管理
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<AddressDO> getAddressPage(AddressPageReqVO pageReqVO) {
        return addressMapper.selectPageByMember(pageReqVO);
    }

    /**
     * 批量删除地址
     * @param ids 一个包含多个地址ID的列表，用于指定需要删除的地址
     * @param memberId
     * @return
     */
    @Override
    public Integer delAddressByIdsAndMember(List<Long> ids, Long memberId) {
        return addressMapper.deleteByIdsAndMemberId(ids, memberId);
    }

    /**
     * 修改默认地址
      * @param addressReqVo
     * @param loginUserId
     * @return
     */
    @Override
    public Boolean updateDefaultAddress(AddressDefaultReqVO addressReqVo, Long loginUserId) {
        // 校验地址是否存在并获取地址对象
        AddressDO addressDO = validateAddressExists(addressReqVo.getId());
        // 校验当前用户是否为地址所有者
        if (loginUserId != null && !addressDO.getMemberId().equals(loginUserId)) {
            throw exception(GlobalErrorCodeConstants.FORBIDDEN);
        }
        // 获取请求的默认状态值
        Integer isDefault = addressReqVo.getIsDefault();
        if (AddressDefaultEnum.DEFAULT.getCode().equals(isDefault)) {
            // 如果设置为默认地址，先将该用户同类型的所有地址设为非默认
            addressMapper.resetAllAddressesToNotDefault(addressDO.getMemberId(), addressReqVo.getAddressType());
        }
        // 更新当前地址的默认状态
        addressMapper.updateAddressToDefault(addressReqVo.getId(), addressReqVo.getIsDefault());
        // 返回更新后的地址列表，按照地址类型过滤
        return Boolean.TRUE;
    }

    /**
     * 批量创建地址
     * @param addresses 地址请求对象列表，包含待创建的地址信息
     * @return
     */
    @Override
    public List<AddressRespVO> createAddressBatch(List<AddressSaveReqVO> addresses) {
        for (AddressSaveReqVO address : addresses) {
            address.setMemberId(SecurityFrameworkUtils.getLoginUserId());
        }
        List<AddressDO> addressDOS = AddressMapStructMapper.INSTANCE.addressToDoList(addresses);
        addressDOS.forEach(address -> address.setSort(address.getName()));
        if (addressMapper.insertBatch(addressDOS)) {
            return BeanUtils.toBean(addressDOS, AddressRespVO.class);
        }else {
            throw exception(ErrorCodeConstants.ADDRESS_ADD_ERROR);
        }
    }


    @Override
    public List<AddressDO> selectAddressByIds(List<Long> ids) {
        List<AddressDO> addressDOS = addressMapper.selectAddressByIds(ids);
        return addressDOS;
    }

    @Override
    public PageResult<AddressDO> fuzzySearchAddress(AddressFuzzyQueryVO queryVO, Long memberId) {
        // 验证查询条件长度，手机号和姓名至少有一个且长度≥2
        if ((queryVO.getPhone() == null || queryVO.getPhone().length() < 2)
                && (queryVO.getName() == null || queryVO.getName().length() < 2)) {
            // 如果没有有效的查询条件，返回空结果
            PageResult<AddressDO> addressDOPageResult = new PageResult<>();
            addressDOPageResult.setList(List.of());
            return addressDOPageResult;
        }
        AddressPageReqVO pageReqVO = new AddressPageReqVO();
        pageReqVO.setPhone(queryVO.getPhone());
        pageReqVO.setName(queryVO.getName());
        pageReqVO.setMemberId(memberId);
        // 调用Mapper执行模糊查询
        return addressMapper.fuzzySearchAddress(pageReqVO);
    }
}


