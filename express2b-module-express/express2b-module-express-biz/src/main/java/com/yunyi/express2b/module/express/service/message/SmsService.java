package com.yunyi.express2b.module.express.service.message;

import com.yunyi.express2b.module.express.api.message.dto.OrderChangeDTO;
import com.yunyi.express2b.module.express.api.message.dto.PaymentOrderDTO;

/**
 * 消息通知 Service 接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/25 上午11:43
 */
public interface SmsService {
    /**
     * 用户在订单状态变更时收到系统的消息通知
     */
    Integer sendOrderChangeMessage (OrderChangeDTO orderChangeDTO);

    /**
     * 用户在生成新的支付单时收到消息通知
     */
    void sendPaymentOrderMessage (PaymentOrderDTO paymentOrderDTO);


}
