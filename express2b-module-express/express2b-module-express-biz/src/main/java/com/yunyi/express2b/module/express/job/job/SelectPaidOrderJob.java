package com.yunyi.express2b.module.express.job.job;

import cn.hutool.db.sql.Order;
import com.yunyi.express2b.framework.quartz.core.handler.JobHandler;
import com.yunyi.express2b.framework.tenant.core.aop.TenantIgnore;
import com.yunyi.express2b.framework.tenant.core.context.TenantContextHolder;
import com.yunyi.express2b.framework.tenant.core.util.TenantUtils;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.enums.OrderStatusTypeEnum;
import com.yunyi.express2b.module.express.service.order.OrderService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/6/7 11:15
 */
@Component
@Slf4j
public class SelectPaidOrderJob implements JobHandler {
    @Resource
    private OrderMapper orderMapper;

    @Resource
    private OrderService orderService;

    @Override
    @TenantIgnore
    public String execute(String param) throws Exception {
        log.info("【开始执行查询已付款订单的定时任务】----------------");

        try {
            orderService.createOrderToPromo();
        } catch (Exception e) {
            log.error("定时任务执行失败，原因：{}", e.getMessage(), e);
            throw new RuntimeException("定时任务执行失败", e);
        }

        return String.format("定时任务执行成功");
    }
}
