package com.yunyi.express2b.module.pricing.controller.admin.pricingrule;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;

import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.*;

import com.yunyi.express2b.module.pricing.controller.admin.pricingrule.vo.*;
import com.yunyi.express2b.module.pricing.dal.dataobject.pricingrule.PricingRuleDO;
import com.yunyi.express2b.module.pricing.service.pricingrule.PricingRuleService;

@Tag(name = "管理后台 - 代理商定价（终端零售价）规则")
@RestController
@RequestMapping("/pricing/rule")
@Validated
public class PricingRuleController {

    @Resource
    private PricingRuleService ruleService;

    @PostMapping("/create")
    @Operation(summary = "创建代理商定价（终端零售价）规则")
    @PreAuthorize("@ss.hasPermission('pricing:rule:create')")
    public CommonResult<Long> createRule(@Valid @RequestBody PricingRuleSaveReqVO createReqVO) {
        return success(ruleService.createRule(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代理商定价（终端零售价）规则")
    @PreAuthorize("@ss.hasPermission('pricing:rule:update')")
    public CommonResult<Boolean> updateRule(@Valid @RequestBody PricingRuleSaveReqVO updateReqVO) {
        ruleService.updateRule(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代理商定价（终端零售价）规则")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pricing:rule:delete')")
    public CommonResult<Boolean> deleteRule(@RequestParam("id") Long id) {
        ruleService.deleteRule(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代理商定价（终端零售价）规则")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pricing:rule:query')")
    public CommonResult<PricingRuleRespVO> getRule(@RequestParam("id") Long id) {
        PricingRuleDO rule = ruleService.getRule(id);
        return success(BeanUtils.toBean(rule, PricingRuleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代理商定价（终端零售价）规则分页")
    @PreAuthorize("@ss.hasPermission('pricing:rule:query')")
    public CommonResult<PageResult<PricingRuleRespVO>> getRulePage(@Valid PricingRulePageReqVO pageReqVO) {
        PageResult<PricingRuleDO> pageResult = ruleService.getRulePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PricingRuleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代理商定价（终端零售价）规则 Excel")
    @PreAuthorize("@ss.hasPermission('pricing:rule:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportRuleExcel(@Valid PricingRulePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<PricingRuleDO> list = ruleService.getRulePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代理商定价（终端零售价）规则.xls", "数据", PricingRuleRespVO.class,
                        BeanUtils.toBean(list, PricingRuleRespVO.class));
    }

}