package com.yunyi.express2b.module.agent.controller.admin.agentprofile.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 9:53
 */
@Schema(description = "管理后台 - 代理商档案分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AgentProfilePageReqVO extends PageParam {

    @Schema(description = "关联用户中心的用户ID（SSOUSERID）", example = "1691")
    private Long memberId;

    @Schema(description = "代理商名称/联系人", example = "张三")
    private String name;

    @Schema(description = "手机号，唯一，用于登录和联系")
    private String mobile;

    @Schema(description = "邮箱（可选）")
    private String email;

    @Schema(description = "状态（0-待审核, 1-正常, 2-冻结）对应AgentStatusEnum", example = "2")
    private Integer status;

    @Schema(description = "当前等级ID，关联agent_level_config.id", example = "20275")
    private Long levelId;

    @Schema(description = "推荐人代理商ID（上级）", example = "18141")
    private Long referrerAgentId;

    @Schema(description = "代理商专属自定义价格模板ID", example = "27560")
    private Long customPricingTemplateId;

    @Schema(description = "关联钱包模块的钱包ID", example = "3435")
    private String walletId;

    @Schema(description = "关联系统模块的租户ID", example = "6844")
    private Long systemTenantId;

    @Schema(description = "直接下级数量", example = "15584")
    private Integer directDownlineCount;

    @Schema(description = "团队总下级数量", example = "18731")
    private Integer totalDownlineCount;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}