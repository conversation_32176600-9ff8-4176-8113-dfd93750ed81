package com.yunyi.express2b.module.crm.service.cardstream;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.yunyi.express2b.module.crm.controller.admin.cardstream.vo.*;
import com.yunyi.express2b.module.crm.dal.dataobject.cardstream.CardStreamDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.crm.dal.mysql.cardstream.CardStreamMapper;
import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.crm.enums.ErrorCodeConstants.*;

/**
 * 卡流水 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CardStreamServiceImpl implements CardStreamService {

    @Resource
    private CardStreamMapper cardStreamMapper;

    /**
     * 创建卡流水
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Long createCardStream(CardStreamSaveReqVO createReqVO) {
        // 插入
        CardStreamDO cardStream = BeanUtils.toBean(createReqVO, CardStreamDO.class);
        cardStreamMapper.insert(cardStream);
        // 返回
        return cardStream.getId();
    }

    /**
     * 更新卡流水
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateCardStream(CardStreamSaveReqVO updateReqVO) {
        // 校验存在
        validateCardStreamExists(updateReqVO.getId());
        // 更新
        CardStreamDO updateObj = BeanUtils.toBean(updateReqVO, CardStreamDO.class);
        cardStreamMapper.updateById(updateObj);
    }

    /**
     * 删除卡流水
     * @param id 编号
     */
    @Override
    public void deleteCardStream(Long id) {
        // 校验存在
        validateCardStreamExists(id);
        // 删除
        cardStreamMapper.deleteById(id);
    }

    /**
     * 校验卡流水存在
     * @param id
     */
    private void validateCardStreamExists(Long id) {
        if (cardStreamMapper.selectById(id) == null) {
            throw exception(CARD_STREAM_NOT_EXISTS);
        }
    }

    /**
     * 获得卡流水
     * @param id 编号
     * @return
     */
    @Override
    public CardStreamDO getCardStream(Long id) {
        return cardStreamMapper.selectById(id);
    }

    /**
     * 获得卡流水分页
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<CardStreamDO> getCardStreamPage(CardStreamPageReqVO pageReqVO) {
        return cardStreamMapper.selectPage(pageReqVO);
    }

}