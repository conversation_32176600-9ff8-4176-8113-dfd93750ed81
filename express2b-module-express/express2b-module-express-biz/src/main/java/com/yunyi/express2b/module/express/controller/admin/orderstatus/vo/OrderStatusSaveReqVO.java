package com.yunyi.express2b.module.express.controller.admin.orderstatus.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单状态管理 订单状态管理新增/修改 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 订单状态管理新增/修改 Request VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderStatusSaveReqVO {

    @Schema(description = "状态ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3123")
    private Long id;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6611")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "订单状态-变更前", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "订单状态-变更前不能为空")
    private String statusStart;

    @Schema(description = "订单状态-变更后")
    private String statusEnd;

    @Schema(description = "操作人ID（系统或用户）", example = "27532")
    private Long operatorId;

    @Schema(description = "操作人姓名", example = "赵六")
    private String operatorName;

    @Schema(description = "备注（状态变更原因）", example = "你猜")
    private String remark;

}