
-- 张洋，增加支付单与订单的关联表
DROP TABLE IF EXISTS `express2b_rel_order_payment`;
CREATE TABLE `express2b_rel_order_payment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` bigint NOT NULL COMMENT '订单表ID',
  `payment_order_id` bigint NOT NULL COMMENT '支付订单表ID',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) COMMENT = '订单与支付订单关联表' ROW_FORMAT = Dynamic;

-- 杨浩
ALTER TABLE express2b_order
ADD COLUMN ucm_order_sn VARCHAR(255) NOT NULL
COMMENT '统一运力订单的唯一标识';