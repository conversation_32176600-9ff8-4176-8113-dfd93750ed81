package com.yunyi.express2b.module.crm.dal.dataobject.address;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 地址簿管理 DO
 *
 * <AUTHOR>
 */
@TableName("express2b_address")
@KeySequence("express2b_address_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddressDO extends BaseDO {

    /**
     * 地址ID
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long memberId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 电话
     */
    private String phone;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 是否默认地址（0: 否, 1: 是）
     */
    private Integer isDefault;
    /**
     * 省
     */
   // private Long provinceId;
    /**
     * 地址code
     */
    private String adccode;
    /**
     * 省code
     */
    private String provinceCode;
    /**
     * 市code
     */
    private String cityCode;
    /**
     * 区code
     */
    private String districtCode;
    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 区名称
     */
    private String districtName;

    /**
     * 排序
     */
    private String sort;
}