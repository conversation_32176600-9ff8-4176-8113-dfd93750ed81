package com.yunyi.express2b.module.agent.controller.admin.pagetemplate.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 页面模板分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PageTemplatePageReqVO extends PageParam {

    @Schema(description = "模块名称，如轮播、功能区等", example = "张三")
    private String moduleName;

    @Schema(description = "组件类型,按钮等", example = "2")
    private String componentType;

    @Schema(description = "展示名称", example = "赵六")
    private String displayName;

    @Schema(description = "0隐藏 1显示")
    private Integer state;

    @Schema(description = "对应dom名")
    private String dom;

    @Schema(description = "父模块的dom名，用于标识子模块所属父模块，若为顶级模块则为null")
    private String parentDom;

    @Schema(description = "子模块ID，若无子模块则为NULL", example = "13756")
    private String subModuleId;

    @Schema(description = "模块或子模块展示顺序")
    private Integer sortOrder;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}