package com.yunyi.express2b.module.express.dal.dataobject.orderaddress;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * 订单地址表
 * @TableName express2b_order_address
 */
@TableName(value ="express2b_order_address")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderAddressDO extends BaseDO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单ID
     */
    @TableField(value = "order_id")
    private Long orderId;

    /**
     * 寄件人省名
     */
    @TableField(value = "sender_province")
    private String senderProvince;

    /**
     * 寄件人市名
     */
    @TableField(value = "sender_city")
    private String senderCity;

    /**
     * 寄件人区名
     */
    @TableField(value = "sender_district")
    private String senderDistrict;

    /**
     * 寄件人行政区划代码
     */
    @TableField(value = "sender_adccode")
    private String senderAdccode;

    /**
     * 寄件人城市代码
     */
    @TableField(value = "sender_citycode")
    private String senderCitycode;

    /**
     * 收件人省名
     */
    @TableField(value = "receiver_province")
    private String receiverProvince;

    /**
     * 收件人市名
     */
    @TableField(value = "receiver_city")
    private String receiverCity;

    /**
     * 收件人区名
     */
    @TableField(value = "receiver_district")
    private String receiverDistrict;

    /**
     * 收件人行政区划代码
     */
    @TableField(value = "receiver_adccode")
    private String receiverAdccode;

    /**
     * 收件人城市代码
     */
    @TableField(value = "receiver_citycode")
    private String receiverCitycode;

    /**
     * 参考CommonStatusEnum
     */
    @TableField(value = "status")
    private Integer status;

}