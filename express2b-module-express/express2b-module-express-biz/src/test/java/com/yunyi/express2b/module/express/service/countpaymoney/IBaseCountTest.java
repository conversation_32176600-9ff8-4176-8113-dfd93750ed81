package com.yunyi.express2b.module.express.service.countpaymoney;

import com.yunyi.express2b.framework.common.util.json.JsonUtils;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.module.express.controller.admin.pricing.vo.PricingSaveReqVO;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.orderaddress.OrderAddressMapper;
import com.yunyi.express2b.module.express.dal.mysql.pricing.PricingMapper;
import com.yunyi.express2b.module.express.service.countpaymoney.decorator.BaseCountDecorator;
import com.yunyi.express2b.module.express.service.countpaymoney.decorator.LadderCountDecorator;
import com.yunyi.express2b.module.express.service.countpaymoney.decorator.SubCardCountDecorator;
import com.yunyi.express2b.module.express.service.countpaymoney.dto.OrderDetailDTO;
import com.yunyi.express2b.module.express.service.countpaymoney.factory.CountDecoratorFactory;
import com.yunyi.express2b.module.express.service.pricing.PricingService;
import com.yunyi.express2b.module.express.service.pricing.PricingServiceImpl;
import com.yunyi.express2b.module.infra.api.config.ConfigApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.time.LocalDate;

import static reactor.core.publisher.Mono.when;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/28 20:20
 */
@Slf4j
@Import({PricingServiceImpl.class,CountDecoratorFactory.class,OrderMapper.class})
class IBaseCountTest extends BaseDbUnitTest {
    IBaseCount baseCount = new BaseCountImpl();

    @Resource
    PricingServiceImpl pricingService;

    @Resource
    CountDecoratorFactory countDecoratorFactory;
    @Resource
    OrderMapper orderMapper;

    @MockitoBean
    private ConfigApi configApi;


    @BeforeEach
    void setUp() {
        Mockito.when(configApi.getConfigValueByKey("profit")).thenReturn("0.01");


    }

    @Test
    void countPayMoney() {
        //初始化一个订单详情
        OrderDetailDTO orderDetail = new OrderDetailDTO();
        orderDetail.setTodayCount(1);
        orderDetail.setMemberId(1L);
        orderDetail.setRetailAmount(1);
        orderDetail.setCostAmount(1);

        double i = baseCount.countPayMoney(orderDetail);
        log.debug(String.valueOf(i));
//
//        BaseCountDecorator baseCountDecorator = new LadderCountDecorator(baseCount);
//        baseCountDecorator.countPayMoney(orderDetail);
//        log.debug(JsonUtils.toJsonString(orderDetail));
//
//        BaseCountDecorator subCardCountDecorator = new SubCardCountDecorator(baseCount);
//        subCardCountDecorator.countPayMoney(orderDetail);
//        log.debug(JsonUtils.toJsonString(orderDetail));
    }

    @Test
    void countLadderPayMoney() {
        //初始化一个订单详情
        OrderDetailDTO orderDetail = new OrderDetailDTO();
        orderDetail.setTodayCount(10);
        orderDetail.setMemberId(1L);
        orderDetail.setRetailAmount(100);
        orderDetail.setCostAmount(50);

        PricingSaveReqVO pricingSaveReqVO = new PricingSaveReqVO();

        // 填充不能为空的字段
        pricingSaveReqVO.setPricingKey("uniquePricingKey");
        pricingSaveReqVO.setLineKey("lineKey123");
        pricingSaveReqVO.setProvincialDiscount(10);
        pricingSaveReqVO.setOutOfProvinceDiscount(10);
        pricingSaveReqVO.setBrandKey("1");
        pricingSaveReqVO.setMinQuantity(1);
        pricingSaveReqVO.setMaxQuantity(100);
        pricingSaveReqVO.setEffectiveDate(LocalDate.now());
        pricingSaveReqVO.setExpiryDate(LocalDate.now().plusYears(1));

        pricingService.createPricing(pricingSaveReqVO);

        LadderCountDecorator ladderDecorator = countDecoratorFactory.createLadderDecorator(new BaseCountImpl());

        Integer i = ladderDecorator.countPayMoney(orderDetail);

        log.info(orderDetail.getLadderDiscountAmount().toString());
        log.info("最终金额为"+i);
    }
}