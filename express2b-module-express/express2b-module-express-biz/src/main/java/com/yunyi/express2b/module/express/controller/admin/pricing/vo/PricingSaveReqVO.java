package com.yunyi.express2b.module.express.controller.admin.pricing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 阶梯价优惠新增/修改 Request VO")
@Data
public class PricingSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23655")
    private Long id;

    @Schema(description = "优惠唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "优惠唯一标识不能为空")
    private String pricingKey;

    @Schema(description = "关联线路标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "关联线路标识不能为空")
    private String lineKey;

    @Schema(description = "省内优惠金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "11353")
    @NotNull(message = "省内优惠金额不能为空")
    private Integer provincialDiscount;

    @Schema(description = "省外优惠金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "18244")
    @NotNull(message = "省外优惠金额不能为空")
    private Integer outOfProvinceDiscount;

    @Schema(description = "优惠说明", example = "你说的对")
    private String description;

    @Schema(description = "品牌ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "品牌ID不能为空")
    private String brandKey;

    @Schema(description = "品牌名称（冗余）", example = "李四")
    private String brandDisplayName;

    @Schema(description = "最小单量（含）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "最小单量（含）不能为空")
    private Integer minQuantity;

    @Schema(description = "最大单量（含）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "最大单量（含）不能为空")
    private Integer maxQuantity;

    @Schema(description = "生效日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "生效日期不能为空")
    private LocalDate effectiveDate;

    @Schema(description = "失效日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "失效日期不能为空")
    private LocalDate expiryDate;

}