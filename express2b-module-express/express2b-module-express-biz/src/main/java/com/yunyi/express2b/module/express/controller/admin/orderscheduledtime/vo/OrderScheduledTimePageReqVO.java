package com.yunyi.express2b.module.express.controller.admin.orderscheduledtime.vo;

import lombok.*;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
/**
 * 管理后台 订单预约取件时间分页 OrderScheduledTimePageReq VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 订单预约取件时间分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderScheduledTimePageReqVO extends PageParam {

    @Schema(description = "订单ID", example = "6714")
    private Long orderId;

    @Schema(description = "快递品牌标识")
    private String brandKey;

    @Schema(description = "预约取件日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] scheduledDate;

    @Schema(description = "预约取件时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalTime[] scheduledTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}