package com.yunyi.express2b.module.express.controller.admin.paymentorder.vo;

import com.yunyi.express2b.framework.common.validation.InEnum;
import com.yunyi.express2b.module.express.enums.PayMethordEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 支付订单新增/修改 Request VO")
@Data
public class PaymentOrderSaveReqVO {

    @Schema(description = "支付订单ID（主键）", requiredMode = Schema.RequiredMode.REQUIRED, example = "23222")
    private Long id;

    @Schema(description = "交易编号-系统生成")
    private String transactionNo;

    @Schema(description = "外部支付交易号")
    private String outTransactionNo;

    @Schema(description = "外部支付获取的外层交易号")
    private String outOutTransactionNo;

    @Schema(description = "订单表ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "29884")
    @NotNull(message = "订单表ID不能为空")
    private Long orderId;

    @Schema(description = "支付金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "支付金额不能为空")
    private Integer amount;

    @Schema(description = "支付状态（支付成功，转入退款，未支付，已关闭，已撤销，用户支付中，支付失败）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    @NotNull(message = "支付状态（支付成功，转入退款，未支付，已关闭，已撤销，用户支付中，支付失败）不能为空")
    private String status;

    @InEnum(PayMethordEnum.class)
    @Schema(description = "支付方式（支付宝，微信）")
    private String paymentMethod;

    @Schema(description = "备注")
    private String content;

    @Schema(description = "手续费")
    private Integer fee;

    @Schema(description = "支付编号")
    private String paymentNumber;

    @Schema(description = "支付提交日期")
    private LocalDateTime paymentSubmitTime;

    @Schema(description = "最后状态更新日期")
    private LocalDateTime lastStatusUpdateTime;

    @Schema(description = "最后退款日期")
    private LocalDateTime lastRefundTime;

    @Schema(description = "对账主键", example = "23171")
    private Long reconciliationId;

    @Schema(description = "对账时间")
    private LocalDateTime reconciliationTime;

    @Schema(description = "IP地址")
    private String ip;

    @Schema(description = "客户端id", example = "2543")
    private Long clientId;

    @Schema(description = "商品信息")
    private String body;

    @Schema(description = "关联personal_info的member_id，sso平台的统一登录ID", example = "17102")
    private Long memberId;

}