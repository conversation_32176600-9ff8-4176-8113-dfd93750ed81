package com.yunyi.express2b.module.crm.dal.mysql.carduser;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.module.crm.controller.app.v1.carduser.vo.CardUserPageReqVO;
import com.yunyi.express2b.module.crm.dal.dataobject.carduser.CardUserDO;
import com.yunyi.express2b.module.crm.enums.CardStatusEnum;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 持卡用户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CardUserMapper extends BaseMapperX<CardUserDO> {

    default PageResult<CardUserDO> selectPage(CardUserPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CardUserDO>()
                .eqIfPresent(CardUserDO::getPayOrderSn, reqVO.getPayOrderSn())
                .eqIfPresent(CardUserDO::getCardlimitedId, reqVO.getCardlimitedId())
                .eqIfPresent(CardUserDO::getCardNumber, reqVO.getCardNumber())
                .likeIfPresent(CardUserDO::getCardName, reqVO.getCardName())
                .eqIfPresent(CardUserDO::getPersonalId, reqVO.getPersonalId())
                .eqIfPresent(CardUserDO::getTotal, reqVO.getTotal())
                .eqIfPresent(CardUserDO::getRemaining, reqVO.getRemaining())
                .betweenIfPresent(CardUserDO::getCardStartDate, reqVO.getCardStartDate())
                .betweenIfPresent(CardUserDO::getCardEndDate, reqVO.getCardEndDate())
                .eqIfPresent(CardUserDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(CardUserDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(CardUserDO::getId));
    }

    /**
     * 根据个人身份ID选择卡用户列表
     *
     * @param personalId 个人身份ID，用于查询卡用户信息
     * @return 返回一个CardUserDO对象列表，包含与指定个人身份ID关联的所有卡用户信息
     */

    default List<CardUserDO> selectByPersonalId(Long personalId) {
        return selectList(new LambdaQueryWrapperX<CardUserDO>()
                .eqIfPresent(CardUserDO::getPersonalId, personalId)
                .eq(CardUserDO::getStatus, CardStatusEnum.ENABLE.getCode()));
    }

    /**
     * 根据个人ID和品牌ID选择卡用户
     *
     * @param personalId 个人ID，用于标识一个个人不能为空
     * @param brandId    品牌ID，用于标识一个品牌不能为空
     * @return 返回一个CardUserDO对象的列表，代表匹配查询条件的卡用户如果
     * 没有找到匹配的记录，列表将为空
     */
    List<CardUserDO> selectByPersonalIdAndBrandId(Long personalId, Long brandId);


    /**
     * 根据卡号查询用户信息列表
     *
     * @param cardNumber 卡号，
     * @return List<CardUserDO> 返回一个CardUserDO对象列表，如果查询不到结果，返回空列表
     */
//    default CardUserDO selectByCardNumber(String cardNumber){
//        return selectOne(new LambdaQueryWrapperX<CardUserDO>()
//                .eqIfPresent(CardUserDO::getCardNumber, cardNumber)
//                .eq(CardUserDO::getStatus, CardStatusEnum.ENABLE.getCode())
//        );
//
//    }
    default CardUserDO selectByCardNumber(String cardNumber) {
        return selectOne(new LambdaQueryWrapperX<CardUserDO>()
                .eq(CardUserDO::getCardNumber, cardNumber)
                .eq(CardUserDO::getStatus, CardStatusEnum.ENABLE.getCode()));
    }

    default CardUserDO selectpayOrderSn(String payOrderSn) {
        return selectOne(new LambdaQueryWrapperX<CardUserDO>()
                .eq(CardUserDO::getPayOrderSn, payOrderSn));
    }

    /**
     * 根据卡限制ID和剩余有效期选择用户列表
     * 此方法用于查询满足特定卡限制ID的用户，并按照卡有效期结束日期和剩余数量升序排序
     *
     * @param cardlimitedId 卡限制ID，用于筛选查询结果
     * @return 返回一个用户列表，包含满足条件的用户信息
     */
    default List<CardUserDO> selectByRemainAndTimeId(Long cardlimitedId) {
        return selectList(new LambdaQueryWrapperX<CardUserDO>()
                .eqIfPresent(CardUserDO::getCardlimitedId, cardlimitedId)
                .orderByAsc(CardUserDO::getCardEndDate)
                .orderByAsc(CardUserDO::getRemaining));
    }

    /**
     * 根据卡号更新用户的剩余次数
     *
     * @param remaining  剩余次数，表示用户在使用服务后应该更新的剩余次数
     * @param cardNumber 卡号，用于识别和定位特定的用户卡片信息
     */
    default int updateRemainingByCardNumber(Integer remaining, String cardNumber) {
        return update(new CardUserDO(), new LambdaUpdateWrapper<CardUserDO>()
                .eq(CardUserDO::getCardNumber, cardNumber)
                .eq(CardUserDO::getStatus, CardStatusEnum.ENABLE.getCode())
                .set(CardUserDO::getRemaining, remaining));

    }

    /**
     * 根据卡号更新用户状态为关闭
     *
     * @param cardNumber 用户卡号，用于唯一标识一个用户
     */
    default void updateStateByCardNumber(String cardNumber, Integer status) {
        update(new CardUserDO(), new LambdaUpdateWrapper<CardUserDO>()
                .eq(CardUserDO::getCardNumber, cardNumber)
                .eq(CardUserDO::getStatus, CardStatusEnum.ENABLE.getCode())
                .set(CardUserDO::getStatus, status));
    }

    /**
     * 根据支付单号更新用户状态为开启
     *
     * @param payOrderSn 用户卡号，用于唯一标识一个用户
     */
    default int updatepayOrderSn(String payOrderSn, Integer status) {
        return update(new CardUserDO(), new LambdaUpdateWrapper<CardUserDO>()
                .eq(CardUserDO::getPayOrderSn, payOrderSn)
                .set(CardUserDO::getStatus, status));
    }


    /**
     * 更新所有符合条件的CardUserDO实体的状态为2
     */
    default void updateAllStatusToTwo(LocalDateTime now) {
        update(new CardUserDO(), new LambdaUpdateWrapper<CardUserDO>()
                .eq(CardUserDO::getStatus, CardStatusEnum.ENABLE.getCode())
                .lt(CardUserDO::getCardEndDate, now)
                .set(CardUserDO::getStatus, CardStatusEnum.DELETE.getCode()));
    }

    /**
     * 根据卡号更新用户状态为删除
     * 当卡号匹配且当前状态为启用时，将用户状态更新为删除
     *
     * @param cardNumber 用户卡号，用于唯一标识一个用户
     * @return 更新影响的行数
     */
    default long updateStatusByCardNumber(String cardNumber) {
        return update(new CardUserDO(), new LambdaUpdateWrapper<CardUserDO>()
                .eq(CardUserDO::getCardNumber, cardNumber)
                .eq(CardUserDO::getStatus, CardStatusEnum.ENABLE.getCode())
                .set(CardUserDO::getStatus, CardStatusEnum.DISABLE.getCode()));

    }


}