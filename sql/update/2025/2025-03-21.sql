-- 增加订单字段历史记录表
CREATE TABLE `order_field_history` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID', -- 唯一标识每条历史记录
  `order_id` bigint NOT NULL COMMENT '订单ID', -- 关联的订单ID
  `field_name` varchar(100) NOT NULL COMMENT '变更的字段名', -- 发生变更的字段名称
  `old_value` text COMMENT '旧值', -- 变更前的字段值
  `new_value` text COMMENT '新值', -- 变更后的字段值
  `change_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间', -- 变更发生的时间
  `operation_type` varchar(10) NOT NULL COMMENT '操作类型（UPDATE/INSERT/DELETE）', -- 操作类型
  `operator` varchar(64) DEFAULT NULL COMMENT '操作人', -- 执行操作的用户
  `creator` varchar(64) DEFAULT '' COMMENT '创建者', -- 记录创建者
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', -- 记录创建时间
  `updater` varchar(64) DEFAULT '' COMMENT '更新者', -- 记录更新者
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', -- 记录更新时间
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除', -- 是否已删除
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号', -- 租户编号
  PRIMARY KEY (`id`) USING BTREE -- 主键
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='订单字段变更历史记录表';

-- 订单表增加订单编号表
ALTER TABLE `express2b_order` ADD COLUMN `order_no` VARCHAR(50) NOT NULL COMMENT '订单编号';

