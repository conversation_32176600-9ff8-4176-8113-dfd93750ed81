package com.yunyi.express2b.module.express.controller.app.v1.order.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/3 13:23
 */



@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AddressParseVO {
    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 区县编码
     */
    private String countyCode;

    /**
     * 完整地址
     */
    private String address;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 详细地址
     */
    private String detail_address;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 区县
     */
    private String county;

    /**
     * 批量订单表id
     */
    private Integer batchId;

    private String excelUrl;
}
