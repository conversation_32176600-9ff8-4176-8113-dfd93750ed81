package com.yunyi.express2b.module.crm.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * CardNumberUtil
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/16 上午9:11
 */

public class CardNumberUtil {
    public static String generateCardNumber() {
        // 生成时间戳
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String timestamp = sdf.format(new Date());

        // 生成随机数
        Random random = new Random();
        int randomNum = random.nextInt(9000) + 1000; // 生成 4 位随机数

        // 组合时间戳和随机数作为卡号
        String cardNumber = timestamp + randomNum;

        return cardNumber;
    }


}
