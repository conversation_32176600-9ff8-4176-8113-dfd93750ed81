package com.yunyi.express2b.module.agent.dal.dataobject.relationship;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 代理商与定价模版关联表
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/22 下午1:10
 */
@TableName("pricing_template_agent_relation")
@Data
public class TemplateAgentRelationDoApi {

    @TableId(type = IdType.AUTO)
    private Long id;

    @NotEmpty(message = "模版ID")
    private Long templateId;

    @NotEmpty(message = "代理商ID")
    private Long agentId;


}