package com.yunyi.express2b.module.express.job.job;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.quartz.core.handler.JobHandler;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.framework.tenant.core.aop.TenantIgnore;
import com.yunyi.express2b.module.express.api.message.dto.OrderChangeDTO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.products.ProductsMapper;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.express.enums.OrderStatusTypeEnum;
import com.yunyi.express2b.module.express.service.message.SmsService;
import com.yunyi.express2b.module.express.service.sendmessage.MessageUtils;
import com.yunyi.express2b.module.express.utils.GenerateOrderNumberUtil;
import com.yunyi.framework.api.login.api.message.MessageApi;
import com.yunyi.framework.api.login.api.message.vo.MessageRequest;
import com.yunyi.framework.api.login.api.message.vo.MessageResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/16 16:58
 */
@Slf4j
@Component
public class SelectUnPaidOrderJob implements JobHandler {

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private MessageUtils messageutils;



    @Override
    @TenantIgnore
    public String execute(String param) throws Exception {
        log.info("【开始执行查询未付款的定时任务】----------------");
        //查询已支付的订单集合
        List<OrderDO> orderDOList = orderMapper.selectUnPaidOrders(List.of(OrderStatusTypeEnum.CREATED.getStatus(),
                OrderStatusTypeEnum.PENDING_ADDITIONAL_FEE.getStatus()));
        log.info("【查询未支付的订单集合】----------------{}",orderDOList);
        for (OrderDO orderDO : orderDOList) {
            messageutils.sendGoodsStatusUpdateMessage(orderDO);
        }
        return String.format("定时任务执行成功");
    }






}
