package com.yunyi.express2b.module.commission.controller.admin.detail;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;

import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.*;

import com.yunyi.express2b.module.commission.controller.admin.detail.vo.*;
import com.yunyi.express2b.module.commission.dal.dataobject.detail.DetailDO;
import com.yunyi.express2b.module.commission.service.detail.DetailService;

@Tag(name = "管理后台 - 分润明细")
@RestController
@RequestMapping("/commission/detail")
@Validated
public class DetailController {

    @Resource
    private DetailService detailService;

    /**
     * 我可以查询分润明细，按时间、来源、下级等维度统计
     * @param pageReqVO
     * @return
     */
    @GetMapping("/commission-details")
    @Operation(summary = "我可以查询分润明细，按时间、来源、下级等维度统计")
    public CommonResult<PageResult<DetailPageReqVO>> getcommissiondetails(@Valid DetailPageReqVO pageReqVO) {
        PageResult<DetailDO> pageResult = detailService.getcommissiondetails(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DetailPageReqVO.class));
    }
    @PostMapping ("/commission-insert")
    @Operation(summary = "我可以为后续分润计算保存分润明细，记录来源、金额、关联订单、上下级关系等")
    public  CommonResult<Long> insertcommission(@Valid DetailSaveReqVO detailSaveReqVO){

        return success(detailService.createDetail(detailSaveReqVO));
    }


    @PostMapping("/create")
    @Operation(summary = "创建分润明细")
    @PreAuthorize("@ss.hasPermission('commission:detail:create')")
    public CommonResult<Long> createDetail(@Valid @RequestBody DetailSaveReqVO createReqVO) {
        return success(detailService.createDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新分润明细")
    @PreAuthorize("@ss.hasPermission('commission:detail:update')")
    public CommonResult<Boolean> updateDetail(@Valid @RequestBody DetailSaveReqVO updateReqVO) {
        detailService.updateDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除分润明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('commission:detail:delete')")
    public CommonResult<Boolean> deleteDetail(@RequestParam("id") Long id) {
        detailService.deleteDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得分润明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('commission:detail:query')")
    public CommonResult<DetailRespVO> getDetail(@RequestParam("id") Long id) {
        DetailDO detail = detailService.getDetail(id);
        return success(BeanUtils.toBean(detail, DetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得分润明细分页")
    @PreAuthorize("@ss.hasPermission('commission:detail:query')")
    public CommonResult<PageResult<DetailRespVO>> getDetailPage(@Valid DetailPageReqVO pageReqVO) {
        PageResult<DetailDO> pageResult = detailService.getDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DetailRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出分润明细 Excel")
    @PreAuthorize("@ss.hasPermission('commission:detail:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDetailExcel(@Valid DetailPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<DetailDO> list = detailService.getDetailPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "分润明细.xls", "数据", DetailRespVO.class,
                        BeanUtils.toBean(list, DetailRespVO.class));
    }

}