package com.yunyi.express2b.module.wallet.service.transactions;

import jakarta.validation.*;
import com.yunyi.express2b.module.wallet.controller.admin.transactions.vo.*;
import com.yunyi.express2b.module.wallet.dal.dataobject.transactions.TransactionsDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;

import java.util.List;

/**
 * 钱包流水 Service 接口
 *
 * <AUTHOR>
 */
public interface TransactionsService {

    /**
     * 创建钱包流水
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTransactions(@Valid TransactionsSaveReqVO createReqVO);

    /**
     * 更新钱包流水
     *
     * @param updateReqVO 更新信息
     */
    void updateTransactions(@Valid TransactionsSaveReqVO updateReqVO);

    /**
     * 删除钱包流水
     *
     * @param id 编号
     */
    void deleteTransactions(Long id);

    /**
     * 获得钱包流水
     *
     * @param id 编号
     * @return 钱包流水
     */
    TransactionsDO getTransactions(Long id);

    /**
     * 获得钱包流水分页
     *
     * @param pageReqVO 分页查询
     * @return 钱包流水分页
     */
    PageResult<TransactionsDO> getTransactionsPage(TransactionsPageReqVO pageReqVO);


    TransactionsDO getTransactionsDetailsByAgentId(Long agentId);

    TransactionsDO getFreezeBalance(Long agentId);

    void updateFreezeBalance();
}