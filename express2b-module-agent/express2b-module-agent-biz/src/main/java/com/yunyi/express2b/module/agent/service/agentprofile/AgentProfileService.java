package com.yunyi.express2b.module.agent.service.agentprofile;

import java.io.File;
import java.util.*;

import com.yunyi.express2b.module.agent.api.dto.RegisterDTO;
import com.yunyi.express2b.module.agent.controller.admin.workbench.vo.AgentTotalVo;
import jakarta.validation.*;
import com.yunyi.express2b.module.agent.controller.admin.agentprofile.vo.*;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.AgentProfileDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;

/**
 * 代理商档案 Service 接口
 *
 * <AUTHOR>
 */
public interface AgentProfileService {

    /**
     * 创建代理商档案
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProfile(@Valid AgentProfileSaveReqVO createReqVO);

    /**
     * 更新代理商档案
     *
     * @param updateReqVO 更新信息
     */
    void updateProfile(@Valid AgentProfileSaveReqVO updateReqVO);

    /**
     * 删除代理商档案
     *
     * @param id 编号
     */
    void deleteProfile(Long id);

    /**
     * 获得代理商档案
     *
     * @param id 编号
     * @return 代理商档案
     */
    AgentProfileDO getProfile(Long id);

    /**
     * 获得代理商档案分页
     *
     * @param pageReqVO 分页查询
     * @return 代理商档案分页
     */
    PageResult<AgentProfileDO> getProfilePage(AgentProfilePageReqVO pageReqVO);

    /**
     * 根据二维码/链接邀请信息更改代理商档案信息
     * @param agentId
     * @param templateId
     */
    void editAgentProfile( Long agentId,Long templateId);

    /**
     * 通过父团队信息查询所有子团队成员信息
     * @return
     */
    List<AgentProfileDO> getTeam();

    /**
     * 通过member_id查询代理商档案
     * @return
     */
    AgentProfileRespVO getProfileByMemberId(Long memberId,Long templateId);


    /**
     * 根据用户关联中心id注册
     * @param registerDTO
     */
    void registerProfile(RegisterDTO registerDTO);

    /**
     * 保存图片url
     * @param file
     */
    void saveImageUrl(File file) throws Exception;

}