## 创建订单
POST {{appApi}}/v1/express/order/create
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

{
  "createReqVO": {
    "brandId": 1,
    "senderName": "刘佳",
    "senderPhone": "13056481234",
    "senderAddress": "小桥大街44号",
    "senderAddressId": 9508,
    "expressCode": "YTO",
    "receivedAmount": 3600,
    "batchId": "",
    "useSubCard": 0,
    "productCode": "YTO_BK",
    "appointmentTime": "2025-05-30",
    "scheduledTime": "08:00-10:00"
  },
  "recipientVOs": [
    {
      "receiverName": "李先生",
      "receiverPhone": "15521548796",
      "receiverAddress": "同方广场A座",
      "receiverAddressId": 9565,
      "itemName": "示例商品",
      "itemCategory": "电子产品",
      "itemQuantity": 1,
      "itemWeight": 2,
      "isInsured": false
    },
    {
      "receiverName": "王先生",
      "receiverPhone": "15524852145",
      "receiverAddress": "测试地址",
      "receiverAddressId": 9567,
      "itemName": "示例商品",
      "itemCategory": "电子产品",
      "itemQuantity": 1,
      "itemWeight": 2,
      "isInsured": false
    }
  ]
}

### 创建多个寄件人和多个收件人的订单
POST {{appApi}}/v1/express/order/create-orders
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

[
  {
    "createReqVO": {
      "brandId": 1,
      "senderName": "刘佳",
      "senderPhone": "13056481234",
      "senderAddress": "小桥大街44号",
      "senderAddressId": 9508,
      "expressCode": "YTO",
      "receivedAmount": 3600,
      "batchId": "",
      "useSubCard": 0,
      "productCode": "YTO_BK",
      "appointmentTime": "2025-05-30",
      "scheduledTime": "08:00-10:00"
    },
    "recipientVOs": [
      {
        "receiverName": "孙先生",
        "receiverPhone": "15521548796",
        "receiverAddress": "同方广场A座",
        "receiverAddressId": 9565,
        "itemName": "示例商品",
        "itemCategory": "电子产品",
        "itemQuantity": 1,
        "itemWeight": 2,
        "isInsured": false
      },
      {
        "receiverName": "王先生",
        "receiverPhone": "15524852145",
        "receiverAddress": "测试地址",
        "receiverAddressId": 9567,
        "itemName": "示例商品",
        "itemCategory": "电子产品",
        "itemQuantity": 1,
        "itemWeight": 2,
        "isInsured": false
      }
    ]
  },
  {
    "createReqVO": {
      "brandId": 1,
      "senderName": "刘佳",
      "senderPhone": "13056481234",
      "senderAddress": "小桥大街44号",
      "senderAddressId": 9508,
      "expressCode": "YTO",
      "receivedAmount": 3600,
      "batchId": "",
      "useSubCard": 0,
      "productCode": "YTO_BK",
      "appointmentTime": "2025-05-30",
      "scheduledTime": "08:00-10:00"
    },
    "recipientVOs": [
      {
        "receiverName": "李先生",
        "receiverPhone": "15521548796",
        "receiverAddress": "同方广场A座",
        "receiverAddressId": 9565,
        "itemName": "示例商品",
        "itemCategory": "电子产品",
        "itemQuantity": 1,
        "itemWeight": 2,
        "isInsured": false
      },
      {
        "receiverName": "王先生",
        "receiverPhone": "15524852145",
        "receiverAddress": "测试地址",
        "receiverAddressId": 9567,
        "itemName": "示例商品",
        "itemCategory": "电子产品",
        "itemQuantity": 1,
        "itemWeight": 2,
        "isInsured": false
      }
    ]
  }
]


### 获取订单
GET {{appApi}}/v1/express/order/get?id=931
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

### 更新订单
PUT {{appApi}}/v1/express/order/update
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

{
  "id": 874,
  "orderNo": "1234567890",
  "batchId": 32377,
  "personalId": 18936,
  "brandId": 8521,
  "pricingKey": "pricingKeyExample",
  "profitKey": "profitKeyExample",
  "status": "CREATED",
  "senderName": "赵六",
  "senderPhone": "13800000000",
  "senderAddress": "北京市海淀区XX街道XX号",
  "senderAddressId": 5494,
  "receiverName": "李四",
  "receiverPhone": "13900000000",
  "receiverAddress": "上海市浦东新区XX街道XX号",
  "receiverAddressId": 7106,
  "costAmount": 100,
  "retailAmount": 150,
  "receivedAmount": 150,
  "discountAmount": 10,
  "agentCommission": 20,
  "groupLeaderCommission": 30,
  "trackingNumber": "TRACK123456",
  "itemName": "李四的物品",
  "itemCategory": "电子产品",
  "itemQuantity": 2,
  "itemWeight": 5,
  "itemValue": 200,
  "packagingDetails": "纸箱包装",
  "isInsured": true,
  "insuredAmount": 50.0,
  "receiverStaffName": "张三",
  "payType": "1",
  "memberIp": "***********",
  "type": 2,
  "cardId": "card123456",
  "height": 30,
  "width": 40,
  "length": 50,
  "scheduledStartTime": "10:00",
  "scheduledEndTime": "12:00",
  "ucmOrderSn": "ucmOrder12345",
  "appointmentTime": "2025-05-30",
  "scheduledTime": "2025-05-30 10:00-12:00",
  "expressCode": "EXP123",
  "productCode": "PROD123",
  "cardNumber": "123456789",
  "useSubCard": "0",
  "receiverStaffMobile": "13911112222",
  "receiverStaffPickupCode": "pickupCode123",
  "agentId": 789,
  "receiverPlacesList": [
    {
      "receiverCity": "沈阳市",
      "receiverProvince": "辽宁省",
      "senderCity": "沈阳市",
      "senderProvince": "辽宁省",
      "receiverDistrict": "和平区",
      "receiverAddress": "沈阳站",
      "weight": 2
    },
    {
      "receiverCity": "吉林市",
      "receiverProvince": "吉林省",
      "senderCity": "沈阳市",
      "senderProvince": "辽宁省",
      "receiverDistrict": "丰满区",
      "receiverAddress": "丰满区",
      "weight": 3
    }
  ],
  "receiverCity": "上海市",
  "receiverCityCode": "310000",
  "receiverDistrict": "浦东新区",
  "receiverDistrictCode": "310115",
  "receiverProvince": "上海",
  "receiverProvinceCode": "31",
  "senderDistrictCode": "110108",
  "senderDistrict": "海淀区",
  "senderProvince": "北京市",
  "senderProvinceCode": "11",
  "senderCityCode": 110000,
  "senderCity": "北京市"
}

###取消订单
POST {{appApi}}/v1/express/order/cancel-order
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

{
  "orderNo": "EA25060387707842010",
  "ucmOrderSn": "",
  "refundReason": "其他"
}

###查看订单详情
GET {{appApi}}/v1/express/order/get?id=874
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

###根据订单状态获得订单管理分页
GET {{appApi}}/v1/express/order/status-pages?status=CANCELED_AFTER_PAYMENT
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

###Excel上传并同步SSO服务器
POST {{appApi}}/v1/express/order/status-pages?status=CANCELED_AFTER_PAYMENT
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}


###手动添加地址进行识别
POST {{appApi}}/v1/express/order/content-parse
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

{
  "address": "杨浩15523456789辽宁省沈阳市铁西区建设东路22号沈阳科技大厦5楼\n"
}


### 查询物流信息
GET {{appApi}}/v1/express/order/tracking?trackingNumber=YT2526368850268&id=757
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

### 根据已支付/待支付状态以及时间段查询订单金额
GET {{appApi}}/v1/express/order/order-status-pay?startTime=2025-04-30&endTime=2025-05-30
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}


### 根据已支付/待支付状态以及时间段查询订单金额
GET {{appApi}}/v1/express/order/orders-list?startTime=2025-04-30&endTime=2025-05-30
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}


### 查询未支付订单列表
GET {{appApi}}/v1/express/order/status-pay
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}


### 查询服务统计数据（发单量、已到达、待付款、进行中、已取消）订单数量
GET {{appApi}}/v1/express/order/orders-count
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}


### 查询今日累计订单量
GET {{appApi}}/v1/express/order/today-count
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

### 查询未付款的订单数量
GET {{appApi}}/v1/express/order/unpaid-order-count
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

###询价
POST {{appApi}}/v1/express/pricing/express
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{appToken}}

{
  "useSubCard": 0,
  "senderCity": "沈阳市",
  "senderProvince": "辽宁省",
  "senderAddress": "和平区",
  "senderDistrict": "沈阳三好街",
  "receiverPlaces": [
    {
      "receiverCity": "沈阳市",
      "receiverProvince": "辽宁省",
      "senderCity": "沈阳市",
      "senderProvince": "辽宁省",
      "receiverDistrict": "和平区",
      "receiverAddress": "沈阳站",
      "weight": 2
    },
    {
      "receiverCity": "吉林市",
      "receiverProvince": "吉林省",
      "senderCity": "沈阳市",
      "senderProvince": "辽宁省",
      "receiverDistrict": "丰满区",
      "receiverAddress": "丰满区",
      "weight": 3
    }
  ]
}

### 运力下单回调
POST {{baseUrl}}/express/order/order-callback
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{token}}

{
  "data": {
    "status": "101",
    "courierName": "唐丽",
    "expressSn": "JT0016790297801",
    "ucmOrderSn": "D20250526194208784670",
    "courierMobile": "17741387993",
    "weight": "4",
    "defPrice": "15.0",
    "freight": "1600",
    "volume": "120",
    "actualWeight": "2.2",
    "feeDetails": [
      {
        "feeType": "PACKAGINGFEE",
        "feeDesc": "包装费",
        "amount": "0.8",
        "payStatus": -1
      },
      {
        "feeType": "INSURANCEFEE",
        "feeDesc": "保价费",
        "amount": "1.8",
        "payStatus": 2
      },
      {
        "feeType": "DELIVERYSERVICEFEE",
        "feeDesc": "配送服务",
        "amount": "1.8",
        "payStatus": 0
      }
    ],
    "printTaskId": "PRINT_001",
    "imgBase64": "iVBORw0KGgoAAAANSUhEUg..."
  }
}

###客服
POST {{appApi}}/v1/express/order/online-customer
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{token}}

{
  "client": 16
}

###生成海报
POST {{appApi}}/v1/express/order/download-poster
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{token}}

###生成小程序
POST {{appApi}}/v1/express/order/generate-applets
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{token}}

###创建二维码
POST {{appApi}}/v1/express/order/create-qr-code
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{token}}

###更新用户信息
POST {{appApi}}/v1/express/order/update-personal
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{token}}

{
  "userName": "张三",
  "mobile": "***********",
  "icon": "https://example.com/icons/zhangsan.png",
  "ssoUserId": ********,
  "version": "1.0.0"
}

###获取微信关注公众号链接
POST {{appApi}}/v1/express/order/official-account-link
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{token}}


###公众号关注/取关推送通知回调接口
POST {{baseUrl}}/express/order/concern
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{token}}

{
  "ssoUserId": ********,
  "status": 0
}

###开通平台代理
POST {{appApi}}/v1/express/order/open-platform-agent
Content-Type: application/json
tenant-id: {{appTenantId}}
Authorization: Bearer {{token}}

