package com.yunyi.express2b.module.express.utils;

import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @date 2025-03-24 19:06:56
 */
@Configuration
public class PayNoGenerator {
    public static String generatePaymentNo(String prefix) {
        // 1. 获取当前时间并格式化到毫秒
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        String datePart = now.format(formatter);

        // 2. 生成3位随机数（000-999）
        int randomNum = ThreadLocalRandom.current().nextInt(1000);
        String randomPart = String.format("%03d", randomNum);

        // 3. 拼接完整订单号
        return prefix + datePart + randomPart;
    }

}
