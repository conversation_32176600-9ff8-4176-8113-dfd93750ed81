package com.yunyi.express2b.module.express.service.locations;

import java.util.*;
import jakarta.validation.*;
import com.yunyi.express2b.module.express.controller.admin.locations.vo.*;
import com.yunyi.express2b.module.express.dal.dataobject.locations.LocationsDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;

/**
 * 全国行政区信息管理 Service 接口
 *
 * <AUTHOR>
 */
public interface LocationsService {

    /**
     * 创建全国行政区信息管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLocations(@Valid LocationsSaveReqVO createReqVO);

    /**
     * 更新全国行政区信息管理
     *
     * @param updateReqVO 更新信息
     */
    void updateLocations(@Valid LocationsSaveReqVO updateReqVO);

    /**
     * 删除全国行政区信息管理
     *
     * @param id 编号
     */
    void deleteLocations(Long id);

    /**
     * 获得全国行政区信息管理
     *
     * @param parentCode 编号
     * @return 全国行政区信息管理
     */
    List<LocationsDO> getLocations( String parentCode );

    /**
     * 获得全国行政区信息管理分页
     *
     * @param pageReqVO 分页查询
     * @return 全国行政区信息管理分页
     */
    PageResult<LocationsDO> getLocationsPage(LocationsPageReqVO pageReqVO);

    /**
     * 排序全国行政区信息管理
     *
     * @param locations 排序信息
     * @return 排序结果
     */
    LocationsDO sort(LocationsDO locations);
}