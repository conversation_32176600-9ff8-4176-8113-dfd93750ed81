package com.yunyi.express2b.module.agent.controller.admin.levelconfig.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 9:53
 */
@Schema(description = "管理后台 - 代理商等级配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LevelConfigPageReqVO extends PageParam {

    @Schema(description = "等级名称（如V1,V2,V3）", example = "李四")
    private String levelName;

    @Schema(description = "此等级默认关联的平台价格模板ID", example = "23614")
    private Long defaultPlatformPricingTemplateId;

    @Schema(description = "升级到此等级所需个人连续3日日均订单量阈值")
    private Integer upgradePersonalDailyOrders;

    @Schema(description = "升级到此等级所需团队连续3日日均订单量阈值")
    private Integer upgradeTeamDailyOrders;

    @Schema(description = "业绩观察天数")
    private Integer upgradeObservationDays;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}