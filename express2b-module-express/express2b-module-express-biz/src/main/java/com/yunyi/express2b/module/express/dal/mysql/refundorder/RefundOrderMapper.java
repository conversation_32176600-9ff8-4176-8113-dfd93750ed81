package com.yunyi.express2b.module.express.dal.mysql.refundorder;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.date.LocalDateTimeUtils;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.module.express.controller.admin.refundorder.vo.RefundOrderPageReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.refundorder.RefundOrderDO;
import com.yunyi.express2b.module.express.enums.RefundStatusTypeEnum;
import org.apache.ibatis.annotations.Mapper;

import java.time.Duration;
import java.util.List;

/**
 * 退款单管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RefundOrderMapper extends BaseMapperX<RefundOrderDO> {

    default PageResult<RefundOrderDO> selectPage(RefundOrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RefundOrderDO>()
                .eqIfPresent(RefundOrderDO::getPaymentOrderId, reqVO.getPaymentOrderId())
                .eqIfPresent(RefundOrderDO::getAmount, reqVO.getAmount())
                .eqIfPresent(RefundOrderDO::getRefundOrderSn, reqVO.getRefundOrderSn())
                .eqIfPresent(RefundOrderDO::getRefundReason, reqVO.getRefundReason())
                .eqIfPresent(RefundOrderDO::getRefundMethod, reqVO.getRefundMethod())
                .eqIfPresent(RefundOrderDO::getRefundStatus, reqVO.getRefundStatus())
                .betweenIfPresent(RefundOrderDO::getRecentRefundApplyTime, reqVO.getRecentRefundApplyTime())
                .betweenIfPresent(RefundOrderDO::getRefundSubmitTime, reqVO.getRefundSubmitTime())
                .betweenIfPresent(RefundOrderDO::getRefundStatusUpdateTime, reqVO.getRefundStatusUpdateTime())
                .betweenIfPresent(RefundOrderDO::getRefundCompleteTime, reqVO.getRefundCompleteTime())
                .eqIfPresent(RefundOrderDO::getReconciliationId, reqVO.getReconciliationId())
                .betweenIfPresent(RefundOrderDO::getReconciliationTime, reqVO.getReconciliationTime())
                .betweenIfPresent(RefundOrderDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(RefundOrderDO::getId));
    }

    /**
     * 通过支付单id列表查询退款中和退款成功的退款单列表
     */
    default List<RefundOrderDO> selectListByPaymentIds(List<Long> longs) {
        return selectList(new LambdaQueryWrapper<RefundOrderDO>()
                .in(RefundOrderDO::getPaymentOrderId, longs)
                .in(RefundOrderDO::getRefundStatus, RefundStatusTypeEnum.PROCESSING.getCode(), RefundStatusTypeEnum.REFUNDED.getCode()));
    }

    /**
     * 获取全部支付状态为退款中而且退款超时的退款单列表
     */
    default List<RefundOrderDO> selectTimeoutRefundOrders(Duration outTimeDuration) {
        return selectList(new LambdaQueryWrapper<RefundOrderDO>()
                .eq(RefundOrderDO::getRefundStatus, RefundStatusTypeEnum.PROCESSING.getCode())
                .le(RefundOrderDO::getRefundSubmitTime, LocalDateTimeUtils.minusTime(outTimeDuration)));
    }

    /**
     * 根据退款单号更新退款单
     */
    default void updateByOrderNo(RefundOrderDO refundOrder) {

    }

    default RefundOrderDO selectByRefundOrderSn(String refundOrderSn){
        return selectOne(new LambdaQueryWrapper<RefundOrderDO>()
                .eq(RefundOrderDO::getRefundOrderSn, refundOrderSn));
    }

    default int updateRefundOrderByRefundOrderSn(String refundOrderSn, String status){
        return update(new RefundOrderDO().setRefundStatus(status), new LambdaQueryWrapper<RefundOrderDO>()
                .eq(RefundOrderDO::getRefundOrderSn, refundOrderSn));
    }

    default RefundOrderDO selectRefundOrderByOrderId(Long id){
        return selectOne(new LambdaQueryWrapper<RefundOrderDO>()
                .eq(RefundOrderDO::getPaymentOrderId, id));
    }

    //   OrderDO selectOrderByPaymentOrderId(Long paymentOrderId);
    /**
     * 根据id和payment_order_id查询退款单
     */
    default RefundOrderDO selectByIdAndPaymentOrderId( Long id,Long paymentOrderId) {
        return selectOne(new LambdaQueryWrapper<RefundOrderDO>()
                .eq(RefundOrderDO::getPaymentOrderId, paymentOrderId)
                .eq(RefundOrderDO::getId, id));
    }

    default List<RefundOrderDO> selectListByRefundStatus(String code){
        return selectList(new LambdaQueryWrapper<RefundOrderDO>()
                .eq(RefundOrderDO::getRefundStatus, code)
                .orderByDesc(RefundOrderDO::getRefundSubmitTime)
        );
    }
}