package com.yunyi.express2b.module.agent.controller.admin.dailyperformancestat.vo;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
/**
 * 代理商日业绩统计分页VO实体
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 * @since 2025/5/15 17:56
 */
@Schema(description = "管理后台 - 代理商日业绩统计分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DailyPerformanceStatPageReqVO extends PageParam {

    @Schema(description = "代理商ID", example = "27505")
    private Long agentId;

    @Schema(description = "统计日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] statDate;

    @Schema(description = "当日个人完成订单数", example = "2209")
    private Integer personalOrderCount;

    @Schema(description = "当日团队完成订单总数", example = "27203")
    private Integer teamOrderCount;

    @Schema(description = "当日个人订单总金额（终端销售价，单位：分）")
    private Integer personalOrderAmount;

    @Schema(description = "当日团队订单总金额（单位：分）")
    private Integer teamOrderAmount;

    @Schema(description = "当日个人利润（终端销售价-平台零售价-红包，单位：分）")
    private Integer personalProfitAmount;

    @Schema(description = "当日团队利润（下级订单带来的利润，单位：分）")
    private Integer teamProfitAmount;

    @Schema(description = "当日获得的分润收入（单位：分）")
    private Integer commissionIncomeAmount;

    @Schema(description = "当日坏账订单数", example = "10292")
    private Integer badDebtCount;

    @Schema(description = "当日坏账总金额（单位：分）")
    private Integer badDebtAmount;

    @Schema(description = "当日红包核销金额（单位：分）")
    private Integer couponDeductAmount;

    @Schema(description = "当日平台补贴金额（如有，单位：分）")
    private Integer subsidyAmount;

    @Schema(description = "当日提现金额（如有，单位：分）")
    private Integer withdrawalAmount;

    @Schema(description = "当日新增客户数", example = "1507")
    private Integer newCustomerCount;

    @Schema(description = "当日活跃客户数", example = "17927")
    private Integer activeCustomerCount;

    @Schema(description = "当日复购客户数", example = "13167")
    private Integer repeatCustomerCount;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}