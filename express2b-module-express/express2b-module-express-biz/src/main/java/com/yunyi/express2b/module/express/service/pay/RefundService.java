package com.yunyi.express2b.module.express.service.pay;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.module.express.controller.admin.refundorder.vo.RefundOrderPageReqVO;
import com.yunyi.express2b.module.express.controller.admin.refundorder.vo.RefundOrderSaveReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.refundorder.RefundOrderDO;
import jakarta.validation.Valid;

/**
 * 退款单管理 Service 接口
 *
 * <AUTHOR>
 */
public interface RefundService {

    /**
     * 重新发起退款
     *
     * @param createReqVO
     * @return
     */
    Boolean refund(RefundOrderSaveReqVO createReqVO);

    /**
     * 更新退款单管理
     *
     * @param updateReqVO 更新信息
     */
    void updateRefundOrder(@Valid RefundOrderSaveReqVO updateReqVO);

    /**
     * 删除退款单管理
     *
     * @param id 编号
     */
    void deleteRefundOrder(Long id);

    /**
     * 获得退款单管理
     *
     * @param id 编号
     * @return 退款单管理
     */
    RefundOrderDO getRefundOrder(Long id);

    /**
     * 获得退款单管理分页
     *
     * @param pageReqVO 分页查询
     * @return 退款单管理分页
     */
    PageResult<RefundOrderDO> getRefundOrderPage(RefundOrderPageReqVO pageReqVO);

    /**
     * 创建退款单
     *
     * @param saveReqVO 创建信息
     * @return 编号
     */
    Long createRefundOrderInfo(RefundOrderSaveReqVO saveReqVO);

    /**
     * 校验退款单是否可以退款
     */
    Boolean validateRefundOrder(RefundOrderDO refundOrderDO);

    /**
     * 检查待退款订单，进行退款。返回退款成功的数量。
     * 这个方法会查询数据库中的所有状态是APPLIED的退款单，然后进行退款。
     */
    int checkRefundOrder();
}