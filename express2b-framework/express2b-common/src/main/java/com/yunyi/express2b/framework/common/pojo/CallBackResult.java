package com.yunyi.express2b.framework.common.pojo;

import cn.hutool.core.bean.BeanUtil;
import lombok.Data;

/**
 * 回调接口返回（加status字段）
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/15 下午2:06
 */
@Data
public class CallBackResult<T> extends CommonResult<T>{
    /**
     * 处理结果页面输出 Result 对象
     * status = 1 即处理成功 否则按失败处理
     */
    private Integer status;

    public static <T> CallBackResult<T> success(T data) {
        CommonResult<T> commonResult = CommonResult.success(data);
        CallBackResult<T> result = BeanUtil.toBean(commonResult, CallBackResult.class);
        //result.status = 1;
        return result;
    }
}
