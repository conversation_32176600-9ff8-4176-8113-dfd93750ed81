package com.yunyi.express2b.module.crm.controller.admin.carduser.vo;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
/**
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 持卡用户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CardUserPageReqVO extends PageParam {

    @Schema(description = "卡种id", example = "8566")
    private Long cardlimitedId;

    @Schema(description = "卡编号")
    private String cardNumber;

    @Schema(description = "卡名-冗余字段", example = "张三")
    private String cardName;

    @Schema(description = "持有用户id", example = "9674")
    private Long personalId;

    @Schema(description = "购买时的总次数")
    private Integer total;

    @Schema(description = "剩余可用次数")
    private Integer remaining;

    @Schema(description = "生效日期（包含）")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] cardStartDate;

    @Schema(description = "结束日期（当日可用）")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] cardEndDate;

    @Schema(description = "启用状态 0-可用 1-不可用", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}