package com.yunyi.express2b.module.wallet.controller.admin.transactions.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 钱包流水 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TransactionsRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8215")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "钱包ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10144")
    @ExcelProperty("钱包ID")
    private Long walletId;

    @Schema(description = "代理商用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15490")
    @ExcelProperty("代理商用户ID")
    private Long agentId;

    @Schema(description = "统一登陆平台的订单支付编号")
    @ExcelProperty("统一登陆平台的订单支付编号")
    private String payOrderSn;

    @Schema(description = "平台唯一交易流水号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30384")
    @ExcelProperty("平台唯一交易流水号")
    private String transactionId;

    @Schema(description = "交易类型 (具体取值见字典说明)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("交易类型 (具体取值见字典说明)")
    private String transactionType;

    @Schema(description = "交易金额 (正为收入,负为支出) (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易金额 (正为收入,负为支出) (单位: 分)")
    private Integer amount;

    @Schema(description = "交易前账户总余额 (可用+冻结) (单位: 分)")
    @ExcelProperty("交易前账户总余额 (可用+冻结) (单位: 分)")
    private Integer balanceBeforeTransaction;

    @Schema(description = "交易后账户总余额 (可用+冻结) (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("交易后账户总余额 (可用+冻结) (单位: 分)")
    private Integer balanceAfterTransaction;

    @Schema(description = "交易后可用余额 (单位: 分)")
    @ExcelProperty("交易后可用余额 (单位: 分)")
    private Integer availableBalanceAfterTransaction;

    @Schema(description = "交易后冻结余额 (单位: 分)")
    @ExcelProperty("交易后冻结余额 (单位: 分)")
    private Integer frozenBalanceAfterTransaction;

    @Schema(description = "关联订单号", example = "24563")
    @ExcelProperty("关联订单号")
    private String relatedOrderId;

    @Schema(description = "关联提现申请ID", example = "8490")
    @ExcelProperty("关联提现申请ID")
    private Long relatedWithdrawalId;

    @Schema(description = "关联风险事件ID或其他业务ID", example = "13849")
    @ExcelProperty("关联风险事件ID或其他业务ID")
    private String relatedEventId;

    @Schema(description = "流水状态 (具体取值见字典说明)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("流水状态 (具体取值见字典说明)")
    private String status;

    @Schema(description = "交易描述", example = "你猜")
    @ExcelProperty("交易描述")
    private String description;

    @Schema(description = "资金预计可用时间 (解冻时间)")
    @ExcelProperty("资金预计可用时间 (解冻时间)")
    private LocalDateTime availableAt;

    @Schema(description = "备注信息 (如操作人,调账原因)", example = "你猜")
    @ExcelProperty("备注信息 (如操作人,调账原因)")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}