package com.yunyi.express2b.module.agent.api.dto;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 注册
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 * @since 2025/5/20 16:21
 */
@Data
public class RegisterDTO extends PageParam {
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 密码
     */
    private String password;
    /**
     * 账号(注册时的默认用户名)
     */
    private String username;

    @NotNull(message = "租户状态")
    private Integer status;

    private String contactName;

    @NotNull(message = "租户套餐编号不能为空")
    private Long packageId;

    @NotNull(message = "过期时间不能为空")
    private LocalDateTime expireTime;

    @NotNull(message = "账号数量不能为空")
    private Integer accountCount;

}
