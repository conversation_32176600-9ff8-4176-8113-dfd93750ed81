-- 创建代理商模块数据表
-- 创建表agent_custom_template_request
DROP TABLE IF EXISTS `agent_custom_template_request`;
CREATE TABLE `agent_custom_template_request` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `agent_id` BIGINT NOT NULL COMMENT '申请代理商ID',
  `requested_template_details` TEXT NOT NULL COMMENT '申请的模板参数（JSON格式）',
  `status` TINYINT NOT NULL COMMENT '申请状态（0-待审核, 1-已批准, 2-已拒绝）',
  `approved_template_id` BIGINT COMMENT '若批准，关联到pricing模块中为此申请创建的模板ID',
  `requested_at` DATETIME NOT NULL COMMENT '申请时间',
  `reviewed_at` DATETIME COMMENT '审核时间',
  `reviewer_comments` VARCHAR(500) COMMENT '审核备注',
  `remarks` VARCHAR(500) NOT NULL COMMENT '申请备注',
  `creator` VARCHAR(64) NOT NULL COMMENT '创建者ID',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NOT NULL COMMENT '更新者ID',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标记',
  `tenant_id` BIGINT NOT NULL COMMENT '租户编号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理商自定义价格模板申请记录表';

-- 创建索引
CREATE INDEX idx_agent_id ON agent_custom_template_request(agent_id);
CREATE INDEX idx_status ON agent_custom_template_request(status);

-- 创建表agent_relationship
DROP TABLE IF EXISTS `agent_relationship`;
CREATE TABLE `agent_relationship` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `ancestor_id` BIGINT NOT NULL COMMENT '上级代理商ID',
  `descendant_id` BIGINT NOT NULL COMMENT '下级代理商ID',
  `depth` INT NOT NULL DEFAULT 0 COMMENT '层级深度',
  UNIQUE INDEX `uk_ancestor_descendant` (`ancestor_id`, `descendant_id`),
  FOREIGN KEY (ancestor_id) REFERENCES agent_agent_profile(id),
  FOREIGN KEY (descendant_id) REFERENCES agent_agent_profile(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理商团队关系表';

-- 创建索引
CREATE INDEX idx_ancestor_id ON agent_relationship(ancestor_id);
CREATE INDEX idx_descendant_id ON agent_relationship(descendant_id);


-- 创建表agent_daily_performance_stat
DROP TABLE IF EXISTS `agent_daily_performance_stat`;
CREATE TABLE `agent_daily_performance_stat` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `agent_id` BIGINT NOT NULL COMMENT '代理商ID',
  `stat_date` DATE NOT NULL COMMENT '统计日期',
  `personal_order_count` INT NOT NULL DEFAULT 0 COMMENT '当日个人完成订单数',
  `team_order_count` INT NOT NULL DEFAULT 0 COMMENT '当日团队完成订单总数',
  `personal_order_amount` INT NOT NULL DEFAULT 0 COMMENT '当日个人订单总金额（终端销售价，单位：分）',
  `team_order_amount` INT NOT NULL DEFAULT 0 COMMENT '当日团队订单总金额（单位：分）',
  `personal_profit_amount` INT NOT NULL DEFAULT 0 COMMENT '当日个人利润（终端销售价-平台零售价-红包，单位：分）',
  `team_profit_amount` INT NOT NULL DEFAULT 0 COMMENT '当日团队利润（下级订单带来的利润，单位：分）',
  `commission_income_amount` INT NOT NULL DEFAULT 0 COMMENT '当日获得的分润收入（单位：分）',
  `bad_debt_count` INT NOT NULL DEFAULT 0 COMMENT '当日坏账订单数',
  `bad_debt_amount` INT NOT NULL DEFAULT 0 COMMENT '当日坏账总金额（被扣除的金额，单位：分）',
  `coupon_deduct_amount` INT NOT NULL DEFAULT 0 COMMENT '当日红包核销总金额（代理商承担，单位：分）',
  `subsidy_amount` INT NOT NULL DEFAULT 0 COMMENT '当日平台补贴金额（如有，单位：分）',
  `withdrawal_amount` INT NOT NULL DEFAULT 0 COMMENT '当日提现金额（如有，单位：分）',
  `new_customer_count` INT NOT NULL DEFAULT 0 COMMENT '当日新增客户数',
  `active_customer_count` INT NOT NULL DEFAULT 0 COMMENT '当日活跃客户数',
  `repeat_customer_count` INT NOT NULL DEFAULT 0 COMMENT '当日复购客户数',
  `creator` VARCHAR(64) NOT NULL COMMENT '创建者ID',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NOT NULL COMMENT '更新者ID',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标记',
  `tenant_id` BIGINT NOT NULL COMMENT '租户编号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理商日业绩统计表';

-- 创建索引
CREATE INDEX idx_agent_id ON agent_daily_performance_stat(agent_id);
CREATE INDEX idx_stat_date ON agent_daily_performance_stat(stat_date);
CREATE INDEX idx_agent_daily_performance_stat_agent_date ON agent_daily_performance_stat(agent_id, stat_date);

-- 创建表agent_agent_profile
DROP TABLE IF EXISTS `agent_agent_profile`;
CREATE TABLE `agent_agent_profile` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '代理商唯一标识',
  `member_id` BIGINT NOT NULL COMMENT '关联用户中心的用户ID（SSOUSERID）',
  `name` VARCHAR(100) NOT NULL COMMENT '代理商名称/联系人',
  `mobile` VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号，唯一，用于登录和联系',
  `email` VARCHAR(100) COMMENT '邮箱（可选）',
  `status` TINYINT NOT NULL COMMENT '状态（0-待审核, 1-正常, 2-冻结）对应AgentStatusEnum',
  `level_id` BIGINT NOT NULL COMMENT '当前等级ID，关联agent_level_config.id',
  `referrer_agent_id` BIGINT COMMENT '推荐人代理商ID（上级）',
  `custom_pricing_template_id` BIGINT COMMENT '代理商专属自定义价格模板ID',
  `wallet_id` VARCHAR(64) NOT NULL COMMENT '关联钱包模块的钱包ID',
  `system_tenant_id` BIGINT NOT NULL COMMENT '关联系统模块的租户ID',
  `direct_downline_count` INT NOT NULL DEFAULT 0 COMMENT '直接下级数量',
  `total_downline_count` INT NOT NULL DEFAULT 0 COMMENT '团队总下级数量',
  `creator` VARCHAR(64) NOT NULL COMMENT '创建者ID',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NOT NULL COMMENT '更新者ID',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标记',
  `tenant_id` BIGINT NOT NULL COMMENT '租户编号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理商档案表';

-- 创建表agent_level_config
DROP TABLE IF EXISTS `agent_level_config`;
CREATE TABLE `agent_level_config` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '等级配置ID',
  `level_name` VARCHAR(50) NOT NULL COMMENT '等级名称（如"V1","V2","V3"）',
  `default_platform_pricing_template_id` BIGINT NOT NULL COMMENT '此等级默认关联的平台价格模板ID',
  `upgrade_personal_daily_orders` INT NOT NULL COMMENT '升级到此等级所需个人连续3日日均订单量阈值',
  `upgrade_team_daily_orders` INT NOT NULL COMMENT '升级到此等级所需团队连续3日日均订单量阈值',
  `upgrade_observation_days` INT NOT NULL COMMENT '业绩观察天数',
  `creator` VARCHAR(64) NOT NULL COMMENT '创建者ID',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NOT NULL COMMENT '更新者ID',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标记',
  `tenant_id` BIGINT NOT NULL COMMENT '租户编号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理商等级配置表';

-- 创建表agent_selected_pricing_template
DROP TABLE IF EXISTS `agent_selected_pricing_template`;
CREATE TABLE `agent_selected_pricing_template` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `superior_agent_id` BIGINT NOT NULL COMMENT '操作选择的上级代理商ID',
  `downline_agent_id` BIGINT NOT NULL COMMENT '被指定模板的下级代理商ID',
  `pricing_template_id` BIGINT NOT NULL COMMENT '选择的价格模板ID',
  `creator` VARCHAR(64) NOT NULL COMMENT '创建者ID',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NOT NULL COMMENT '更新者ID',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标记',
  `tenant_id` BIGINT NOT NULL COMMENT '租户编号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理商为下级选择的价格模板记录表';

-- 创建表agent_custom_template_request
CREATE TABLE `agent_custom_template_request` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `agent_id` BIGINT NOT NULL COMMENT '申请代理商ID',
  `requested_template_details` TEXT NOT NULL COMMENT '申请的模板参数（JSON格式）',
  `status` TINYINT NOT NULL COMMENT '申请状态（0-待审核, 1-已批准, 2-已拒绝）',
  `approved_template_id` BIGINT COMMENT '若批准，关联到pricing模块中为此申请创建的模板ID',
  `requested_at` DATETIME NOT NULL COMMENT '申请时间',
  `reviewed_at` DATETIME COMMENT '审核时间',
  `reviewer_comments` VARCHAR(500) COMMENT '审核备注',
  `remarks` VARCHAR(500) NOT NULL COMMENT '申请备注',
  `creator` VARCHAR(64) NOT NULL COMMENT '创建者ID',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NOT NULL COMMENT '更新者ID',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标记',
  `tenant_id` BIGINT NOT NULL COMMENT '租户编号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理商自定义价格模板申请记录表';

-- 创建表agent_daily_performance_stat
CREATE TABLE `agent_daily_performance_stat` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
  `agent_id` BIGINT NOT NULL COMMENT '代理商ID',
  `stat_date` DATE NOT NULL COMMENT '统计日期',
  `personal_order_count` INT NOT NULL DEFAULT 0 COMMENT '当日个人完成订单数',
  `team_order_count` INT NOT NULL DEFAULT 0 COMMENT '当日团队完成订单总数',
  `personal_order_amount` INT NOT NULL DEFAULT 0 COMMENT '当日个人订单总金额（终端销售价，单位：分）',
  `team_order_amount` INT NOT NULL DEFAULT 0 COMMENT '当日团队订单总金额（单位：分）',
  `personal_profit_amount` INT NOT NULL DEFAULT 0 COMMENT '当日个人利润（终端销售价-平台零售价-红包，单位：分）',
  `team_profit_amount` INT NOT NULL DEFAULT 0 COMMENT '当日团队利润（下级订单带来的利润，单位：分）',
  `commission_income_amount` INT NOT NULL DEFAULT 0 COMMENT '当日获得的分润收入（单位：分）',
  `bad_debt_count` INT NOT NULL DEFAULT 0 COMMENT '当日坏账订单数',
  `bad_debt_amount` INT NOT NULL DEFAULT 0 COMMENT '当日坏账总金额（单位：分）',
  `coupon_deduct_amount` INT NOT NULL DEFAULT 0 COMMENT '当日红包核销金额（单位：分）',
  `subsidy_amount` INT NOT NULL DEFAULT 0 COMMENT '当日平台补贴金额（如有，单位：分）',
  `withdrawal_amount` INT NOT NULL DEFAULT 0 COMMENT '当日提现金额（如有，单位：分）',
  `new_customer_count` INT NOT NULL DEFAULT 0 COMMENT '当日新增客户数',
  `active_customer_count` INT NOT NULL DEFAULT 0 COMMENT '当日活跃客户数',
  `repeat_customer_count` INT NOT NULL DEFAULT 0 COMMENT '当日复购客户数',
  `creator` VARCHAR(64) NOT NULL COMMENT '创建者ID',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) NOT NULL COMMENT '更新者ID',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除标记',
  `tenant_id` BIGINT NOT NULL COMMENT '租户编号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理商日业绩统计表';

-- 创建索引
CREATE INDEX idx_agent_id ON agent_daily_performance_stat(agent_id);
CREATE INDEX idx_stat_date ON agent_daily_performance_stat(stat_date);
CREATE INDEX idx_agent_daily_performance_stat_agent_date ON agent_daily_performance_stat(agent_id, stat_date);