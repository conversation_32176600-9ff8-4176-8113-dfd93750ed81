package com.yunyi.express2b.module.pricing.api;

import com.yunyi.express2b.module.pricing.api.vo.PricingRuleSaveReqVO;
import com.yunyi.express2b.module.pricing.api.vo.RouteSaveReqVO;
import com.yunyi.express2b.module.pricing.api.vo.SelectedPricingTemplateSaveReqVO;
import com.yunyi.express2b.module.pricing.api.vo.TemplateSaveReqVO;
import jakarta.validation.Valid;

/**
 * 定价模块 API 接口
 *
 * <AUTHOR>
 */
public interface PricingApiflow {
    
    /**
     * 计算价格
     *
     * @param fromCityId 出发城市编号
     * @param toCityId 目的城市编号
     * @param weight 重量，单位克
     * @return 价格，单位分
     */
    // Long calculatePrice(Long fromCityId, Long toCityId, Integer weight);

    /**
     * 创建快递全国运价基础
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRoute(@Valid RouteSaveReqVO createReqVO);

    /**
     * 创建代理商定价（终端零售价）规则
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRule(@Valid PricingRuleSaveReqVO createReqVO);
    /**
     * 创建代理商为下级选择的价格模板记录
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSelectedPricingTemplate(@Valid SelectedPricingTemplateSaveReqVO createReqVO);
    /**
     * 创建定价模板
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTemplate(@Valid TemplateSaveReqVO createReqVO);

    TemplateSaveReqVO statusTemplate(Long id);


    /**
     * 插入关系表中的代理商id和模板id关系
     * @param agentId
     * @param templateId
     * @return
     */
    Long insertTemplateRelationship(Long agentId,Long templateId);
} 