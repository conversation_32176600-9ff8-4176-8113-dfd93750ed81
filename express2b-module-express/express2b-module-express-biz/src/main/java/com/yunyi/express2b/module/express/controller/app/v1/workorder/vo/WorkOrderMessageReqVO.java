package com.yunyi.express2b.module.express.controller.app.v1.workorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "用户 APP - 工单修改 Request VO")
@Data
public class WorkOrderMessageReqVO {

    @Schema(description = "工单id", requiredMode = Schema.RequiredMode.REQUIRED, example = "10042")
    @NotNull
    private Long id;

    @Schema(description = "问题描述")
    @NotEmpty
    private String passengerQuestion;
}