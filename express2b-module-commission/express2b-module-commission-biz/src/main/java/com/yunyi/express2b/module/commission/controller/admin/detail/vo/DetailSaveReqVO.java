package com.yunyi.express2b.module.commission.controller.admin.detail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 分润明细新增/修改 Request VO")
@Data
public class DetailSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "26017")
    private Long id;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "29090")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "订单编号不能为空")
    private String orderNo;

    @Schema(description = "分润目标ID（代理商ID）", requiredMode = Schema.RequiredMode.REQUIRED, example = "11732")
    @NotNull(message = "分润目标ID（代理商ID）不能为空")
    private Long targetId;

    @Schema(description = "分润目标类型（2:V2代理商,3:V3代理商）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "分润目标类型（2:V2代理商,3:V3代理商）不能为空")
    private Integer targetType;

    @Schema(description = "分润金额（单位：分）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "分润金额（单位：分）不能为空")
    private Integer amount;

    @Schema(description = "状态 (1:未结算, 2:已结算, 3:已取消)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态 (1:未结算, 2:已结算, 3:已取消)不能为空")
    private Integer status;

    @Schema(description = "取消原因", example = "不对")
    private String reason;

    @Schema(description = "计算时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "计算时间不能为空")
    private LocalDateTime calculateTime;

    @Schema(description = "起始时间")
    private LocalDateTime startTime;
    @Schema(description = "终止时间")
    private LocalDateTime endTime;

}