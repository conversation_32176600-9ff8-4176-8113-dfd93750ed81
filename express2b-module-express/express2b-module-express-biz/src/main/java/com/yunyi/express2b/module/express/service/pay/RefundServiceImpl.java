package com.yunyi.express2b.module.express.service.pay;

import cn.hutool.core.util.ObjectUtil;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.crm.api.card.refundcard.RefundCardApi;
import com.yunyi.express2b.module.express.controller.admin.refundorder.vo.RefundOrderPageReqVO;
import com.yunyi.express2b.module.express.controller.admin.refundorder.vo.RefundOrderSaveReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.paymentorder.PaymentOrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.refundorder.RefundOrderDO;
import com.yunyi.express2b.module.express.dal.mysql.paymentorder.PaymentOrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.refundorder.RefundOrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.relorderpayment.RelOrderPaymentMapper;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.express.service.paymentorder.PaymentOrderService;
import com.yunyi.express2b.module.express.utils.PayNoGenerator;
import com.yunyi.framework.api.base.config.PaymentConfig;
import com.yunyi.framework.api.base.enums.CommonCodeConstants;
import com.yunyi.framework.api.login.api.payment.PaymentGatewayApi;
import com.yunyi.framework.api.login.api.payment.vo.RefundOrderRequest;
import com.yunyi.framework.api.login.api.payment.vo.RefundOrderResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.REFUND_ORDER_NOT_EXISTS;
import static com.yunyi.express2b.module.express.enums.RefundStatusTypeEnum.*;

/**
 * 退款单管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class RefundServiceImpl implements RefundService {

    private final String REFUND_ORDER_PREFIX = "TD";

    @Resource
    private RefundOrderMapper refundOrderMapper;

    @Resource
    private PaymentOrderMapper paymentOrderMapper;

    @Resource
    private PaymentGatewayApi paymentGatewayApi;

    @Resource
    private PaymentConfig paymentConfig;

    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 退款单幂等性的redis的key
     */
    private final String KEY_NAME = "refund.tag";

    @Override
    public Boolean refund(RefundOrderSaveReqVO refundOrderSaveReqVO) {
        //2. 查询所需参数（根据id查refund_order_sn，根据paymentOrderId查transaction_no）
        PaymentOrderDO paymentOrderDO = paymentOrderMapper.selectById(refundOrderSaveReqVO.getPaymentOrderId());
        if (ObjectUtil.isEmpty(paymentOrderDO)) {
            throw exception(ErrorCodeConstants.PAYMENT_ORDER_NOT_EXISTS);
        }
        //3. 构建参数
        RefundOrderRequest refundOrderRequest = new RefundOrderRequest();
        refundOrderRequest.setRefundOrderSn(refundOrderSaveReqVO.getRefundOrderSn());
        refundOrderRequest.setPayOrderSn(paymentOrderDO.getTransactionNo());
        refundOrderRequest.setAmount(refundOrderSaveReqVO.getAmount());
        refundOrderRequest.setNotifyUrl(paymentConfig.getRefundNotifyUrl());
        // 4. 调用退款接口
        return refund(refundOrderRequest);
    }

    /**
     * 退款
     *
     * @param refundOrderRequest
     * @return
     */
    private Boolean refund(RefundOrderRequest refundOrderRequest) {
        //5. 检查退款单是否存在
        RefundOrderDO refundOrderDO = refundOrderMapper.selectByRefundOrderSn(refundOrderRequest.getRefundOrderSn());
        if (ObjectUtil.isEmpty(refundOrderDO)) {
            throw exception(ErrorCodeConstants.REFUND_ORDER_NOT_EXISTS);//发起退款失败
        }
        if (!validateRefundOrder(refundOrderDO)) {
            // 校验失败，取消退款
            throw exception(ErrorCodeConstants.REQUEST_REFUND_FAIL);
        }
        //6. 执行退款
        CommonResult<RefundOrderResponse> refundOrderResponseCommonResult = paymentGatewayApi.refundOrder(refundOrderRequest);
        //7. 更新数据库退款单信息
        Long timestamp = System.currentTimeMillis(); // 时间戳：1720965645000
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
        //最近退款申请时间 now()
        refundOrderDO.setRecentRefundApplyTime(localDateTime);
        //退款状态最后更新时间 now()
        refundOrderDO.setRefundStatusUpdateTime(localDateTime);
        //更新者(搁置)
        Integer code = refundOrderResponseCommonResult.getCode();
        if (CommonCodeConstants.SUCCESS.equals(code)) {//退款申请-成功
            //退款状态 -> 退款中
            refundOrderDO.setRefundStatus(PROCESSING.getCode());
            //保存数据到数据库
            refundOrderMapper.updateById(refundOrderDO);
            //清空幂等
            removeRedisKey(refundOrderDO);
            return Boolean.TRUE;
        } else {//退款申请-失败
            //退款状态 -> 失败
            refundOrderDO.setRefundStatus(FAILED.getCode());
            //保存数据到数据库
            refundOrderMapper.updateById(refundOrderDO);
            //清空幂等
            removeRedisKey(refundOrderDO);
            return Boolean.FALSE;
        }
    }

    /**
     * 更新退款单
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateRefundOrder(RefundOrderSaveReqVO updateReqVO) {
        // 校验存在
        validateRefundOrderExists(updateReqVO.getId());
        // 更新
        RefundOrderDO updateObj = BeanUtils.toBean(updateReqVO, RefundOrderDO.class);
        refundOrderMapper.updateById(updateObj);
    }

    /**
     * 删除退款单
     * @param id 编号
     */
    @Override
    public void deleteRefundOrder(Long id) {
        // 校验存在
        validateRefundOrderExists(id);
        // 删除
        refundOrderMapper.deleteById(id);
    }

    /**
     * 校验退款单是否存在
     * @param id
     */
    private void validateRefundOrderExists(Long id) {
        if (refundOrderMapper.selectById(id) == null) {
            throw exception(REFUND_ORDER_NOT_EXISTS);
        }
    }

    /**
     * 获得退款单
     * @param id 编号
     * @return
     */
    @Override
    public RefundOrderDO getRefundOrder(Long id) {
        return refundOrderMapper.selectById(id);
    }

    /**
     * 获得退款单分页
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<RefundOrderDO> getRefundOrderPage(RefundOrderPageReqVO pageReqVO) {
        return refundOrderMapper.selectPage(pageReqVO);
    }

    /**
     * 创建退款单
     * @param saveReqVO 创建信息
     * @return
     */
    @Override
    public Long createRefundOrderInfo(RefundOrderSaveReqVO saveReqVO) {
        log.info("【开始创建退款单】----------：{}", saveReqVO);
        RefundOrderSaveReqVO refundOrderSaveReqVO = new RefundOrderSaveReqVO();
        refundOrderSaveReqVO.setPaymentOrderId(saveReqVO.getPaymentOrderId());
        refundOrderSaveReqVO.setAmount(saveReqVO.getAmount());
        //目前先写死
        refundOrderSaveReqVO.setRefundReason("取消");
        refundOrderSaveReqVO.setRefundOrderSn(PayNoGenerator.generatePaymentNo(REFUND_ORDER_PREFIX));
        // 设置退款时间为当前系统时间
        refundOrderSaveReqVO.setRecentRefundApplyTime(LocalDateTime.now());
        refundOrderSaveReqVO.setRefundStatus(APPLIED.getCode());
        refundOrderSaveReqVO.setRefundSubmitTime(LocalDateTime.now());
        RefundOrderDO refundOrder = BeanUtils.toBean(refundOrderSaveReqVO, RefundOrderDO.class);
        refundOrderMapper.insert(refundOrder);
        log.info("【创建退款单结束】----------{}", refundOrder);
        return refundOrder.getId();
    }

    /**
     * 验证退款单
     * @param refundOrderDO
     * @return
     */
    @Override
    public Boolean validateRefundOrder(RefundOrderDO refundOrderDO) {
        //1. 创建幂等 RefundOrderRequest对象无法获取订单创建时间
        String idempotentKey = String.format("refund:%s:%s:%s:%s:%s",
                refundOrderDO.getPaymentOrderId(),
                refundOrderDO.getRefundOrderSn(),
                refundOrderDO.getAmount(),
                refundOrderDO.getRefundMethod(),
                refundOrderDO.getRefundSubmitTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        );
        //2. 检查幂等唯一性
        Boolean isExist = redisTemplate.opsForSet().isMember(KEY_NAME, idempotentKey); // true
        //3. 不唯一抛异常
        if (isExist) {
            throw exception(ErrorCodeConstants.REQUEST_REFUND_FAIL); //发起退款失败
        }
        //4. 加入幂等
        addRedisKey(refundOrderDO);
        // 退款单不存在
        return Boolean.TRUE;
    }

    /**
     * 生成幂等 value
     * @param refundOrderDO
     * @return
     */
    private String getRedisKey(RefundOrderDO refundOrderDO) {
        return String.format("refund:%s:%s:%s:%s:%s",
                refundOrderDO.getPaymentOrderId(),
                refundOrderDO.getRefundOrderSn(),
                refundOrderDO.getAmount(),
                refundOrderDO.getRefundMethod(),
                refundOrderDO.getRefundSubmitTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        );
    }

    /**
     * 添加幂等到redis
     * @param refundOrderDO
     */
    private void addRedisKey(RefundOrderDO refundOrderDO) {
        String idempotentKey = getRedisKey(refundOrderDO);
        redisTemplate.opsForSet().add(KEY_NAME, idempotentKey);
    }

    /**
     * 从redis中删除幂等
     * @param refundOrderDO
     */
    private void removeRedisKey(RefundOrderDO refundOrderDO){
        String idempotentKey = getRedisKey(refundOrderDO);
        redisTemplate.opsForSet().remove(KEY_NAME, idempotentKey);
    }

    @Override
    public int checkRefundOrder() {
        // 1. 查询状态为APPLIED的退款订单
        log.info("【开始检查待退款订单】");
        List<RefundOrderDO> appliedRefundOrders = refundOrderMapper.selectListByRefundStatus(APPLIED.getCode());
        
        if (appliedRefundOrders.isEmpty()) {
            log.info("【检查完成】没有找到待退款的订单");
            return 0;
        }
        
        log.info("【找到待退款订单】数量: {}", appliedRefundOrders.size());
        
        // 2. 遍历处理每个待退款订单
        int successCount = 0;
        for (RefundOrderDO refundOrder : appliedRefundOrders) {
            try {
                // 将退款单转换为退款请求参数
                RefundOrderSaveReqVO refundReqVO = new RefundOrderSaveReqVO();
                refundReqVO.setRefundOrderSn(refundOrder.getRefundOrderSn());
                refundReqVO.setPaymentOrderId(refundOrder.getPaymentOrderId());
                refundReqVO.setAmount(refundOrder.getAmount());
                
                // 调用退款方法
                Boolean result = refund(refundReqVO);
                if (result) {
                    successCount++;
                    log.info("【退款成功】退款单号: {}", refundOrder.getRefundOrderSn());
                } else {
                    log.error("【退款失败】退款单号: {}", refundOrder.getRefundOrderSn());
                }
            } catch (Exception e) {
                refundOrderMapper.updateRefundOrderByRefundOrderSn(refundOrder.getRefundOrderSn(), FAILED.getCode());
                log.error("【退款处理异常】退款单号: {}, 异常信息: {}", refundOrder.getRefundOrderSn(), e.getMessage(), e);
            }
        }
        
        log.info("【退款处理完成】成功处理退款订单数量: {}", successCount);
        return successCount;
    }
}