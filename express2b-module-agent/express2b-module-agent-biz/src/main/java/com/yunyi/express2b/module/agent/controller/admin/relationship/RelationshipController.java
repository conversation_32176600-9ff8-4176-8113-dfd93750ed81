package com.yunyi.express2b.module.agent.controller.admin.relationship;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;

import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.*;

import com.yunyi.express2b.module.agent.controller.admin.relationship.vo.*;
import com.yunyi.express2b.module.agent.dal.dataobject.relationship.RelationshipDO;
import com.yunyi.express2b.module.agent.service.relationship.RelationshipService;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 9:53
 */
@Tag(name = "管理后台 - 代理商团队关系")
@RestController
@RequestMapping("/agent/relationship")
@Validated
public class RelationshipController {

    @Resource
    private RelationshipService relationshipService;

    @PostMapping("/create")
    @Operation(summary = "创建代理商团队关系")
    @PreAuthorize("@ss.hasPermission('agent:relationship:create')")
    public CommonResult<Long> createRelationship(@Valid @RequestBody RelationshipSaveReqVO createReqVO) {
        return success(relationshipService.createRelationship(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代理商团队关系")
    @PreAuthorize("@ss.hasPermission('agent:relationship:update')")
    public CommonResult<Boolean> updateRelationship(@Valid @RequestBody RelationshipSaveReqVO updateReqVO) {
        relationshipService.updateRelationship(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代理商团队关系")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:relationship:delete')")
    public CommonResult<Boolean> deleteRelationship(@RequestParam("id") Long id) {
        relationshipService.deleteRelationship(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代理商团队关系")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:relationship:query')")
    public CommonResult<RelationshipRespVO> getRelationship(@RequestParam("id") Long id) {
        RelationshipDO relationship = relationshipService.getRelationship(id);
        return success(BeanUtils.toBean(relationship, RelationshipRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代理商团队关系分页")
    @PreAuthorize("@ss.hasPermission('agent:relationship:query')")
    public CommonResult<PageResult<RelationshipRespVO>> getRelationshipPage(@Valid RelationshipPageReqVO pageReqVO) {
        PageResult<RelationshipDO> pageResult = relationshipService.getRelationshipPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RelationshipRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代理商团队关系 Excel")
    @PreAuthorize("@ss.hasPermission('agent:relationship:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportRelationshipExcel(@Valid RelationshipPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<RelationshipDO> list = relationshipService.getRelationshipPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代理商团队关系.xls", "数据", RelationshipRespVO.class,
                        BeanUtils.toBean(list, RelationshipRespVO.class));
    }

}