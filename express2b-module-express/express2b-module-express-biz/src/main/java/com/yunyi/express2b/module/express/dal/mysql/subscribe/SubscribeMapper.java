package com.yunyi.express2b.module.express.dal.mysql.subscribe;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.module.express.controller.admin.subscribe.vo.SubscribePageReqVO;
import com.yunyi.express2b.module.express.controller.admin.subscribe.vo.SubscribeSaveReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.subscribe.SubscribeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户订阅信息表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SubscribeMapper extends BaseMapperX<SubscribeDO> {

    default PageResult<SubscribeDO> selectPage(SubscribePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SubscribeDO>()
                .eqIfPresent(SubscribeDO::getTempType, reqVO.getTempType())
                .eqIfPresent(SubscribeDO::getMemberId, reqVO.getMemberId())
                .eqIfPresent(SubscribeDO::getSubscribe, reqVO.getSubscribe())
                .betweenIfPresent(SubscribeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SubscribeDO::getId));
    }

    /**
     * 查询用户是否订阅改模板
     */
    default SubscribeDO selectSubscribe(Long memberId, Integer tempType) {
        LambdaQueryWrapper<SubscribeDO> queryWrapper = new LambdaQueryWrapper<SubscribeDO>();
        queryWrapper.select(SubscribeDO::getSubscribe)
                .eq(SubscribeDO::getMemberId, memberId)
                .eq(SubscribeDO::getTempType, tempType);

        return selectOne(queryWrapper);
    }
    /**
     * 批量插入订阅信息
     */
    int batchInsert(List<SubscribeDO> subscribeDOList);


    /**
     * 根据 SubscribeSaveReqVO 中的 tempType 和 memberId 字段查询 SubscribeDO 对象
     * @param reqVO 包含查询条件的请求对象
     * @return 符合条件的 SubscribeDO 对象，如果没有则返回 null
     */
    default SubscribeDO selectQueryData(SubscribeSaveReqVO reqVO) {
        LambdaQueryWrapper<SubscribeDO> queryWrapper = new LambdaQueryWrapper<SubscribeDO>();
        queryWrapper.select(SubscribeDO::getSubscribe, SubscribeDO::getId)
                .eq(SubscribeDO::getMemberId,  reqVO.getMemberId())
                .eq(SubscribeDO::getTempType, reqVO.getTempType())
                .last("LIMIT 1"); // 添加限制，只返回一条记录

        return selectOne(queryWrapper);
    }


}