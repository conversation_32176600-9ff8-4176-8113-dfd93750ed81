# 小B云运力Pro用户体系产品需求文档 (PRD)

## 1. 修订记录
| 版本 | 日期 | 修订人 | 修订内容 |
|---|---|---|---|
| 1.0 | 2025-06-12 | 解蕾 | 初稿，根据产品讨论创建，定义了新的用户体系和升级路径 |

## 2. 项目背景与目标
### 2.1 背景
在当前产品基础上，为了进一步降低新用户的使用门槛，提升用户转化率和长期留存，我们决定优化现有的"企业账户"体系。旧体系概念较重，可能将无营业执照的个人商家（如直播卖家、微商）拒之门外。

### 2.2 目标
引入一个更灵活、更具吸引力的"免费增值(Freemium)"用户模型。通过设立"标准用户"和"Pro用户"两个层级，实现产品驱动增长(Product-led Growth)，让用户先体验核心价值，再通过解锁高级功能自然地完成升级，最终为未来的商业化奠定坚实基础。

## 3. 用户体系定义
### 3.1 用户层级
| 用户层级 | 目标用户 | 核心价值 | 获取方式 |
|---|---|---|---|
| **标准用户** | 个人、偶尔发件的C端用户或初次接触本产品的小微商户。 | 零门槛快速上手，体验核心发件功能。 | 通过微信/手机号自由注册，默认为此等级。 |
| **Pro用户** | 日发单量稳定的小微企业、电商卖家、直播团队等需要协作和数据管理的用户。 | 免费解锁高级功能，提升发货效率和管理能力。 | 通过自助激活或客服协助免费升级。 |
| **商业版用户** | (未来规划) 发单量大、团队规模大、或需要定制化服务的企业。 | 享受付费专属功能、更高的用量额度和专属服务支持。 | 从Pro用户付费升级。 |

### 3.2 功能权限对比
| 功能维度 | 标准用户 | Pro用户 |
|---|---|---|
| **核心发件** | ✅ 单独/批量发件 | ✅ 所有基础功能 |
| **团队协作** | ❌ | ✅ 子账户管理、权限分配 |
| **高级工具** | ❌ | ✅ API对接、小程序定制、开具发票 |
| **深度洞察** | ❌ | ✅ 更全面的数据报表与分析 |
| **专属价格** | 标准价格 | ✅ 可配置更优惠的专属合同价或预存款折扣 |

### 3.3 Pro功能设计思路
为了让Pro用户感受到明确的价值提升，我们将提供以下专属功能。这些功能的设计旨在解决小微企业从"个人作坊"向"团队运作"和"精细化运营"转变过程中的核心痛点。

#### 3.3.1 团队协作 (子账户管理、权限分配)
*   **用户痛点**: 当业务增长，一个人处理所有发货和对账变得效率低下。老板希望员工能帮忙操作，但又担心共用一个账号带来的安全风险（如资金安全、核心数据泄露）。
*   **解决方案**: Pro用户可以创建多个子账户，并为每个子账户分配不同权限（如：仅发货、可对账、可管理等）。老板用主账号掌控全局，员工用子账户分担工作，权责分明，安全高效。
*   **核心价值**: 解决团队化管理的刚需，让我们的工具从"个人发件帮手"升级为"企业物流协同平台"。

#### 3.3.2 高级工具 (API对接、小程序定制、开具发票)
*   **用户痛点**: 手动处理大量订单费时费力，容易出错；希望拥有自己品牌的发货小程序；正规经营需要发票进行财务记账。
*   **解决方案**:
    *   **API对接**: 提供标准API接口，允许用户将自己的电商平台、ERP、进销存系统与我们打通，实现订单自动同步和批量处理。
    *   **小程序定制**: 提供模板化的小程序定制服务，让用户能快速生成一个带有自己品牌Logo和名称的发货小程序。
    *   **开具发票**: 提供线上申请和开具发票的功能，满足企业财务合规性要求。
*   **核心价值**: 帮助用户实现业务自动化、品牌化和正规化，将我们的产品深度嵌入其核心工作流中。

#### 3.3.3 深度洞察 (更全面的数据报表与分析)
*   **用户痛点**: 不清楚每月物流成本花了多少，哪些地区的订单最多，哪个快递品牌用得最划算。缺乏数据支持，无法优化经营决策。
*   **解决方案**: 提供多维度的数据报表和可视化看板。Pro用户可以清晰地看到订单趋势、成本分析、区域分布、运力品牌偏好等关键数据。
*   **核心价值**: 从提供"操作"价值，升级到提供"决策"价值。帮助用户不仅仅是"完成发货"，更是"优化发货策略"，提升产品粘性。

#### 3.3.4 专属价格 (可配置更优惠的专属合同价或预存款折扣)
*   **用户痛点**: 发货量上来了，希望获得比标准散客更优惠的价格，以降低物流成本，提升竞争力。
*   **解决方案**: 为Pro用户提供价格配置能力。未来可以支持基于发货量承诺的合同价，或通过预存款模式享受阶梯折扣。
*   **核心价值**: 提供最直接的商业激励，建立更紧密的合作关系。这是未来实现商业化变现的关键一环，也是留住高价值客户的核心手段。

## 4. 核心流程：用户升级路径
我们提供"自助升级"和"客服协助"两条路径，满足不同用户的需求。

### 4.1 自助升级路径 (Self-Service)
此路径旨在为用户提供一个即时的、7x24小时可用的全自动升级体验。
#### 4.1.1 流程图
```mermaid
graph TD
    A[用户在小程序内] --> B("看到“升级至Pro”入口<br/>或被场景化提示触发")
    B --> C{点击“免费升级为Pro”}
    C --> D[进入Pro激活页面]
    D --> D1[可选是否关注公众号]
    D1 --> D2[用户关注公众号]
    D2 --> D3[后端记录状态]
    D --> E[填写登录账号和密码]
    E --> F{点击“完成升级”}
    F --> G{后端校验数据}
    G -- 校验失败 --> E
    G -- 校验成功 --> H["更新用户为Pro等级<br/>创建Web登录凭证"]
    H --> I["小程序内显示成功页<br/>并进行新功能引导"]
    H --> I1{"已经关注公众号"}
    I1 --> J[发送公众号欢迎通知]
    I --> K[升级完成]
    J --> K
```

#### 4.1.2 激活页面设计
当用户在小程序内点击【免费升级为Pro用户】后，系统将展示以下激活页面：
---
**免费升级，解锁Pro专属功能！**
为了给您创建Web管理后台，请设置您的登录信息。

**登录账号**： `[ 请输入常用邮箱或手机号 ]`
*(建议使用您好记的邮箱或手机号作为账号)*

**设置密码**： `[ 请输入密码 ]`

**确认密码**： `[ 请再次输入密码 ]`

**[ 完成升级 ]**
---
#### 4.1.3 关键逻辑
1.  **账号验证**：用户输入的"登录账号"需在系统中保持唯一性。
2.  **密码策略**：需符合基本的密码安全策略（如长度、复杂度要求）。
3.  **状态更新**：用户点击"完成升级"后，后端立即将其账户状态更新为"Pro用户"，并创建Web端登录凭证。

### 4.2 客服协助路径 (Sales-Assisted)
此路径为需求复杂、或希望与真人沟通的客户提供个性化服务。
#### 4.2.1 流程说明
用户在小程序内点击【联系我们，定制方案】后，弹出表单让用户提交信息。我们的服务团队将在约定时间内联系客户，提供咨询并协助其完成升级。
#### 4.2.2 表单字段
- **联系人姓名** (必填)
- **联系手机号** (必填)
- **业务说明** (选填, 引导用户描述他的发货量、团队人数、主要需求等)


### 4.3 场景化升级触发点
为避免用户忽略被动放置的"升级"按钮，我们应在用户最需要Pro功能时，主动、场景化地进行提示，以最大化转化率。

| 需求场景 | 触发时机 | 提示文案（示例） |
|---|---|---|
| **团队协作需求** | 当用户在系统中频繁修改寄件人时 | "需要添加团队成员吗？免费升级到Pro版，即可管理子账户！" |
| **发单量与洞察需求** | 当标准用户累计发单量达到一个阈值（如20单）后。 | "您正在成为我们的核心发件人！免费升级到Pro版，解锁详细的数据报表，追踪您的业务增长。" |
| **自动化需求** | 当系统检测到用户在短期内，反复向3个以上相同的地址发件时。 | "发货量很大？Pro用户可以通过API连接您的应用，实现订单全自动处理。" |


## 5. Pro用户引导流程 (Onboarding)
成功升级Pro后，必须立即引导用户体验Pro功能的价值，防止"升完即忘"。
### 5.1 小程序内引导与公众号关注
- **成功页面设计**: 激活成功后，小程序内应展示一个精心设计的成功与引导页面，而非简单的"成功"提示。页面示例如下：
    ---
    ### 🎉 **恭喜您！已成功升级为Pro用户！**
    您的专属Web管理后台已开通，请使用您设置的账号密码登录。
    后台地址： `[https://********]`

    #### **推荐您立即开始 (完成您的第一个Pro操作):**
    - [ ] **[邀请您的第一个团队成员](link_to_feature)**
    - [ ] **[前往Web后台查看数据看板](link_to_feature)**
    ---
    - **关注公众号指引 (可选|如果未管不)**：
        - 在页面下方，放置一个公众号的二维码和引导语："**推荐关注官方公众号**，第一时间获取物流状态通知、优惠活动和专属客服支持。"
        - 明确这是一个**可选操作**，用户不关注也可以继续使用所有功能。
        - 用户长按二维码即可关注，流程顺畅。

## 6. 商业化考量 (Monetization)
当前Pro版本免费，是吸引用户的核心策略。但系统设计必须为未来的商业化预留清晰的、可扩展的路径。

### 6.1 未来用户分层规划
| 用户层级 | 核心定位 | 商业模式 |
|---|---|---|
| **标准版 (Standard)** | 吸引流量，提供基础体验 | 免费 |
| **专业版 (Pro)** | 培养高价值用户，提升产品粘性 | 免费（当前阶段） |
| **商业版 (Business)** | **(未来付费版本)** 面向更大团队和更高发单量的企业 | 按月/年订阅 |

### 6.2 商业化抓手设计
未来从Pro版向商业版引导付费时，将采用以下一种或多种组合策略：
*   **功能限制 (Feature-Gating)**: 某些最高级的功能仅"商业版"可用。
    *   *示例*：更高级的安全设置（如操作日志审计）、自定义角色权限、专属客户成功经理对接。
*   **用量限制 (Usage-Based Limits)**: Pro版在核心指标上会设定一个免费额度，超出部分需升级到商业版。这是最公平、最主流的SaaS商业化模式。
    *   *系统要求*：架构设计必须从第一天起，就能精确追踪每个账户的核心用量（如：每月发单量、子账户数量、API调用次数）。
    *   *示例*：Pro版每月可免费发件50单、免费创建2个子账户。超出后提示升级。
*   **付费附加功能 (Paid Add-ons)**: 部分独立且价值高的功能，可以作为单独的付费项，供Pro或商业版用户按需购买。
    *   *示例*："小程序定制"功能可作为一次性的付费增值服务。

## 7. 需追踪的关键指标 (KPIs)
为科学地衡量本策略的成效，并为后续迭代提供数据支持，我们需要建立一个监控仪表盘，追踪以下核心指标：

| 指标名称 | 计算公式 / 定义 | 衡量目的 |
|---|---|---|
| **Pro用户转化率** | (周期内升级到Pro的用户数 / 周期内活跃的标准用户数) * 100% | 衡量Pro版本对用户的整体吸引力。 |
| **转化周期** | 用户从"注册"到"升级Pro"的平均/中位数天数。 | 评估用户价值发现的速度，周期越短越好。 |
| **Pro功能采用率** | (升级后7天内使用过至少1个Pro功能的用户数 / 总Pro用户数) * 100% | 判断Pro功能是否真正实用，Onboarding引导是否有效。 |
| **渠道效率分析** | 分别统计通过"自助升级"和"客服协助"路径完成升级的用户占比。 | 评估不同转化渠道的效率，指导资源投入。 |
| **用户留存率** | 对比标准用户和Pro用户的次月留存率。 | 验证Pro版本是否能有效提升用户粘性和长期价值。 | 