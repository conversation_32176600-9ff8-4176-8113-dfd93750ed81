package com.yunyi.express2b.module.express.api;


import com.yunyi.express2b.module.express.api.message.SmsApi;

import com.yunyi.express2b.module.express.api.message.dto.OrderChangeDTO;
import com.yunyi.express2b.module.express.dal.dataobject.subscribe.SubscribeDO;
import com.yunyi.express2b.module.express.dal.mysql.subscribe.SubscribeMapper;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 发送消息api实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/27 下午5:24
 */
@Service
public class SmsApiImpl implements SmsApi {


    @Resource
    private SubscribeMapper subscribeMapper;

    /**
     * 发送订单变更消息
     * @param orderChangeDTO
     * @return
     */
    @Override
    public Integer sendOrderMessage(OrderChangeDTO orderChangeDTO) {
        //查询用户是否订阅订单变更通知
        Integer subscribe = getSubscribe(orderChangeDTO.getMemberId(), 1);
        return subscribe;

    }

    /**
     * 获取用户是否订阅指定模板
     */
    public Integer getSubscribe(Long memberId, Integer tempType) {
        // 查询用户用户订阅记录
        SubscribeDO subscribeDO = subscribeMapper.selectSubscribe(memberId, tempType);
        //返回订阅状态
        if (subscribeDO != null) {
            return subscribeDO.getSubscribe();
        } else {
            return 0;
        }
    }

}
