-- 杨浩
ALTER TABLE express2b_order MODIFY COLUMN STATUS VARCHAR(100);

-- 杨浩
drop table if exists express2b_products;
CREATE TABLE `express2b_products` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '物品ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `name` varchar(100) NOT NULL COMMENT '物品名称',
  `quantity` int NOT NULL COMMENT '物品数量',
  `weight` int NOT NULL COMMENT '物品重量(克/g)',
  `category` varchar(100) NOT NULL COMMENT '物品分类',
  `length` int DEFAULT NULL COMMENT '长度(cm)',
  `width` int DEFAULT NULL COMMENT '宽度(cm)',
  `height` int DEFAULT NULL COMMENT '高度(cm)',
  `description` varchar(100) NOT NULL COMMENT '物品详细描述',
  `price` int DEFAULT NULL COMMENT '物品价格',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者', -- 记录创建者
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', -- 记录创建时间
  `updater` varchar(64) DEFAULT '' COMMENT '更新者', -- 记录更新者
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', -- 记录更新时间
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除', -- 是否已删除
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号', -- 租户编号
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物品信息表';

