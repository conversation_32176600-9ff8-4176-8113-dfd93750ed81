package com.yunyi.express2b.module.express.controller.admin.refundorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 退款单管理新增/修改 Request VO")
@Data
public class RefundOrderSaveReqVO {

    @Schema(description = "退款订单ID（主键）", requiredMode = Schema.RequiredMode.REQUIRED, example = "6471")
    private Long id;

    @Schema(description = "关联支付订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3811")
    @NotNull(message = "关联支付订单ID不能为空")
    private Long paymentOrderId;

    @Schema(description = "退款金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "退款金额不能为空")
    private Integer amount;

    @Schema(description = "退款单号")
    private String refundOrderSn;

    @Schema(description = "退款原因", example = "不对")
    private String refundReason;

    @Schema(description = "退款方式（例如：原路返回）")
    private String refundMethod;

    @Schema(description = "退款状态", example = "2")
    private String refundStatus;

    @Schema(description = "最近退款申请时间")
    private LocalDateTime recentRefundApplyTime;

    @Schema(description = "退款提交时间")
    private LocalDateTime refundSubmitTime;

    @Schema(description = "退款状态最后更新时间")
    private LocalDateTime refundStatusUpdateTime;

    @Schema(description = "退款完成时间")
    private LocalDateTime refundCompleteTime;

    @Schema(description = "对账注解", example = "30259")
    private Long reconciliationId;

    @Schema(description = "对账时间")
    private LocalDateTime reconciliationTime;

}