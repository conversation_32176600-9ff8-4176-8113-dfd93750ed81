package com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceuser;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 用户开票信息登记新增/修改 Request VO")
@Data
public class InvoiceUserSaveRespVO {

    @Schema(description = "发票抬头（单位或个人名称）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceTitle;

    @Schema(description = "纳税人识别号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String taxNumber;

    @Schema(description = "接收邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    private String email;
}
