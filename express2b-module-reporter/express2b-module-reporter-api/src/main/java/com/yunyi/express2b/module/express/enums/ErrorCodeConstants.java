//      报表模块-reporter：   1-009-xxx-xxx  [1-009-000-000 ~ 1-009-999-999]
package com.yunyi.express2b.module.reporter.enums;

import com.yunyi.express2b.framework.common.exception.ErrorCode;

/**
 * reporter 错误码枚举类
 *
 * reporter 系统，使用 1_009_000_000 段
 * 报表基础模块：1_009_001_xxx
 */
public interface ErrorCodeConstants {
    // ========== 报表基础模块 1_009_001_xxx ==========
    ErrorCode REPORT_NOT_EXISTS = new ErrorCode(1_009_001_000, "报表不存在");
}