package com.yunyi.express2b.module.agent.controller.admin.agentprofile;

import cn.hutool.core.util.StrUtil;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.agent.api.dto.RegisterDTO;
import com.yunyi.express2b.module.agent.controller.admin.relationship.vo.RelationshipPageReqVO;
import com.yunyi.express2b.module.agent.service.relationship.RelationshipService;
import com.yunyi.express2b.module.infra.api.file.FileApi;
import com.yunyi.express2b.module.pricing.api.PricingApiflow;
import com.yunyi.express2b.module.pricing.api.vo.SelectedPricingTemplateSaveReqVO;
import com.yunyi.express2b.module.system.enums.logger.LoginLogTypeEnum;
import jakarta.annotation.security.PermitAll;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;

import java.io.File;
import java.util.*;
import java.io.IOException;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;

import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.*;

import com.yunyi.express2b.module.agent.controller.admin.agentprofile.vo.*;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.AgentProfileDO;
import com.yunyi.express2b.module.agent.service.agentprofile.AgentProfileService;
import org.springframework.web.multipart.MultipartFile;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 9:53
 */
@Tag(name = "管理后台 - 代理商档案")
@RestController
@RequestMapping("/agent/profile")
@Validated
public class AgentProfileController {


    @Resource
    private AgentProfileService profileService;
    @Resource
    private RelationshipService relationshipService;
    @Autowired
    private AgentProfileService agentProfileService;


    @PostMapping("/create")
    @Operation(summary = "创建代理商档案")
    @PreAuthorize("@ss.hasPermission('agent:profile:create')")
    public CommonResult<Long> createProfile(@Valid @RequestBody AgentProfileSaveReqVO createReqVO) {
        return success(profileService.createProfile(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代理商档案")
    @PreAuthorize("@ss.hasPermission('agent:profile:update')")
    public CommonResult<Boolean> updateProfile(@Valid @RequestBody AgentProfileSaveReqVO updateReqVO) {
        profileService.updateProfile(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代理商档案")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:profile:delete')")
    public CommonResult<Boolean> deleteProfile(@RequestParam("id") Long id) {
        profileService.deleteProfile(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代理商档案")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:profile:query')")
    public CommonResult<AgentProfileRespVO> getProfile(@RequestParam("id") Long id) {
        AgentProfileDO profile = profileService.getProfile(id);
        return success(BeanUtils.toBean(profile, AgentProfileRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代理商档案分页")
    @PreAuthorize("@ss.hasPermission('agent:profile:query')")
    public CommonResult<PageResult<AgentProfileRespVO>> getProfilePage(@Valid AgentProfilePageReqVO pageReqVO) {
        PageResult<AgentProfileDO> pageResult = profileService.getProfilePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AgentProfileRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代理商档案 Excel")
    @PreAuthorize("@ss.hasPermission('agent:profile:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProfileExcel(@Valid AgentProfilePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AgentProfileDO> list = profileService.getProfilePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代理商档案.xls", "数据", AgentProfileRespVO.class,
                        BeanUtils.toBean(list, AgentProfileRespVO.class));
    }

    /**
     * 根据价格模板id进行代理商档案的检索（根据memberId字段），并返回一个邀请链接（String）
     * @param templateId
     * @return
     */

    @GetMapping("/createinvitation")
    @Operation(summary = "为下级选择模板，系统自动生成带模板信息的二维码/邀请链接")
    @PreAuthorize("@ss.hasPermission('agent:profile:query')")
    public CommonResult<String> createTemplate(@RequestParam("templateId")Long templateId){
        //根据用户中心关联ID返回代理商ID并判断价格模板是否可用
        AgentProfileRespVO agentProfileRespVO = agentProfileService.getProfileByMemberId(SecurityFrameworkUtils.getLoginUserId(),templateId);
        return success("app-api/v1/agent/profile/createinvitation?agentId="+agentProfileRespVO.getId()+"&templateId="+templateId);
    }


    @GetMapping("/getTeam")
    @Operation(summary = "获得代理商档案团队")
    @PreAuthorize("@ss.hasPermission('agent:profile:query')")
    public CommonResult<List<AgentProfileRespVO>> getTeam(){
        List<AgentProfileDO> list = profileService.getTeam();
        return success(BeanUtils.toBean(list, AgentProfileRespVO.class));
    }

    /**
     * 根据价格模板的id以及代理商id进行代理商档案的更新
     * @param agentId
     * @param templateId
     * @return
     */
    @PutMapping("/accesstemplate")
    @Operation(summary = "通过邀请码/二维码，自动绑定上级和价格模板")
    @PreAuthorize("@ss.hasPermission('agent:profile:create')")
    public CommonResult<Boolean> accessTemplate(@RequestParam("agentId")Long agentId,@RequestParam("templateId")Long templateId) {
        //发送数据并更新代理商档案信息,绑定价格模板
        agentProfileService.editAgentProfile(agentId,templateId);
        return success(true);
    }

    @PostMapping("/register")
    @PermitAll
    @Operation(summary = "注册")
    public CommonResult<Boolean> register(@Valid @RequestBody RegisterDTO registerDTO) {

        //根据关联用户中心id注册代理商、注册租户、注册钱包SecurityFrameworkUtils.getLoginUserId()
        agentProfileService.registerProfile(registerDTO);
        return success(true);

    }

    @PostMapping("/license")
    @Operation(summary = "上传营业执照")
    public CommonResult<Boolean> uploadLicense(@RequestParam("file") MultipartFile file) throws Exception {

        File tempFile = File.createTempFile("license-", file.getOriginalFilename());
        try {
            file.transferTo(tempFile);
            agentProfileService.saveImageUrl(tempFile);
            return success(true);
        } finally {
            // 可选：上传后删除临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }
    }



}