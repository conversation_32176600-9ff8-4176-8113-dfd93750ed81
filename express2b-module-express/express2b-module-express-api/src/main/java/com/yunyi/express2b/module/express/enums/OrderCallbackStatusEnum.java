package com.yunyi.express2b.module.express.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/7 20:31
 */
public enum OrderCallbackStatusEnum {
    SUCCESS(0, "下单成功"),
    ORDER_ACCEPTED(1, "已接单"),
    COLLECTING(2, "收件中"),
    USER_CANCELLED(9, "用户主动取消"),
    PICKED_UP(10, "已取件"),
    COLLECTION_FAILED(11, "揽收失败"),
    SIGNED(13, "已签收"),
    ABNORMAL_SIGNED(14, "异常签收"),
    SETTLED(15, "已结算"),
    ORDER_CANCELLED(99, "订单已取消"),
    IN_TRANSIT(101, "运输中"),
    ORDER_CREATED(200, "已出单"),
    ORDER_CREATION_FAILED(201, "出单失败"),
    ORDER_PLACEMENT_FAILED(610, "下单失败"),
    WEIGHT_MODIFIED(155, "修改重量"),
    ORDER_REVIVED(166, "订单复活"),
    DELIVERY_IN_PROGRESS(400, "派送中");
    private final Integer code;
    private final String description;

    OrderCallbackStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据 code 获取枚举值
     */
    public static OrderCallbackStatusEnum fromCode(Integer code) {
        for (OrderCallbackStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的状态码: " + code);
    }

    /**
     * 获取所有状态码与描述的映射（可用于接口返回或日志打印）
     */
    public static Map<Integer, String> getCodeDescriptionMap() {
        Map<Integer, String> map = new HashMap<>();
        for (OrderCallbackStatusEnum status : values()) {
            map.put(status.getCode(), status.getDescription());
        }
        return map;
    }
}
