package com.yunyi.express2b.module.pricing.api;


import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.agent.api.AgentApi;
import com.yunyi.express2b.module.agent.api.TemplateAgentRelationshipApi;
import com.yunyi.express2b.module.agent.api.dto.DetailShareReqDO;
import com.yunyi.express2b.module.agent.api.dto.RelationShipDTO;
import com.yunyi.express2b.module.agent.api.dto.TemplateDTO;
import com.yunyi.express2b.module.commission.api.CommissionApi;
import com.yunyi.express2b.module.commission.api.vo.DetailSaveReqVO;
import com.yunyi.express2b.module.pricing.api.dto.DetailShareReqDTO;
import com.yunyi.express2b.module.pricing.api.vo.PricingRuleApiPageReqVO;
import com.yunyi.express2b.module.pricing.dal.dataobject.pricingrule.PricingRuleDO;
import com.yunyi.express2b.module.pricing.dal.mysql.pricingrule.PricingRuleMapper;
import com.yunyi.express2b.module.pricing.service.pricingrule.PricingRuleService;
import com.yunyi.express2b.module.wallet.api.TransationsApi.TransactionsApi;
import com.yunyi.express2b.module.wallet.api.TransationsApi.dto.TransactionsDetailsDTO;
import com.yunyi.express2b.module.wallet.controller.admin.transactions.vo.TransactionsSaveReqVO;
import com.yunyi.express2b.module.wallet.service.transactions.TransactionsService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 分润/差价 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PricingUtilsApiServiceImpl implements PricingUtilsApiService {

    @Resource
    private CommissionApi commissionApi;
    @Resource
    private AgentApi agentProfileQueryApi;
    //    优化前查询定价模版的接口
    @Resource
    private TemplateAgentRelationshipApi templateAgentRelationshipApi;
    @Resource
    private PricingRuleService pricingRuleService;
    //   优化后的查询定价模版接口
    @Resource
    private PricingRuleMapper pricingRuleMapper;
    @Resource
    private TransactionsService transactionsService;
    @Resource
    private TransactionsApi transactionsApi;


    /**
     * 分润公共方法
     *
     * @param reqDO 分润请求对象
     * @return 分润明细ID
     */

    @Override
    public CommonResult<String> sharebenefit(DetailShareReqDTO reqDO) {
        CommonResult<String> Result = new CommonResult<>();
        try {
            // 1. 检查代理商等级
            Integer agentLevelId = Integer.valueOf(agentProfileQueryApi.getAgentLevelId().toString());
            switch (agentLevelId) {
                case 3:
                    Result.setData(agentLevelId.toString());
                    Result.setMsg("V3等级不分润");
                    return Result;
                case 1:
                    CommonResult<Long> CommonResult = handleV1AgentShare(BeanUtils.toBean(reqDO, DetailShareReqDO.class));
                    Result.setData(CommonResult.getData().toString());
                    Result.setMsg("V2等级分润");
                    return Result;
                case 2:
                    CommonResult<Long> longCommonResult = handleV2AgentShare(BeanUtils.toBean(reqDO, DetailShareReqDO.class));
                    Result.setData(longCommonResult.toString());
                    Result.setMsg("V3等级分润");
                    return Result;
                default:
                    Result.setData(agentLevelId.toString());
                    Result.setMsg("未知的代理商等级");
                    return Result;
            }
        } catch (Exception e) {
            // 返回包含错误信息的结果
            return CommonResult.error(500, "分润处理失败: " + e.getLocalizedMessage());
        }
    }


    /**
     * 处理V1代理商分润
     */
    private CommonResult<Long> handleV1AgentShare(DetailShareReqDO reqDO) {
        // 如果是v1级别的 需要查出最近的上级v2和最近的上级v3
        // 获取V1代理商模板
        TemplateDTO v1Template = templateAgentRelationshipApi.getTemplateIdByAgentId(reqDO.getAgentId());

        // 获取V1上级代理商信息
        RelationShipDTO v2Relation = agentProfileQueryApi.getAgentProfileByAgentIdAndLevelId(reqDO.getAgentId(), 1L);
        Long v2AgentId = v2Relation.getNearestSpecificLevelAncestorId();
        TemplateDTO v2Template = templateAgentRelationshipApi.getTemplateIdByAgentId(v2AgentId);

        // 获取V2上级代理商信息
        RelationShipDTO v3Relation = agentProfileQueryApi.getAgentProfileByAgentIdAndLevelId(v2AgentId, 2L);
        Long v3AgentId = v3Relation.getNearestSpecificLevelAncestorId();
        TemplateDTO v3Template = templateAgentRelationshipApi.getTemplateIdByAgentId(v3AgentId);

        // 计算V1到V2的分润
        Long v1ToV2Detail = calculateAndSaveShare(reqDO, v1Template, v2Template, v2AgentId);

        // 计算V2到V3的分润
        Long v2ToV3Detail = calculateAndSaveShare(reqDO, v2Template, v3Template, v3AgentId);

        return CommonResult.success(v1ToV2Detail);
    }

    /**
     * 处理V2代理商分润
     */
    private CommonResult<Long> handleV2AgentShare(DetailShareReqDO reqDO) {
        // 获取V2代理商模板
        TemplateDTO v2Template = templateAgentRelationshipApi.getTemplateIdByAgentId(reqDO.getAgentId());

        // 获取V2上级代理商信息
        RelationShipDTO v3Relation = agentProfileQueryApi.getAgentProfileByAgentIdAndLevelId(reqDO.getAgentId(), 2L);
        //获取v3代理商id
        Long v3AgentId = v3Relation.getNearestSpecificLevelAncestorId();
        TemplateDTO v3Template = templateAgentRelationshipApi.getTemplateIdByAgentId(v3AgentId);

        // 计算V2到V3的分润
        Long detail = calculateAndSaveShare(reqDO, v2Template, v3Template, v3AgentId);

        return CommonResult.success(detail);
    }

    /**
     * 计算并保存分润
     */
    private Long calculateAndSaveShare(DetailShareReqDO reqDO, TemplateDTO lowerTemplate, TemplateDTO higherTemplate, Long targetAgentId) {
        // 计算首重分润
        int firstWeightDifference = higherTemplate.getPlatformFirstPrice() - lowerTemplate.getPlatformFirstPrice();

        // 计算续重分润
        int additionalWeightDifference = Math.toIntExact(reqDO.getAgentId() - 1) *
                (higherTemplate.getPlatformAdditionalPrice() - lowerTemplate.getPlatformAdditionalPrice());

        // 计算总分润
        int totalShare = firstWeightDifference + additionalWeightDifference;

        // 创建分润明细
        DetailSaveReqVO detailSaveReqVO = new DetailSaveReqVO();
        detailSaveReqVO.setOrderId(reqDO.getOrderId());
        detailSaveReqVO.setOrderNo(reqDO.getOrderNo());
        detailSaveReqVO.setTargetId(targetAgentId);
        detailSaveReqVO.setTargetType(2); // 分润目标类型：代理商
        detailSaveReqVO.setAmount(totalShare);
        detailSaveReqVO.setStatus(1); // 状态：未结算

        return commissionApi.createDetail(detailSaveReqVO);
    }




    /**
     * 差价计算公共方法
     *
     * @param reqVO
     * @return
     */
    @Override
    public CommonResult<PricingRuleApiPageReqVO> difference(PricingRuleApiPageReqVO reqVO) {
        // 1. 获取订单数据（假设通过方法参数传入或调用其他API获取）
        Integer orderFirstWeight = reqVO.getFirstWeightMarkup(); // 假设VO中包含首重信息
        Integer orderAdditionalWeight = reqVO.getAdditionalWeightMarkup(); // 假设VO中包含续重信息
        // 2. 根据代理商ID查询定价规则（reqVO中已提供agentId）
        Long agentId = reqVO.getAgentId();
        // 用于查询规则
        Long ruleId = pricingRuleMapper.selectIdByAgentId(agentId);
        PricingRuleDO rule = pricingRuleService.getRule(ruleId);
        // 3. 获取代理商对应的价格（模拟数据，实际应调用服务获取）
        Integer agentFirstPrice = rule.getFirstWeightMarkup(); // 代理商默认首重价格（单位：分）
        Integer agentAdditionalPrice = rule.getAdditionalWeightMarkup() - agentFirstPrice; // 代理商续重价格（单位：分）
        //还要拿到对应的模板价格，然后用代理商价格-模板价格即可得出差价
        TemplateDTO templateDTO = templateAgentRelationshipApi.getTemplateIdByAgentId(agentId);
        //orderFirstWeight*(templateFirstPrice-模板首重价格)+templateAdditionalPrice*(templateAdditionalPrice-模板续重价格) = (double)总差价
        double totalDifferencePrice = orderFirstWeight * (agentFirstPrice - templateDTO.getFirstWeightMarkup()) + orderAdditionalWeight * (agentAdditionalPrice - templateDTO.getAdditionalWeightMarkup());
        // 6. 将差价添加到钱包流水表（需调用钱包服务接口，此处为模拟逻辑）
        TransactionsDetailsDTO transactionsDTO = transactionsApi.getTransactionsDetailsByAgentId(agentId);
        //然后把拿到的DTO转成VO返回
        TransactionsSaveReqVO TransactionsSaveReqVO = BeanUtils.toBean(transactionsDTO, TransactionsSaveReqVO.class);
        // 6. 将差价添加到钱包流水表（需调用钱包服务接口）
        transactionsService.createTransactions(TransactionsSaveReqVO);
        // 返回结果
        return CommonResult.success(reqVO);

    }

}