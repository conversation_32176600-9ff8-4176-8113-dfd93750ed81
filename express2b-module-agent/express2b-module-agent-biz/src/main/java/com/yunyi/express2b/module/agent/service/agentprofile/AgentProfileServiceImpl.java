package com.yunyi.express2b.module.agent.service.agentprofile;

import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.agent.api.dto.RegisterDTO;
import com.yunyi.express2b.module.agent.dal.mysql.relationship.RelationshipMapper;
import com.yunyi.express2b.module.agent.service.relationship.RelationshipService;
import com.yunyi.express2b.module.express.api.ordertotal.OrderAgentTotalApi;
import com.yunyi.express2b.module.infra.api.file.FileApi;
import com.yunyi.express2b.module.pricing.api.PricingApiflow;
import com.yunyi.express2b.module.pricing.api.vo.SelectedPricingTemplateSaveReqVO;
import com.yunyi.express2b.module.system.api.tenant.TenantApi;
import com.yunyi.express2b.module.system.api.tenant.dto.TenantSaveReqDTO;
import com.yunyi.express2b.module.wallet.api.WalletApi.WalletApi;
import com.yunyi.express2b.module.pricing.api.vo.TemplateSaveReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.io.File;
import java.util.*;
import com.yunyi.express2b.module.agent.controller.admin.agentprofile.vo.*;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.AgentProfileDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;

import com.yunyi.express2b.module.agent.dal.mysql.agentprofile.AgentProfileMapper;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.*;

/**
 * 代理商档案 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AgentProfileServiceImpl implements AgentProfileService {

    @Resource
    private AgentProfileMapper profileMapper;
    @Autowired
    private AgentProfileMapper agentProfileMapper;
    @Resource
    private PricingApiflow pricingApiflow;
    @Resource
    private WalletApi walletApi;
    @Resource
    private TenantApi tenantApi;
    @Resource
    private FileApi fileApi;
    @Autowired
    private RelationshipService relationshipService;
    @Resource
    private OrderAgentTotalApi orderAgentTotal;
    @Resource
    private RelationshipMapper relationshipMapper;

    @Override
    public Long createProfile(AgentProfileSaveReqVO createReqVO) {
        // 插入
        AgentProfileDO profile = BeanUtils.toBean(createReqVO, AgentProfileDO.class);
        profileMapper.insert(profile);
        // 返回
        return profile.getId();
    }

    @Override
    public void updateProfile(AgentProfileSaveReqVO updateReqVO) {
        // 校验存在
        validateProfileExists(updateReqVO.getId());
        // 更新
        AgentProfileDO updateObj = BeanUtils.toBean(updateReqVO, AgentProfileDO.class);
        profileMapper.updateById(updateObj);
    }

    @Override
    public void deleteProfile(Long id) {
        // 校验存在
        validateProfileExists(id);
        // 删除
        profileMapper.deleteById(id);
    }

    private void validateProfileExists(Long id) {
        if (profileMapper.selectById(id) == null) {
            throw exception(PROFILE_NOT_EXISTS);
        }
    }

    @Override
    public AgentProfileDO getProfile(Long id) {
        return profileMapper.selectById(id);
    }

    @Override
    public PageResult<AgentProfileDO> getProfilePage(AgentProfilePageReqVO pageReqVO) {
        return profileMapper.selectPage(pageReqVO);
    }

    /**
     * 根据为下级选择的定价模板更新代理商档案
     * @param agentId
     * @param templateId
     */
    @Override
    public void  editAgentProfile(Long agentId,Long templateId) {
        AgentProfileRespVO agentProfileRespVO = new AgentProfileRespVO()
            .setReferrerAgentId(agentId)
            .setCustomPricingTemplateId(templateId);
        if(agentProfileRespVO.getCustomPricingTemplateId()==null){
            throw exception(TEMPLATE_NOT_EXISTS);
        }
        if(agentProfileRespVO.getReferrerAgentId()==null){
            throw exception(AGENT_NOT_EXISTS);
        }
        //根据memberId查询代理商档案信息
        AgentProfileDO agentProfileDO = agentProfileMapper.getAgentIdByMemberId(SecurityFrameworkUtils.getLoginUserId());
        //如果为新用户则跳转注册界面
        if(agentProfileDO==null){
            throw exception(AGENT_NOT_EXISTS);
        }
        //创建代理商档案信息的VO对象
        agentProfileDO.setReferrerAgentId(agentProfileRespVO.getReferrerAgentId())
                        .setCustomPricingTemplateId(agentProfileRespVO.getCustomPricingTemplateId());
        //创建选择价格模板的对象
        SelectedPricingTemplateSaveReqVO selectedPricingTemplateSaveReqVO = new SelectedPricingTemplateSaveReqVO()
                .setSuperiorAgentId(agentProfileRespVO.getReferrerAgentId())
                .setDownlineAgentId(agentId)
                .setPricingTemplateId(templateId);
        //创建选择价格模板
        pricingApiflow.createSelectedPricingTemplate(selectedPricingTemplateSaveReqVO);
        //更新代理商档案信息
        agentProfileMapper.updateById(agentProfileDO);
        //更新团队关系表
        relationshipService.insertRelationshipById(agentProfileDO);
    }

    /**
     * 通过父团队信息的id查询子团队信息
     * @return
     */
    @Override
    public List<AgentProfileDO> getTeam() {
        AgentProfileDO agentProfileDO = agentProfileMapper.getAgentIdByMemberId(1L);
        return profileMapper.getTeamById(agentProfileDO.getId());
    }

    /**
     * 根据memberId查询代理商档案ID
     * @return
     */
    @Override
    public AgentProfileRespVO getProfileByMemberId(Long memberId,Long templateId) {
        AgentProfileDO agentProfileDO = agentProfileMapper.getAgentIdByMemberId(memberId);
        //判断存在
        if(agentProfileDO==null)
            throw exception(AGENT_NOT_EXISTS);
        TemplateSaveReqVO templateSaveReqVO = pricingApiflow.statusTemplate(templateId);
        //判断是否可用
        if(templateSaveReqVO.getStatus()==0L)
            throw exception(TEMPLATE_NOT_EXISTS);
        return BeanUtils.toBean(agentProfileDO, AgentProfileRespVO.class);
    }

    /**
     * 注册
     * @param registerDTO
     */
    @Override
    public void registerProfile(RegisterDTO registerDTO) {

        //判断关联用户id是否存在代理商档案SecurityFrameworkUtils.getLoginUserId()
        if(agentProfileMapper.getAgentIdByMemberId(SecurityFrameworkUtils.getLoginUserId())!=null)
            throw exception(AGENT_EXISTS);
        //判断手机号是否唯一
        if(agentProfileMapper.getAgentByMobile(registerDTO.getMobile())!=null)
            throw exception(MOBILE_EXISTS);
        //创建租户，获取代理商ID
        Long systemTenantID = tenantApi.createTenant(new TenantSaveReqDTO()
                .setName(registerDTO.getUsername())
                .setUsername(registerDTO.getUsername())
                .setPassword(registerDTO.getPassword())
                .setExpireTime(registerDTO.getExpireTime())
                .setStatus(registerDTO.getStatus())
                .setPackageId(registerDTO.getPackageId())
                .setAccountCount(registerDTO.getAccountCount())
                .setContactName(registerDTO.getContactName()));


        //注册代理商档案并注册租户
        AgentProfileRespVO agentProfileRespVO = new AgentProfileRespVO()
                .setName(registerDTO.getUsername())
                .setMobile(registerDTO.getMobile())
                .setMemberId(SecurityFrameworkUtils.getLoginUserId())
                .setEmail(registerDTO.getEmail())
                .setStatus(0)
                .setLevelId(1L)
                .setSystemTenantId(systemTenantID);
        agentProfileMapper.insert(BeanUtils.toBean(agentProfileRespVO, AgentProfileDO.class));
        //判断插入是否成功
        AgentProfileDO agentProfileDO = agentProfileMapper.getAgentIdByMemberId(SecurityFrameworkUtils.getLoginUserId());
        if(agentProfileDO==null){
            throw exception(AGENT_NOT_EXISTS);
        }
        //注册钱包，存入钱包id
        agentProfileDO.setWalletId((walletApi.createWallet(agentProfileDO.getId())).toString());

        //更新代理商表，将钱包id、租户id存入
        agentProfileMapper.updateById(agentProfileDO);
    }

    /**
     * 上传营业执照,并保存
     * @param file
     */
    @Override
    public void saveImageUrl(File file) throws Exception {

        //上传图片返回url
        List<String> imageUrlList= fileApi.imageUploadMsg(file);
        if (imageUrlList == null || imageUrlList.isEmpty()) {
            throw new Exception("图片上传失败");
        }
        // 从返回的列表中获取第一个URL（假设只需要一个）
        String imageUrl = imageUrlList.get(0);

        Long memberId = SecurityFrameworkUtils.getLoginUserId();
        AgentProfileDO agentProfile = agentProfileMapper.selectByMemberId(memberId);
        if (agentProfile == null) {
            throw new Exception("memberId对应的记录不存在，无法更新");
        }
        int rows = agentProfileMapper.updateLicenseUr(imageUrl, memberId);
        if (rows <= 0) {
            throw new Exception("保存营业执照URL失败，请重试");
        }

    }
}