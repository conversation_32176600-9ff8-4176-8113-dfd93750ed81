package com.yunyi.express2b.module.crm.controller.admin.cardstream;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;
import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;
import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.*;
import com.yunyi.express2b.module.crm.controller.admin.cardstream.vo.*;
import com.yunyi.express2b.module.crm.dal.dataobject.cardstream.CardStreamDO;
import com.yunyi.express2b.module.crm.service.cardstream.CardStreamService;
/**
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 卡流水")
@RestController
@RequestMapping("crm/card/card-stream")
@Validated
public class CardStreamController {

    @Resource
    private CardStreamService cardStreamService;

    @PostMapping("/create")
    @Operation(summary = "创建卡流水")
    @PreAuthorize("@ss.hasPermission('crm:card-stream:create')")
    public CommonResult<Long> createCardStream(@Valid @RequestBody CardStreamSaveReqVO createReqVO) {
        return success(cardStreamService.createCardStream(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新卡流水")
    @PreAuthorize("@ss.hasPermission('crm:card-stream:update')")
    public CommonResult<Boolean> updateCardStream(@Valid @RequestBody CardStreamSaveReqVO updateReqVO) {
        cardStreamService.updateCardStream(updateReqVO);
        return success(true);
    }

    /*@DeleteMapping("/delete")
    @Operation(summary = "删除卡流水")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('crm:card-stream:delete')")
    public CommonResult<Boolean> deleteCardStream(@RequestParam("id") Long id) {
        cardStreamService.deleteCardStream(id);
        return success(true);
    }*/

    @GetMapping("/get")
    @Operation(summary = "获得卡流水")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('crm:card-stream:query')")
    public CommonResult<CardStreamRespVO> getCardStream(@RequestParam("id") Long id) {
        CardStreamDO cardStream = cardStreamService.getCardStream(id);
        return success(BeanUtils.toBean(cardStream, CardStreamRespVO.class));
    }
    //修改次卡核销流水



    @GetMapping("/page")
    @Operation(summary = "获得卡流水分页")
    @PreAuthorize("@ss.hasPermission('crm:card-stream:query')")
    public CommonResult<PageResult<CardStreamRespVO>> getCardStreamPage(@Valid CardStreamPageReqVO pageReqVO) {
        PageResult<CardStreamDO> pageResult = cardStreamService.getCardStreamPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CardStreamRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出卡流水 Excel")
    @PreAuthorize("@ss.hasPermission('crm:card-stream:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCardStreamExcel(@Valid CardStreamPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CardStreamDO> list = cardStreamService.getCardStreamPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "卡流水.xls", "数据", CardStreamRespVO.class,
                        BeanUtils.toBean(list, CardStreamRespVO.class));
    }

}