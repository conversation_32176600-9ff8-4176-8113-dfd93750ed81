package com.yunyi.express2b.module.system.dal.dataobject.news;

import com.yunyi.express2b.module.system.enums.DictTypeConstants;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 文章管理 DO
 *
 * <AUTHOR>
 */
@TableName("express2b_new")
@KeySequence("express2b_new_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewsDO extends BaseDO {

    /**
     * 文章ID
     */
    @TableId
    private Long id;
    /**
     * 文章标题
     */
    private String title;
    /**
     * 文章内容
     */
    private String content;
    /**
     * 文章类型(枚举)
     * 枚举 {@link DictTypeConstants}
     */
    private Integer type;

}