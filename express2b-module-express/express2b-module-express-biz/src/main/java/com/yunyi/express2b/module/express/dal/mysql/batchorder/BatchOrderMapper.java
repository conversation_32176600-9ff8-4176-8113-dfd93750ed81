package com.yunyi.express2b.module.express.dal.mysql.batchorder;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.module.express.controller.admin.batchorder.vo.BatchOrderPageReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.batchorder.BatchOrderDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 批量订单管理 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BatchOrderMapper extends BaseMapperX<BatchOrderDO> {

    default PageResult<BatchOrderDO> selectPage(BatchOrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BatchOrderDO>()
                .eqIfPresent(BatchOrderDO::getPersonalId, reqVO.getPersonalId())
                .eqIfPresent(BatchOrderDO::getType, reqVO.getType())
                .eqIfPresent(BatchOrderDO::getBatchNumber, reqVO.getBatchNumber())
                .eqIfPresent(BatchOrderDO::getData, reqVO.getData())
                .betweenIfPresent(BatchOrderDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BatchOrderDO::getId));
    }

    default BatchOrderDO selectBatchOrder(String batchNumber){
        return selectOne(BatchOrderDO::getBatchNumber, batchNumber);
    }

    /**
     * 根据批量订单编号，更新他的data数据，用在excel上传oss后回调的更新事件中。
     * @param url oss回调的url地址
     * @param batchOrderId 批量订单编号
     */
    default void updateDataById(String url, int batchOrderId){
        update(BatchOrderDO.builder()
                .data(url).build(),
                new LambdaQueryWrapperX<BatchOrderDO>()
                        .eq(BatchOrderDO::getId, batchOrderId)
        );
    }
}