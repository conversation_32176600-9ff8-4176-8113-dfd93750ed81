package com.yunyi.express2b.module.express.controller.admin.batchorder;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;
import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;
import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.*;
import com.yunyi.express2b.module.express.controller.admin.batchorder.vo.*;
import com.yunyi.express2b.module.express.dal.dataobject.batchorder.BatchOrderDO;
import com.yunyi.express2b.module.express.service.batchorder.BatchOrderService;

@Tag(name = "管理后台 - 批量订单管理")
@RestController
@RequestMapping("/express/batch-order")
@Validated
public class BatchOrderController {

    @Resource
    private BatchOrderService batchOrderService;

    @PostMapping("/create")
    @Operation(summary = "创建批量订单管理")
    @PreAuthorize("@ss.hasPermission('express:batch-order:create')")
    public CommonResult<Long> createBatchOrder(@Valid @RequestBody BatchOrderSaveReqVO createReqVO) {
        return success(batchOrderService.createBatchOrder(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新批量订单管理")
    @PreAuthorize("@ss.hasPermission('express:batch-order:update')")
    public CommonResult<Boolean> updateBatchOrder(@Valid @RequestBody BatchOrderSaveReqVO updateReqVO) {
        batchOrderService.updateBatchOrder(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除批量订单管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('express:batch-order:delete')")
    public CommonResult<Boolean> deleteBatchOrder(@RequestParam("id") Long id) {
        batchOrderService.deleteBatchOrder(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得批量订单管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('express:batch-order:query')")
    public CommonResult<BatchOrderRespVO> getBatchOrder(@RequestParam("id") Long id) {
        BatchOrderDO batchOrder = batchOrderService.getBatchOrder(id);
        return success(BeanUtils.toBean(batchOrder, BatchOrderRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得批量订单管理分页")
    @PreAuthorize("@ss.hasPermission('express:batch-order:query')")
    public CommonResult<PageResult<BatchOrderRespVO>> getBatchOrderPage(@Valid BatchOrderPageReqVO pageReqVO) {
        PageResult<BatchOrderDO> pageResult = batchOrderService.getBatchOrderPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BatchOrderRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出批量订单管理 Excel")
    @PreAuthorize("@ss.hasPermission('express:batch-order:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportBatchOrderExcel(@Valid BatchOrderPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BatchOrderDO> list = batchOrderService.getBatchOrderPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "批量订单管理.xls", "数据", BatchOrderRespVO.class,
                        BeanUtils.toBean(list, BatchOrderRespVO.class));
    }

}