package com.yunyi.express2b.module.express.dal.dataobject.order;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/16 11:13
 */

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;

@Data
public class OrderAndProductsReqDO {
    /**
     * 订单ID
     */
    @TableId
    private Long id;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 批量订单主键
     */
    private Long batchId;
    /**
     * 用户ID
     */
    private Long personalId;
    /**
     * 快递品牌ID
     */
    private Long brandId;
    /**
     * 阶梯价策略标志
     */
    private String pricingKey;
    /**
     * 分润策略标志
     */
    private String profitKey;
    /**
     * 订单状态
     */
    private String status;
    /**
     * 寄件人姓名
     */
    private String senderName;
    /**
     * 寄件人电话
     */
    private String senderPhone;
    /**
     * 寄件人地址
     */
    private String senderAddress;
    /**
     * 寄件人地址ID
     */
    private Long senderAddressId;
    /**
     * 收件人姓名
     */
    private String receiverName;
    /**
     * 收件人电话
     */
    private String receiverPhone;
    /**
     * 收件人地址
     */
    private String receiverAddress;
    /**
     * 收件人地址ID
     */
    private Long receiverAddressId;
    /**
     * 成本金额
     */
    private Integer costAmount;
    /**
     * 零售金额
     */
    private Integer retailAmount;
    /**
     * 实收金额|订单金额
     */
    private Integer receivedAmount;
    /**
     * 优惠总金额
     */
    private Integer discountAmount;
    /**
     * 代理商佣金
     */
    private Integer agentCommission;
    /**
     * 团长佣金
     */
    private Integer groupLeaderCommission;
    /**
     * 快递运单号
     */
    private String trackingNumber;
    /**
     * 物品名称
     */
    private String itemName;
    /**
     * 物品分类
     */
    private String itemCategory;
    /**
     * 物品数量
     */
    private Integer itemQuantity;
    /**
     * 物品重量（kg）
     */
    private BigDecimal itemWeight;
    /**
     * 物品价值（元）
     */
    private BigDecimal itemValue;
    /**
     * 包装详情
     */
    private String packagingDetails;
    /**
     * 是否保价
     */
    private Boolean isInsured;
    /**
     * 保价金额（元）
     */
    private BigDecimal insuredAmount;
    /**
     * 预约日期
     */
    private LocalDate scheduledDate;
    /**
     * 收件员姓名
     */
    private String receiverStaffName;

    /**
     * 支付类型
     */
    private String payType;
    /**
     * 用户地址
     */
    private String memberIp;
    /**
     * 预约取件起止时间
     */
    private LocalTime scheduledStartTime;
    /**
     * 预约取件结束时间
     */
    private LocalTime scheduledEndTime;

    /**
     * 统一运力订单的唯一标识
     */
    private String ucmOrderSn;
    /**
     * 取件的预约日期
     */
    private String appointmentTime;

    //#############################
    /**
     * 物品ID
     */
    @TableId
    private Long productsId;
    /**
     * 订单ID
     */
    private Long productsOrderId;
    /**
     * 物品名称
     */
    private String name;
    /**
     * 物品重量(克/g)
     */
    private Integer weight;
    /**
     * 物品分类
     */
    private String category;
    /**
     * 长度(cm)
     */
    private Integer length;
    /**
     * 宽度(cm)
     */
    private Integer width;
    /**
     * 高度(cm)
     */
    private Integer height;
    /**
     * 物品详细描述
     */
    private String description;
    /**
     * 物品价格
     */
    private Integer price;

    /**
     * 物品数量
     */
    private Integer quantity;
}
