package com.yunyi.express2b.module.crm.service.companyaudithistory;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import com.yunyi.express2b.module.crm.controller.admin.companyaudithistory.vo.*;
import com.yunyi.express2b.module.crm.dal.dataobject.companyaudithistory.CompanyAuditHistoryDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.crm.dal.mysql.companyaudithistory.CompanyAuditHistoryMapper;
import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.crm.enums.ErrorCodeConstants.*;

/**
 * 企业账户审核记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CompanyAuditHistoryServiceImpl implements CompanyAuditHistoryService {

    @Resource
    private CompanyAuditHistoryMapper companyAuditHistoryMapper;

    /**
     * 创建企业账户审核记录
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Long createCompanyAuditHistory(CompanyAuditHistorySaveReqVO createReqVO) {
        // 插入
        CompanyAuditHistoryDO companyAuditHistory = BeanUtils.toBean(createReqVO, CompanyAuditHistoryDO.class);
        companyAuditHistoryMapper.insert(companyAuditHistory);
        // 返回
        return companyAuditHistory.getId();
    }

    /**
     * 更新企业账户审核记录
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateCompanyAuditHistory(CompanyAuditHistorySaveReqVO updateReqVO) {
        // 校验存在
        validateCompanyAuditHistoryExists(updateReqVO.getId());
        // 更新
        CompanyAuditHistoryDO updateObj = BeanUtils.toBean(updateReqVO, CompanyAuditHistoryDO.class);
        companyAuditHistoryMapper.updateById(updateObj);
    }

    /**
     * 删除企业账户审核记录
     * @param id 编号
     */
    @Override
    public void deleteCompanyAuditHistory(Long id) {
        // 校验存在
        validateCompanyAuditHistoryExists(id);
        // 删除
        companyAuditHistoryMapper.deleteById(id);
    }

    /**
     * 校验企业账户审核记录是否存在
     * @param id 编号
     */
    private void validateCompanyAuditHistoryExists(Long id) {
        if (companyAuditHistoryMapper.selectById(id) == null) {
            throw exception(COMPANY_AUDIT_HISTORY_NOT_EXISTS);
        }
    }

    /**
     * 获得企业账户审核记录
     * @param id 编号
     * @return 企业账户审核记录
     */
    @Override
    public CompanyAuditHistoryDO getCompanyAuditHistory(Long id) {
        return companyAuditHistoryMapper.selectById(id);
    }

    /**
     * 获得企业账户审核记录分页
     * @param pageReqVO 分页查询
     * @return 企业账户审核记录分页
     */
    @Override
    public PageResult<CompanyAuditHistoryDO> getCompanyAuditHistoryPage(CompanyAuditHistoryPageReqVO pageReqVO) {
        return companyAuditHistoryMapper.selectPage(pageReqVO);
    }

}