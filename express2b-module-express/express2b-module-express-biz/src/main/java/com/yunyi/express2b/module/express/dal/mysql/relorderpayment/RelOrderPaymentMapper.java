package com.yunyi.express2b.module.express.dal.mysql.relorderpayment;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yunyi.express2b.framework.common.util.collection.CollectionUtils;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.module.express.dal.dataobject.relorderpayment.RelOrderPaymentDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 订单与支付订单关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RelOrderPaymentMapper extends BaseMapperX<RelOrderPaymentDO> {
    /**
     * 根据订单id查询支付订单id
     */
    default List<Long> selectPaymentIdByOrderId(Long orderId) {
        List<RelOrderPaymentDO> list = selectList(RelOrderPaymentDO::getOrderId, orderId);
        return CollectionUtils.convertList(list, RelOrderPaymentDO::getPaymentOrderId);
    }

    /**
     * 根据订单id查询支付订单数量
     * @param orderId
     * @return
     */
    default Long selectCountRelOrderPaymentByOrderId(Long orderId){
        return selectCount(new LambdaQueryWrapper<RelOrderPaymentDO>().eq(RelOrderPaymentDO::getOrderId, orderId));
    }
}