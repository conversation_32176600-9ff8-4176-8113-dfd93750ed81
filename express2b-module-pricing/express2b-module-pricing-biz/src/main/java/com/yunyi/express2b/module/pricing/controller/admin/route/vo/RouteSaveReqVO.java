package com.yunyi.express2b.module.pricing.controller.admin.route.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 快递全国运价基础新增/修改 Request VO")
@Data
public class RouteSaveReqVO {

    @Schema(description = "运费ID (主键)", requiredMode = Schema.RequiredMode.REQUIRED, example = "9371")
    private Long id;

    @Schema(description = "品牌标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "品牌标识不能为空")
    private String brandKey;

    @Schema(description = "线路标识（格式：品牌-始发地-目的地）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "线路标识（格式：品牌-始发地-目的地）不能为空")
    private String lineKey;

    @Schema(description = "始发地名称")
    private String sendProvince;

    @Schema(description = "目的地名称")
    private String receiveProvince;

    @Schema(description = "始发地adcode", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "始发地adcode不能为空")
    private String sendProvinceCode;

    @Schema(description = "目的地adcode", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "目的地adcode不能为空")
    private String receiveProvinceCode;

    @Schema(description = "产品名称")
    private String productCode;

    @Schema(description = "首重重量(g)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "首重重量(g)不能为空")
    private Integer firstWeight;

    @Schema(description = "续重重量(g)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "续重重量(g)不能为空")
    private Integer additionalWeight;

    @Schema(description = "供应首重成本价（分）", requiredMode = Schema.RequiredMode.REQUIRED, example = "22335")
    @NotNull(message = "供应首重成本价（分）不能为空")
    private Integer costFirstPrice;

    @Schema(description = "供应续重成本价（分）", requiredMode = Schema.RequiredMode.REQUIRED, example = "28776")
    @NotNull(message = "供应续重成本价（分）不能为空")
    private Integer costOverPrice;

    @Schema(description = "市场首重价格（分）", example = "24127")
    private Integer originalFirstPrice;

    @Schema(description = "市场续重价格（分）", example = "4647")
    private Integer originalOverPrice;

}