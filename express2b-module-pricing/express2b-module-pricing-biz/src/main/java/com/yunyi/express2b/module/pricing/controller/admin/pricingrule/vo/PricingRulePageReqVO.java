package com.yunyi.express2b.module.pricing.controller.admin.pricingrule.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 代理商定价（终端零售价）规则分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PricingRulePageReqVO extends PageParam {

    @Schema(description = "代理商ID", example = "31961")
    private Long agentId;

    @Schema(description = "加价类型 (1-比例加价，2-固定金额加价-默认)", example = "1")
    private Integer markupType;

    @Schema(description = "首重加价值 (比例或固定金额，单位是分)")
    private Integer firstWeightMarkup;

    @Schema(description = "续重加价值 (比例或固定金额，单位是分)")
    private Integer additionalWeightMarkup;

    @Schema(description = "品牌标识 (可为空，表示所有品牌)")
    private String brandKey;

    @Schema(description = "始发地adcode (可为空，表示所有始发地)")
    private String fromAdcode;

    @Schema(description = "目的地adcode (可为空，表示所有目的地)")
    private String toAdcode;

    @Schema(description = "状态 (0-禁用，1-启用)", example = "2")
    private Integer status;

    @Schema(description = "优先级 (数字越大优先级越高)")
    private Integer priority;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "租户编号", example = "20785")
    private Long tenantId;

}