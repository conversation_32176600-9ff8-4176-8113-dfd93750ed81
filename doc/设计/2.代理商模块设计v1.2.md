# 2.代理商模块设计v1.2

- 2025-05-10 
  - 修改部分DO记表结构
  - 增加一张代理商业绩统计表
  - 增加定义任务设计

# 代理商模块概要设计说明书

## 1\. 模块划分 (Module Division)

代理商模块 (`express2b-module-agent`) 是投流平台的核心模块之一，负责管理代理商从注册、认证、信息维护到等级升降、团队管理、以及部分与定价和钱包相关的配置与交互。

### 1.1 功能模块构成

根据 `框架设计说明书.md` 中的定义，代理商模块主要包含以下子模块：

- `express2b-module-agent-api`:
   - **功能**: 提供代理商模块对外暴露的接口契约、数据传输对象 (DTOs) 和枚举。这是其他模块与代理商模块交互的唯一入口。
   - **包结构**:
      - `com.yunyi.express2b.module.agent.api.dto`: 代理商相关数据传输对象 (DTOs)，例如 `AgentInfoDTO`, `AgentLevelDTO`, `AgentSelectedPricingTemplateDTO`。
      - `com.yunyi.express2b.module.agent.api.enums`: 代理商模块对外暴露的枚举，例如 `AgentStatusEnum`, `AgentLevelEnum`, `AgentErrorCodeConstants.java`。
      - `com.yunyi.express2b.module.agent.api.AgentApi.java`: 代理商服务核心接口定义。
- `express2b-module-agent-biz`:
   - **功能**: 代理商模块的业务逻辑实现层，包括对外 API 的具体实现、内部服务、控制器、数据持久化等。
   - **包结构**:
      - `com.yunyi.express2b.module.agent.biz.api`: 对 `agent-api` 模块中 `AgentApi` 接口的具体实现。
      - `com.yunyi.express2b.module.agent.biz.controller`: 负责处理 HTTP 请求，与前端或其他外部系统交互。
         - `com.yunyi.express2b.module.agent.biz.controller.admin`: 管理后台API控制器
         - `com.yunyi.express2b.module.agent.biz.controller.app.v1`: 前端API控制器v1版本
         - `vo`: 视图对象 (VOs)，用于 Controller 层与前端的数据交互，例如 `AgentProfileRespVO`, `AgentRegisterReqVO`, `AgentTeamStructureRespVO`。
      - `com.yunyi.express2b.module.agent.biz.service`: 核心业务逻辑服务，如代理商注册、登录、信息管理、等级计算、团队管理、定价模板选择等。
         - `impl`: 服务接口的具体实现。
      - `com.yunyi.express2b.module.agent.biz.convert`: 使用 MapStruct 实现 DO, DTO, VO 之间的转换。
      - `com.yunyi.express2b.module.agent.biz.dal`: 数据访问层。
         - `dataobject`: 代理商相关的数据实体对象 (DOs)，例如 `AgentDO`, `AgentLevelConfigDO`, `AgentRelationshipDO`, `AgentPricingTemplateSelectionDO`。
         - `mapper`: MyBatis Plus Mapper 接口。
      - `com.yunyi.express2b.module.agent.biz.job`: (可选) 定时任务，例如用于计算代理商业绩、检查升级条件等。
      - `com.yunyi.express2b.module.agent.biz.listener`: (可选) 事件监听器，例如监听用户注册成功事件，进行初始化操作。
### 1.2 模块关系

```mermaid
graph TD
    subgraph "外部系统/用户"
        Frontend["前端 (代理商管理后台/小程序)"]
        AdminPlatform["系统管理后台"]
    end

    subgraph "投流业务平台"
        AgentModule["express2b-module-agent (代理商模块)"]
        OrderModule["express2b-module-order (订单模块)"]
        PricingModule["express2b-module-pricing (定价模块)"]
        CommissionModule["express2b-module-commission (分润模块)"]
        WalletModule["express2b-module-wallet (钱包模块)"]
        AuthModule["统一认证模块 (如 Spring Security)"]
        SystemModule["express2b-module-system (系统模块)"]
    end

    Frontend -->|"API 请求 (VOs)"| AgentModule
    AdminPlatform -->|"API 请求 (VOs/DTOs)"| AgentModule

    AgentModule -->|"调用接口 (DTOs)"| OrderModule
    AgentModule -->|"调用接口 (DTOs)"| PricingModule
    AgentModule -->|"调用接口 (DTOs)"| CommissionModule
    AgentModule -->|"调用接口 (DTOs)"| WalletModule
    AgentModule -->|"调用接口"| AuthModule
    AgentModule -->|"调用接口 (DTOs)"| SystemModule

    OrderModule -->|"调用接口 (DTOs)"| AgentModule
    PricingModule -->|"调用接口 (DTOs)"| AgentModule
    CommissionModule -->|"调用接口 (DTOs)"| AgentModule
    WalletModule -->|"调用接口 (DTOs)"| AgentModule
```
### 1.3 模块职责

- **代理商生命周期管理**: 负责代理商的注册、登录、认证、信息修改、状态管理（启用、禁用）。
- **代理商等级管理**:
   - 根据个人业绩或团队业绩，实现代理商等级的自动升级（V1, V2, V3），遵循"只升不降"原则。
   - 提供代理商等级相关的配置读取和查询。
- **代理商团队管理 (分销链)**:
   - 维护代理商之间的推荐关系。
   - 提供团队结构查询、团队业绩统计等功能。
   - 生成邀请码/链接，用于发展下级代理。
- **代理商定价策略辅助**:
   - 允许代理商为其直接发展的下级选择平台提供的价格模板（包括默认等级模板和代理商专属的自定义模板）。
   - 允许代理商向平台申请自定义价格模板。
   - 存储和管理代理商选择的定价模板信息。
- **对外接口提供**: 向其他模块（如订单、分润、钱包）提供查询代理商基本信息、等级信息、选定价格模板等数据的接口。
- **与认证模块集成**: 负责代理商的身份认证和授权管理。
- **与系统模块集成**: 在代理商创建时，通过系统模块创建对应的租户，使代理商能够作为独立租户登录系统。

## 2\. 数据对象使用规范 (Data Object Usage Specification)

### 2.1 定义

- **领域对象 (Domain Object / Entity, DO)**:
   - **定义**: 与数据库表结构一一对应的 Java 对象，封装了核心业务数据和逻辑。
   - **使用要求**: 严格限制在 `biz` 模块的 `dal` 层和 `service` 层内部使用。**严禁跨模块传递，严禁在 Controller 层及以上层次使用。**
   - **核心概念**: 体现业务模型的实体，具有持久化能力。
- **数据传输对象 (Data Transfer Object, DTO)**:
   - **定义**: 用于模块与模块之间、或 `biz` 层内部不同服务间远距离调用的数据载体，通常不包含业务逻辑，只包含数据。
   - **使用要求**: 主要用于 `api` 子模块中定义的接口参数和返回值，以及 `biz` 模块内部跨服务方法调用的参数和返回值。
   - **核心概念**: 模块间通信的契约，关注数据的传输和解耦。
- **视图对象 (View Object, VO)**:
   - **定义**: 用于 `biz` 模块的 `controller` 层与前端（或其他HTTP客户端）之间的数据传输对象，也用于同一模块内 `service` 层向 `controller` 层返回专门为视图展示处理过的数据。
   - **使用要求**: 主要在 `controller` 层的方法参数和返回值中使用，以及 `service` 层返回给 `controller` 层的数据。
   - **核心概念**: 面向展示层的数据封装，可能包含一些前端需要的特定格式或合并字段。
   - **命名规则**:


   - 请求参数VO: `功能+ReqVO`，如 `AgentRegisterReqVO`
   - 响应结果VO: `功能+RespVO`，如 `AgentProfileRespVO`
### 2.2 代理商模块关键数据对象示例

#### 2.2.1 关键 DO (Domain Objects)

- `AgentDO` (代理商信息表)
   - `id`: Long, 主键
   - `memberId`: Long, 关联用户中心的用户ID（SSOUSERID）
   - `name`: String, 代理商名称/联系人
   - `mobile`: String, 手机号
   - `email`: String, 邮箱 (可选)
   - `status`: Integer, 状态 (例如: 0-待审核, 1-正常, 2-冻结) (对应 `AgentStatusEnum`)
   - `levelId`: Long, 当前等级ID (关联 `AgentLevelConfigDO`)
   - `referrerAgentId`: Long, 推荐人代理商ID (上级)
   - `customPricingTemplateId`: Long, (可选) 代理商申请并通过的专属自定义价格模板ID
   - `walletId`: String, 关联钱包模块的钱包ID
   - `systemTenantId`: Long, 关联系统模块的租户ID
   - `irectDownlineCount`: Integer
   - `totalDownlineCount`: Integer
   - `createTime`: LocalDateTime
   - `updateTime`: LocalDateTime
   - `creator`: String
   - `updater`: String
   - `deleted`: Boolean
   - `tenantId`: Long
   - ... 其他必要字段
- `AgentLevelConfigDO` (代理商等级配置表)
   - `id`: Long, 主键
   - `levelName`: String, 等级名称 (如 "V1", "V2", "V3") (对应 `AgentLevelEnum`)
   - `defaultPlatformPricingTemplateId`: Long, 此等级默认关联的平台价格模板ID
   - `upgradePersonalDailyOrders`: Integer, 升级到此等级所需个人连续3日日均订单量阈值
   - `upgradeTeamDailyOrders`: Integer, 升级到此等级所需团队连续3日日均订单量阈值
   - `upgradeObservationDays`: Integer, 业绩观察天数 (例如: 3)
   - `createTime`: LocalDateTime
   - `updateTime`: LocalDateTime
   - `creator`: String
   - `updater`: String
   - `deleted`: Boolean
   - `tenantId`: Long
   - ...
- `AgentSelectedPricingTemplateDO` (代理商为下级选择的定价模板记录)
   - `id`: Long, 主键
   - `superiorAgentId`: Long, 操作选择的上级代理商ID
   - `downlineAgentId`: Long, 被指定模板的下级代理商ID
   - `pricingTemplateId`: Long, 选择的价格模板ID (来自 `pricing` 模块的模板表)
   - `createTime`: LocalDateTime
   - `updateTime`: LocalDateTime
   - `creator`: String
   - `updater`: String
   - `deleted`: Boolean
   - `tenantId`: Long
- `AgentCustomTemplateRequestDO` (代理商自定义模板申请记录)
   - `id`: Long, 主键
   - `agentId`: Long, 申请代理商ID
   - `requestedTemplateDetails`: String (JSON), 申请的模板参数 (首重、续重等)
   - `status`: Integer (0-待审核, 1-已批准, 2-已拒绝)
   - `approvedTemplateId`: Long, (若批准) 关联到 `pricing` 模块中为此申请创建的模板ID
   - `requestedAt`: LocalDateTime
   - `reviewedAt`: LocalDateTime
   - `reviewerComments`: String
   - `createTime`: LocalDateTime
   - `updateTime`: LocalDateTime
   - `creator`: String
   - `updater`: String
   - `deleted`: Boolean
   - `tenantId`: Long
- `AgentDailyPerformanceStatDO`（代理商日业绩统计）
   - `id`: Long，主键
   - `agentId`: Long，代理商ID
   - `statDate`: LocalDate，统计日期
   - `personalOrderCount`: Integer，当日个人完成订单数
   - `teamOrderCount`: Integer，当日团队完成订单总数
   - `personalOrderAmount`: Integer，当日个人订单总金额（终端销售价，单位：分）
   - `teamOrderAmount`: Integer，当日团队订单总金额（单位：分）
   - `personalProfitAmount`: Integer，当日个人利润（终端销售价-平台零售价-红包，单位：分）
   - `teamProfitAmount`: Integer，当日团队利润（下级订单带来的利润，单位：分）
   - `commissionIncomeAmount`: Integer，当日获得的分润收入（单位：分）
   - `badDebtCount`: Integer，当日坏账订单数
   - `badDebtAmount`: Integer，当日坏账总金额（被扣除的金额，单位：分）
   - `couponDeductAmount`: Integer，当日红包核销总金额（代理商承担，单位：分）
   - `subsidyAmount`: Integer，当日平台补贴金额（如有，单位：分）
   - `withdrawalAmount`: Integer，当日提现金额（如有，单位：分）
   - `newCustomerCount`: Integer，当日新增客户数
   - `activeCustomerCount`: Integer，当日活跃客户数
   - `repeatCustomerCount`: Integer，当日复购客户数
   - `creator`: String，创建者ID
   - `createTime`: LocalDateTime，创建时间
   - `updater`: String，更新者ID
   - `updateTime`: LocalDateTime，更新时间
   - `deleted`: Boolean，逻辑删除标记
   - `tenantId`: Long，租户编号
#### 2.2.2 关键 DTO (Data Transfer Objects)

- `AgentInfoDTO` (用于其他模块获取代理商基本信息)
   - `agentId`: Long, 代理商ID
   - `name`: String, 代理商名称
   - `mobile`: String, 手机号
   - `status`: Integer, 状态 (`AgentStatusEnum`)
   - `levelId`: Long, 等级ID
   - `levelName`: String, 等级名称 (`AgentLevelEnum`)
   - `referrerAgentId`: Long, 推荐人ID
   - `walletId`: Long 钱包ID
- `AgentIdAndLevelDTO` (用于传递代理商ID和等级)
   - `agentId`: Long
   - `levelId`: Long
   - `levelName`: String
- `AgentSelectedPricingTemplateInfoDTO` (用于获取代理商为下级选择的模板信息)
   - `superiorAgentId`: Long
   - `downlineAgentId`: Long
   - `pricingTemplateId`: Long
   - `templateName`: String (需要从 `PricingModule` 获取)
- `CreateAgentRequestDTO` (用于内部服务创建代理商)
   - `name`: String
   - `mobile`: String
   - `password`: String (原始密码，Service层会加密)
   - `referrerAgentCode`: String (邀请码，可选)
- `UpdateAgentLevelDTO` (用于内部服务或事件更新代理商等级)
   - `agentId`: Long
   - `newLevelId`: Long
   - `reason`: String // 升级原因，例如 "PERSONAL_PERFORMANCE", "TEAM_PERFORMANCE"
#### 2.2.3 关键 VO (View Objects)

- `AgentProfileRespVO` (代理商个人信息视图)
   - `agentId`: Long
   - `name`: String
   - `mobile`: String
   - `email`: String
   - `status`: Integer (`AgentStatusEnum` 对应的描述)
   - `levelName`: String
   - `referrerAgentName`: String (可选)
   - `personalPerformance`: PerformanceRespVO (个人业绩VO，例如：`currentDailyOrders`, `daysToUpgrade`)
   - `teamPerformance`: PerformanceRespVO (团队业绩VO)
- `AgentRegisterReqVO` (代理商注册请求)
   - `name`: String, 姓名/昵称 (必填)
   - `mobile`: String, 手机号 (必填, 用于登录)
   - `password`: String, 密码 (必填)
   - `smsCode`: String, 短信验证码 (必填)
   - `inviteCode`: String, 邀请码 (可选)
- `AgentLoginReqVO` (代理商登录请求)
   - `mobile`: String, 手机号
   - `password`: String, 密码
- `AgentTeamMemberRespVO` (团队成员信息视图)
   - `agentId`: Long
   - `name`: String
   - `levelName`: String
   - `joinDate`: String (格式化日期)
   - `directDownlineCount`: Integer
   - `totalDownlineCount`: Integer
   - `recentOrderCount`: Integer (例如近7天订单，每日定时任务进行统计，保存在缓存中。)
- `SelectPricingTemplateForDownlineReqVO` (为下级选择定价模板请求)
   - `downlineAgentId`: Long (被指定的下级代理商ID)
   - `pricingTemplateId`: Long (选择的定价模板ID, 来源于代理商可用的模板列表)
- `RequestCustomPricingTemplateReqVO` (申请自定义定价模板请求)
   - `firstWeightKg`: Double, 首重重量
   - `firstWeightPrice`: Double, 首重的加价
   - `continuedWeightKg`: Double, 续重单位重量
   - `continuedWeightPrice`: Double, 续重单位的加价
   - `remarks`: String, 申请备注
- `AgentAvailablePricingTemplateRespVO` (代理商可用定价模板列表项)
   - `templateId`: Long
   - `templateName`: String
   - `description`: String (例如："V1 默认模板", "我的专属模板1")
   - `isCustom`: Boolean (是否为自定义模板)
   - `details`: PricingTemplateDetailsRespVO (首重、续重价格等)
   - idDefault：Boolean（是否为默认模板）
## 3\. 接口设计 (Interface Design)

### 3.1. 模块间接口 (`express2b-module-agent-api.AgentApi.java`)

以下是代理商模块对外提供的核心接口方法：

| 接口名称 (Interface Name) | 方法名称 (Method Name)                                                                     | 功能描述                                              | 输入参数 (Parameters)                                                                                    | 返回值 (Return Value)                                                                         | 可能抛出的异常                                                                             | 接口定义位置                                      |
|---------------------------|--------------------------------------------------------------------------------------------|-------------------------------------------------------|----------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------|---------------------------------------------------|
| `AgentApi`                | `getAgentInfo(Long agentId)`                                                               | 根据代理商ID获取其详细信息。                           | `agentId` (Long, 必须, 代理商ID)                                                                         | `AgentInfoDTO` (成功则返回代理商信息，若不存在或异常则返回null或抛异常)                        | `AgentException` (业务异常，使用`AGENT_NOT_FOUND`错误码)                                    | `com.yunyi.express2b.module.agent.api.AgentApi`   |
| `AgentApi`                | `getAgentBasicInfo(Long agentId)`                                                          | 根据代理商ID获取其基本信息（ID和等级）。                 | `agentId` (Long, 必须, 代理商ID)                                                                         | `AgentIdAndLevelDTO` (成功则返回，不存在则返回null或抛异常)                                    | `AgentException` (业务异常，使用`AGENT_NOT_FOUND`错误码)                                    | `com.yunyi.express2b.module.agent.api.AgentApi`   |
| `AgentApi`                | `getAgentLevelId(Long agentId)`                                                            | 根据代理商ID获取其等级ID。                             | `agentId` (Long, 必须, 代理商ID)                                                                         | `Long` (等级ID，若代理商不存在或无等级则返回null或抛异常)                                      | `AgentException` (业务异常，使用`AGENT_NOT_FOUND`错误码)                                    | `com.yunyi.express2b.module.agent.api.AgentApi`   |
| `AgentApi`                | `getReferrerAgentId(Long agentId)`                                                         | 根据代理商ID获取其直接推荐人ID。                       | `agentId` (Long, 必须, 代理商ID)                                                                         | `Long` (推荐人代理商ID，若无推荐人或代理商不存在则返回null)                                    | `AgentException` (业务异常，使用`AGENT_NOT_FOUND`错误码)                                    | `com.yunyi.express2b.module.agent.api.AgentApi`   |
| `AgentApi`                | `getDirectDownlineIds(Long agentId)`                                                       | 获取某代理商的所有直接下级代理商ID列表。               | `agentId` (Long, 必须, 代理商ID)                                                                         | `List<Long>` (直接下级ID列表，可能为空列表)                                                    | `AgentException` (业务异常，使用`AGENT_NOT_FOUND`错误码)                                    | `com.yunyi.express2b.module.agent.api.AgentApi`   |
| `AgentApi`                | `getSelectedPricingTemplateForDownline(Long superiorAgentId, Long downlineAgentId)`        | 获取上级为特定下级选择的价格模板ID。                   | `superiorAgentId` (Long, 必须, 上级代理ID), `downlineAgentId` (Long, 必须, 下级代理ID)                   | `AgentSelectedPricingTemplateInfoDTO` (选择的模板信息，若未选择或用户不存在则返回null或抛异常) | `AgentException` (业务异常，使用`AGENT_NOT_FOUND`或`TEMPLATE_NOT_SELECTED`错误码)           | `com.yunyi.express2b.module.agent.api.AgentApi`   |
| `AgentApi`                | `createAgent(CreateAgentRequestDTO request)`                                               | （内部或后台调用）创建新代理商。                         | `request` (`CreateAgentRequestDTO`, 必须, 包含注册信息)                                                  | `Long` (新创建的代理商ID)                                                                     | `AgentException` (业务异常，使用`AGENT_MOBILE_ALREADY_EXISTS`或`INVITE_CODE_INVALID`错误码) | `com.yunyi.express2b.module.agent.api.AgentApi`   |
| `AgentApi`                | `updateAgentLevel(UpdateAgentLevelDTO request)`                                            | （内部或定时任务调用）更新代理商等级。                   | `request` (`UpdateAgentLevelDTO`, 必须, 包含代理商ID和新等级ID)                                          | `void`                                                                                        | `AgentException` (业务异常，使用`AGENT_NOT_FOUND`或`INVALID_LEVEL_TRANSITION`错误码)        | `com.yunyi.express2b.module.agent.api.AgentApi`   |
| `AgentApi`                | `recordAgentDailyOrders(Long agentId, LocalDate date, int personalOrders, int teamOrders)` | 记录代理商某日的个人和团队订单量（供等级计算服务调用）。 | `agentId` (Long, 必须), `date` (LocalDate, 必须), `personalOrders` (int, 必须), `teamOrders` (int, 必须) | `void`                                                                                        | `AgentException` (业务异常，使用`AGENT_NOT_FOUND`错误码)                                    | `com.yunyi.express2b.module.agent.api.AgentApi`   |
| `AgentApi`                | `getAgentCustomPricingTemplateId(Long agentId)`                                            | 获取代理商绑定的专属自定义价格模板ID。                 | `agentId` (Long, 必须, 代理商ID)                                                                         | `Long` (自定义价格模板ID，若无则返回null)                                                      | `AgentException` (业务异常，使用`AGENT_NOT_FOUND`错误码)                                    | `com.yunyi.express2b.module.agent.api.AgentApi`   |
| `AgentApi`                | `associateWallet(Long agentId, String walletId)`                                           | 关联代理商与其钱包ID。                                 | `agentId` (Long, 必须, 代理商ID), `walletId` (String, 必须, 钱包ID)                                      | `void`                                                                                        | `AgentException` (业务异常，使用`AGENT_NOT_FOUND`或`WALLET_ALREADY_ASSOCIATED`错误码)       | `com.yunyi.express2b.module.agent.api.AgentApi`   |
| `SystemApi`               | `createTenant(CreateTenantDTO request)`                                                    | 为新创建的代理商创建对应的系统租户。                   | `request` (`CreateTenantDTO`, 必须, 包含租户名称、管理员信息等)                                           | `Long` (新创建的租户ID)                                                                       | `SystemException` (业务异常，使用`TENANT_NAME_ALREADY_EXISTS`等错误码)                      | `com.yunyi.express2b.module.system.api.SystemApi` |

### 3.2. 对外接口 (API 设计 - Controller 层)

| 接口路径 (API Path)                                      | HTTP 方法 | 功能描述                                      | 请求参数 (Request Parameters)                                                                                                     | 响应 (Response)                                                                                                                                                                                                                                   | 认证/授权要求                    |
|----------------------------------------------------------|-----------|-----------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------|
| `/v1/agents/register`                                    | `POST`    | 代理商注册。                                   | **请求体 (JSON)**: `AgentRegisterReqVO` (包含 `name`, `mobile`, `password`, `smsCode`, `inviteCode` (可选))                       | **响应状态码**: 201 Created (成功时HTTP状态码), 400 Bad Request, 409 Conflict <br> **响应体 (JSON)**: `CommonResult<AgentRegisteredRespVO>` (成功时 `data` 可包含 `agentId` 和 `token`)。失败时 `code` 为错误码，`msg` 为错误信息，`data` 为 `null`。 | 无需认证                         |
| `/v1/agents/login`                                       | `POST`    | 代理商登录。                                   | **请求体 (JSON)**: `AgentLoginReqVO` (包含 `mobile`, `password`)                                                                  | **响应状态码**: 200 OK, 400 Bad Request, 401 Unauthorized <br> **响应体 (JSON)**: `CommonResult<LoginSuccessRespVO>` (成功时 `data` 包含 `token` 和 `agentProfile`)。失败时 `code` 为错误码，`msg` 为错误信息。                                      | 无需认证                         |
| `/v1/agents/logout`                                      | `POST`    | 代理商登出。                                   | 无                                                                                                                                | **响应状态码**: 200 OK <br> **响应体 (JSON)**: `CommonResult<Void>` (成功时 `data` 为 `null`)。                                                                                                                                                    | 需要认证 (Agent Token)           |
| `/v1/agents/profiles`                                    | `GET`     | 获取当前登录代理商的个人信息。                 | 无                                                                                                                                | **响应状态码**: 200 OK, 401 Unauthorized <br> **响应体 (JSON)**: `CommonResult<AgentProfileRespVO>`。                                                                                                                                              | 需要认证 (Agent Token)           |
| `/v1/agents/profiles`                                    | `PUT`     | 更新当前登录代理商的个人信息。                 | **请求体 (JSON)**: `UpdateAgentProfileReqVO` (包含可修改的字段如 `name`, `email`)                                                 | **响应状态码**: 200 OK, 400 Bad Request, 401 Unauthorized <br> **响应体 (JSON)**: `CommonResult<AgentProfileRespVO>` (返回更新后的信息)。                                                                                                          | 需要认证 (Agent Token)           |
| `/v1/agents/teams/structures`                            | `GET`     | 获取当前代理商的团队结构（例如直接下级）。       | **查询参数**: `pageNo` (int, 可选, 页码), `pageSize` (int, 可选, 每页数量),`levelDepth` (int, 可选, 查询层级深度, 默认1)          | **响应状态码**: 200 OK, 401 Unauthorized <br> **响应体 (JSON)**: `CommonResult<PageResult<AgentTeamMemberRespVO>>` 或 `CommonResult<List<AgentTeamMemberRespVO>>`。                                                                                | 需要认证 (Agent Token)           |
| `/v1/agents/teams/invite-infos`                          | `GET`     | 获取当前代理商的邀请码和邀请链接。             | 无                                                                                                                                | **响应状态码**: 200 OK, 401 Unauthorized <br> **响应体 (JSON)**: `CommonResult<InviteInfoRespVO>` (data 包含 `inviteCode` 和 `inviteLink`)。                                                                                                       | 需要认证 (Agent Token)           |
| `/v1/agents/pricings/available-templates`                | `GET`     | 获取当前代理商可用于发展下级的价格模板列表。   | 无                                                                                                                                | **响应状态码**: 200 OK, 401 Unauthorized <br> **响应体 (JSON)**: `CommonResult<List<AgentAvailablePricingTemplateRespVO>>`。                                                                                                                       | 需要认证 (Agent Token)           |
| `/v1/agents/pricings/select-template-for-downline`       | `POST`    | 为新发展的下级或已存在的直接下级选择价格模板。 | **请求体 (JSON)**: `SelectPricingTemplateForDownlineReqVO` (包含 `downlineAgentId` (如果是已存在的下级), `pricingTemplateId`)     | **响应状态码**: 200 OK (或 201 Created), 400 Bad Request, 401 Unauthorized, 403 Forbidden, 404 Not Found <br> **响应体 (JSON)**: `CommonResult<Void>` (成功提示) 或 `CommonResult<UpdatedDownlineInfoRespVO>` (返回更新后的下级信息)。             | 需要认证 (Agent Token)           |
| `/v1/agents/pricings/custom-templates/request`           | `POST`    | 代理商申请自定义价格模板。                     | **请求体 (JSON)**: `RequestCustomPricingTemplateReqVO` (包含模板的定价细则和备注)                                                 | **响应状态码**: 202 Accepted, 400 Bad Request, 401 Unauthorized <br> **响应体 (JSON)**: `CommonResult<RequestSubmittedRespVO>` (成功时 `data` 可包含申请ID)。                                                                                      | 需要认证 (Agent Token)           |
| `/v1/agents/pricings/custom-templates/requests`          | `GET`     | 查看当前代理商的自定义模板申请记录。           | **查询参数**: `pageNo` (int, 可选), `pageSize` (int, 可选), `status` (int, 可选, 按状态筛选)                                      | **响应状态码**: 200 OK, 401 Unauthorized <br> **响应体 (JSON)**: `CommonResult<PageResult<CustomTemplateRequestSummaryRespVO>>`。                                                                                                                  | 需要认证 (Agent Token)           |
| `/v1/agents/levels/progress`                             | `GET`     | 获取当前代理商的等级信息及升级进度。           | 无                                                                                                                                | **响应状态码**: 200 OK, 401 Unauthorized <br> **响应体 (JSON)**: `CommonResult<AgentLevelProgressRespVO>`。                                                                                                                                        | 需要认证 (Agent Token)           |
| `/agents/{agentId}/custom-templates/{requestId}/approve` | `POST`    | (管理后台API) 批准代理商的自定义模板申请。     | **路径参数**: `agentId`, `requestId` <br> **请求体 (JSON)**: (可选) `ApproveTemplateRequestReqVO` (包含批准备注，最终确认的模板ID) | **响应状态码**: 200 OK, 400 Bad Request, 404 Not Found <br> **响应体 (JSON)**: `CommonResult<Void>` (成功提示)。                                                                                                                                   | 需要认证 (Admin Token, 特定权限) |
| `/agents/{agentId}/custom-templates/{requestId}/reject`  | `POST`    | (管理后台API) 拒绝代理商的自定义模板申请。     | **路径参数**: `agentId`, `requestId` <br> **请求体 (JSON)**: `RejectTemplateRequestReqVO` (必须, 包含拒绝原因)                    | **响应状态码**: 200 OK, 400 Bad Request, 404 Not Found <br> **响应体 (JSON)**: `CommonResult<Void>` (成功提示)。                                                                                                                                   | 需要认证 (Admin Token, 特定权限) |

**Controller 包路径约定:**

- 面向终端用户的 Controller (URL 前缀 `/app-api`) 应位于 `com.yunyi.express2b.module.agent.controller.app` 包下。
   - 版本化的接口（如 `/v1/`）应在该包下创建对应的版本子目录，例如 `com.yunyi.express2b.module.agent.controller.app.v1`。
- 面向管理后台的 Controller (URL 前缀 `/admin-api`) 应位于 `com.yunyi.express2b.module.agent.controller.admin` 包下。

### 3.3. 接口契约

- **版本管理**:
   - 前端接口 (`app-api`) 通过在包路径和 URL 路径中使用版本号（如 `v1`, `v2`）进行管理，例如 `com.yunyi.express2b.module.agent.controller.app.v1` 和 `/app-api/v1/...`。
   - 后端接口 (`admin-api`) 的版本管理策略待定，当前统一使用 `/admin-api/...` 前缀。
- **稳定性**: `agent-api` 模块中的 DTO 和接口定义一旦发布，应尽量保持稳定。如有不兼容变更，前端接口应通过提升版本号处理。
- **统一响应格式**: 所有 Controller 接口统一返回 `CommonResult<T>` 对象（定义于 `com.yunyi.express2b.framework.common.pojo.CommonResult`）。
   - `code`: `Integer` 类型，表示业务状态码。成功时为预定义的成功代码（例如 0 或 20000），失败时为具体的错误代码（来自动模块的 `AgentErrorCodeConstants` 或全局的 `GlobalErrorCodeConstants`）。
   - `data`: `T` 类型，泛型数据字段，仅在请求成功且有数据返回时包含业务数据。
   - `msg`: `String` 类型，描述信息。成功时通常为 "success" 或具体成功信息，失败时为面向用户的错误提示信息。
- **错误处理**:
   - Controller 层应通过全局异常处理器统一捕获业务异常（自定义异常）和系统异常，并转换为 `CommonResult.error(...)` 形式返回。
   - `AgentErrorCodeConstants.java` 中定义模块相关的业务错误码及其对应的 `msg`。


```java
/**
 * 代理商模块异常
 */
public class AgentException extends BusinessException {
    public AgentException(Integer code, String message) {
        super(code, message);
    }
    
    public AgentException(AgentErrorCodeConstants errorCode) {
        super(errorCode.getCode(), errorCode.getMessage());
    }
}

/**
 * 代理商模块错误码 2-10-xxx
 */
public enum AgentErrorCodeConstants implements ErrorCode {
    /**
     * 代理商不存在
     */
    AGENT_NOT_FOUND(2_10_001, "代理商不存在"),
    
    /**
     * 手机号已被使用
     */
    AGENT_MOBILE_ALREADY_EXISTS(2_10_002, "手机号已被注册"),
    
    /**
     * 邀请码无效
     */
    INVITE_CODE_INVALID(2_10_003, "邀请码无效"),
     
    /**
     * 非法的等级变更操作
     */
    INVALID_LEVEL_TRANSITION(2_10_004, "非法的等级变更操作"),
    
    /**
     * 未选择定价模板
     */
    TEMPLATE_NOT_SELECTED(2_10_005, "未选择定价模板"),
    
    /**
     * 钱包已被关联
     */
    WALLET_ALREADY_ASSOCIATED(2_10_006, "钱包已被关联"),
    
    /**
     * 租户创建失败
     */
    TENANT_CREATE_FAILED(2_10_007, "租户创建失败"),
    
    /**
     * 租户已存在
     */
    TENANT_ALREADY_EXISTS(2_10_008, "租户已存在");
    
    private final Integer code;
    private final String message;
    
    AgentErrorCodeConstants(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    
    @Override
    public Integer getCode() {
        return code;
    }
    
    @Override
    public String getMessage() {
        return message;
    }
}
```
 
- **RESTful 风格**: 接口设计遵循 RESTful 规范。
- **幂等性设计**: 对于创建、更新、删除等操作，需要考虑接口的幂等性。
- **安全控制**: 根据接口是 `app-api` 还是 `admin-api`，以及具体业务需求，实施不同的安全认证和授权机制（如 JWT Token 校验、权限注解等）。
- **接口文档**: 使用 Swagger (Knife4j) 自动生成 API 文档，并确保所有接口、参数、返回对象都有清晰的注释说明。

```java
@Operation(summary = "代理商注册", description = "创建新的代理商账户")
@PostMapping("/v1/agents/register")
@ApiResponses({
    @ApiResponse(code = 200, message = "操作成功", response = CommonResult.class),
    @ApiResponse(code = 400, message = "参数校验失败"),
    @ApiResponse(code = 409, message = "手机号已存在")
})
public CommonResult<AgentRegisteredRespVO> registerAgent(@RequestBody @Valid AgentRegisterReqVO reqVO) {
    // 方法实现
}
```
 
## 4\. 数据流和交互 (Data Flow and Interaction)

### 4.1 代理商注册并由上级指定价格模板

```mermaid
sequenceDiagram
    participant User as "用户"
    participant Frontend as "代理商H5/小程序"
    participant AgentController as "Agent模块Controller"
    participant AgentService as "Agent模块Service"
    participant AgentDAL as "Agent模块DAL"
    participant AuthModule as "认证模块"
    participant PricingApi as "Pricing模块Api"
    participant SystemApi as "System模块Api"

    User->>Frontend: 填写注册信息 (含邀请码 InviteCodeA)
    Frontend->>AgentController: POST /v1/agents/register (AgentRegisterReqVO)
    AgentController->>AgentService: registerAgent(AgentRegisterDTO)
    AgentService->>AgentDAL: findByMobile(mobile)
    alt 手机号已存在
        AgentDAL-->>AgentService: AgentDO
        AgentService-->>AgentController: throw AgentException(AGENT_MOBILE_ALREADY_EXISTS)
        AgentController-->>Frontend: 返回 CommonResult.error(AGENT_MOBILE_ALREADY_EXISTS)
    else 手机号可用
        AgentDAL-->>AgentService: null
        AgentService->>AgentDAL: findAgentByInviteCode(InviteCodeA)
        AgentDAL-->>AgentService: ReferrerAgentDO (上级代理A)
        AgentService->>PricingApi: getSelectedTemplateByInviteCode(InviteCodeA)
        PricingApi-->>AgentService: PricingTemplateDTO (模板T)
        AgentService->>AuthModule: encryptPassword(password)
        AuthModule-->>AgentService: hashedPassword
        AgentService->>AgentDAL: saveAgent(AgentDO: 新用户B, referrerId=A.id, status=ACTIVE)
        AgentDAL-->>AgentService: newAgentB_DO (含ID)
        AgentService->>AgentDAL: saveAgentSelectedPricingTemplate(AgentSelectedPricingTemplateDO: superiorId=A.id, downlineId=B.id, templateId=模板T)
        AgentDAL-->>AgentService: success
        
        AgentService->>SystemApi: createTenant(CreateTenantDTO: name=B.name, contactName=B.name, contactMobile=B.mobile, username=B.mobile, password=password, agentId=B.id)
        SystemApi-->>AgentService: tenantId
        AgentService->>AgentDAL: updateAgentTenantId(B.id, tenantId)
        AgentDAL-->>AgentService: success
        
        AgentService->>AuthModule: generateToken(newAgentB_DO.id)
        AuthModule-->>AgentService: jwtToken
        AgentService-->>AgentController: AgentRegisteredRespVO (含token, agentId)
        AgentController-->>Frontend: 返回 CommonResult<AgentRegisteredRespVO>
        Frontend-->>User: 注册成功，自动登录
    end
```
*流程说明：上述流程简化了邀请码与模板的关联，实际可能是在生成邀请码时就已绑定了上级选择的模板ID。*

### 4.2 代理商升级检查流程 (定时任务)

```mermaid
sequenceDiagram
    participant Scheduler as "定时任务调度器"
    participant UpgradeJob as "AgentLevelUpgradeCheckJob"
    participant AgentDAL as "Agent模块DAL"
    participant StatDAL as "业绩统计表DAL"
    participant LevelConfigDAL as "等级配置DAL"
    participant AgentApi as "AgentApi (升级接口)"

    Scheduler->>UpgradeJob: "每天零点后触发升级检查任务"
    UpgradeJob->>AgentDAL: "查询所有活跃代理商列表"
    loop "遍历每个代理商"
        UpgradeJob->>StatDAL: "查询近N天日业绩统计数据"
        UpgradeJob->>LevelConfigDAL: "获取升级条件配置"
        UpgradeJob->>UpgradeJob: "判定是否满足升级条件"
        alt "满足升级条件"
            UpgradeJob->>AgentApi: "调用升级接口(AgentApi.updateAgentLevel)"
            UpgradeJob->>AgentDAL: "记录升级日志/状态"
        end
    end
    UpgradeJob-->>Scheduler: "升级检查完成"
```

> 说明：
> - "AgentLevelUpgradeCheckJob" 依赖本模块的 "agent_daily_performance_stat" 作为业绩数据来源，依赖 "agent_level_config" 作为升级条件配置。
> - 升级接口调用本模块API，升级结果可用于后续通知、数据看板等。

### 4.3 代理商申请自定义价格模板

```mermaid
sequenceDiagram
    participant AgentFrontend as "代理商管理后台"
    participant AgentController as "Agent模块Controller"
    participant AgentService as "Agent模块Service"
    participant AgentDAL as "Agent模块DAL"
    participant AdminNotificationService as "管理员通知服务"

    AgentFrontend->>AgentController: POST /v1/agents/pricings/custom-templates/request (RequestCustomPricingTemplateReqVO)
    AgentController->>AgentService: requestCustomPricingTemplate(agentId, RequestCustomPricingTemplateDTO)
    AgentService->>AgentDAL: saveCustomTemplateRequest(AgentCustomTemplateRequestDO: agentId, details=dto, status=PENDING)
    AgentDAL-->>AgentService: AgentCustomTemplateRequestDO (含requestId)
    AgentService->>AdminNotificationService: notifyAdminNewTemplateRequest(requestId, agentId)
    AdminNotificationService-->>AgentService: ack
    AgentService-->>AgentController: RequestSubmittedRespVO (requestId)
    AgentController-->>AgentFrontend: 返回 CommonResult<RequestSubmittedRespVO>
```
## 5\. 数据库设计概要 (Database Design Overview)

基于上述DO的定义，代理商模块的核心数据表如下：

1. `agent_agent_profile` (代理商档案表)

| 字段名 | 类型 | 说明 |
|-----------------------------|----------------|--------------------------------------------------------------|
| id | BIGINT, PK, AI | 代理商唯一标识 |
| member_id | BIGINT | 关联用户中心的用户ID（SSOUSERID），DO中为memberId |
| name | VARCHAR(100) | 代理商名称/联系人 |
| mobile | VARCHAR(20) | 手机号，唯一，用于登录和联系 |
| email | VARCHAR(100) | 邮箱（可选） |
| status | TINYINT | 状态（0-待审核, 1-正常, 2-冻结），对应AgentStatusEnum |
| level_id | BIGINT | 当前等级ID，关联agent_level_config.id，DO中为levelId |
| referrer_agent_id | BIGINT | 推荐人代理商ID（上级），DO中为referrerAgentId |
| custom_pricing_template_id | BIGINT | 代理商专属自定义价格模板ID，DO中为customPricingTemplateId |
| wallet_id | VARCHAR(64) | 关联钱包模块的钱包ID |
| system_tenant_id | BIGINT | 关联系统模块的租户ID |
| direct_downline_count | INT | 直接下级数量，DO中为irectDownlineCount（建议修正为directDownlineCount）|
| total_downline_count | INT | 团队总下级数量，DO中为totalDownlineCount |
| creator | VARCHAR(64) | 创建者ID |
| create_time | DATETIME | 创建时间 |
| updater | VARCHAR(64) | 更新者ID |
| update_time | DATETIME | 更新时间 |
| deleted | BIT(1) | 逻辑删除标记 |
| tenant_id | BIGINT | 租户编号 |
**修正说明2025-05-10：**
- 字段名需与DO保持一致，如member_id、level_id、custom_pricing_template_id等。
- 增加direct_downline_count、total_downline_count字段，便于团队统计。
- password_hash、salt等字段应由认证模块管理，表结构中可去除，仅保留业务相关字段。
2. `agent_level_config` (代理商等级配置表)

| 字段名 | 类型 | 说明 |
|----------------------------------|----------------|--------------------------------------------------------------|
| id | BIGINT, PK, AI | 等级配置ID |
| level_name | VARCHAR(50) | 等级名称（如"V1"、"V2"、"V3"），DO中为levelName |
| default_platform_pricing_template_id | BIGINT | 此等级默认关联的平台价格模板ID，DO中为defaultPlatformPricingTemplateId |
| upgrade_personal_daily_orders | INT | 升级到此等级所需个人连续3日日均订单量阈值，DO中为upgradePersonalDailyOrders |
| upgrade_team_daily_orders | INT | 升级到此等级所需团队连续3日日均订单量阈值，DO中为upgradeTeamDailyOrders |
| upgrade_observation_days | INT | 业绩观察天数，DO中为upgradeObservationDays |
| creator | VARCHAR(64) | 创建者ID |
| create_time | DATETIME | 创建时间 |
| updater | VARCHAR(64) | 更新者ID |
| update_time | DATETIME | 更新时间 |
| deleted | BIT(1) | 逻辑删除标记 |
| tenant_id | BIGINT | 租户编号 |
**修正说明2025-05-10：**
- 字段名与DO保持一致，level_name、upgrade_personal_daily_orders等。
- level_enum字段可选，若仅用level_name区分可省略。

3. `agent_selected_pricing_template` (代理商为下级选择的价格模板记录表)

| 字段名 | 类型 | 说明 |
|--------------------|----------------|--------------------------------------------------------------|
| id | BIGINT, PK, AI | 主键 |
| superior_agent_id | BIGINT | 上级代理商ID，DO中为superiorAgentId |
| downline_agent_id | BIGINT | 被指定模板的下级代理商ID，DO中为downlineAgentId |
| pricing_template_id| BIGINT | 选择的价格模板ID，DO中为pricingTemplateId |
| creator | VARCHAR(64) | 创建者ID |
| create_time | DATETIME | 创建时间 |
| updater | VARCHAR(64) | 更新者ID |
| update_time | DATETIME | 更新时间 |
| deleted | BIT(1) | 逻辑删除标记 |
| tenant_id | BIGINT | 租户编号 |
4. `agent_custom_template_request` (代理商自定义价格模板申请表)

| 字段名 | 类型 | 说明 |
|--------------------------|----------------|--------------------------------------------------------------|
| id | BIGINT, PK, AI | 主键 |
| agent_id | BIGINT | 申请代理商ID，DO中为agentId |
| requested_template_details | TEXT | 申请的模板参数（JSON），DO中为requestedTemplateDetails |
| status | TINYINT | 申请状态（0-待审核, 1-已批准, 2-已拒绝），DO中为status |
| approved_template_id | BIGINT | 若批准，关联到pricing模块中为此申请创建的模板ID，DO中为approvedTemplateId |
| requested_at | DATETIME | 申请时间，DO中为requestedAt |
| reviewed_at | DATETIME | 审核时间，DO中为reviewedAt |
| reviewer_comments | VARCHAR(500) | 审核备注，DO中为reviewerComments |
| remarks | VARCHAR(500) | 申请备注，DO中为remarks |
| creator | VARCHAR(64) | 创建者ID |
| create_time | DATETIME | 创建时间 |
| updater | VARCHAR(64) | 更新者ID |
| update_time | DATETIME | 更新时间 |
| deleted | BIT(1) | 逻辑删除标记 |
| tenant_id | BIGINT | 租户编号 |
5. `agent_daily_performance_stat` (代理商日业绩统计表)

| 字段名                    | 类型             | 说明                       |
|---------------------------|------------------|----------------------------|
| id                        | BIGINT, PK, AI   | 主键                       |
| agent_id                  | BIGINT           | 代理商ID                   |
| stat_date                 | DATE             | 统计日期                   |
| personal_order_count      | INT              | 当日个人完成订单数         |
| team_order_count          | INT              | 当日团队完成订单总数       |
| personal_order_amount     | INT              | 个人订单总金额（终端销售价，单位：分）|
| team_order_amount         | INT              | 团队订单总金额（单位：分）             |
| personal_profit_amount    | INT              | 个人利润（终端销售价-平台零售价-红包，单位：分）|
| team_profit_amount        | INT              | 团队利润（下级订单带来的利润，单位：分）|
| commission_income_amount  | INT              | 分润收入（单位：分）                   |
| bad_debt_count            | INT              | 坏账订单数                 |
| bad_debt_amount           | INT              | 坏账总金额（单位：分）                 |
| coupon_deduct_amount      | INT              | 红包核销金额（单位：分）               |
| subsidy_amount            | INT              | 平台补贴金额（单位：分）               |
| withdrawal_amount         | INT              | 提现金额（单位：分）                   |
| new_customer_count        | INT              | 新增客户数                 |
| active_customer_count     | INT              | 活跃客户数                 |
| repeat_customer_count     | INT              | 复购客户数                 |
| creator                   | VARCHAR(64)      | 创建者ID                   |
| create_time               | DATETIME         | 创建时间                   |
| updater                   | VARCHAR(64)      | 更新者ID                   |
| update_time               | DATETIME         | 更新时间                   |
| deleted                   | BIT(1)           | 逻辑删除标记               |
| tenant_id                 | BIGINT           | 租户编号                   |

**设计理由补充：**
- 为满足代理商升级、分润、钱包结算、数据看板、团队激励、财务核算等多维度业务需求，建议将日业绩统计表扩展为多维度业绩与收益统计表，涵盖订单金额、利润、分润、坏账、红包、客户等关键指标，为后续精细化运营和数据分析提供坚实的数据基础。
```mermaid
erDiagram
    agent_agent_profile {
        BIGINT id PK "主键"
        BIGINT member_id "用户中心用户ID"
        VARCHAR(100) name "代理商名称/联系人"
        VARCHAR(20) mobile "手机号"
        VARCHAR(100) email "邮箱"
        TINYINT status "状态"
        BIGINT level_id "当前等级ID"
        BIGINT referrer_agent_id "推荐人代理商ID"
        BIGINT custom_pricing_template_id "专属自定义价格模板ID"
        VARCHAR(64) wallet_id "钱包ID"
        BIGINT system_tenant_id "系统租户ID"
        INT direct_downline_count "直接下级数量"
        INT total_downline_count "团队总下级数量"
        VARCHAR(64) creator "创建者ID"
        DATETIME create_time "创建时间"
        VARCHAR(64) updater "更新者ID"
        DATETIME update_time "更新时间"
        BIT(1) deleted "逻辑删除"
        BIGINT tenant_id "租户编号"
    }

    agent_level_config {
        BIGINT id PK "主键"
        VARCHAR(50) level_name "等级名称"
        BIGINT default_platform_pricing_template_id "默认平台价格模板ID"
        INT upgrade_personal_daily_orders "升级所需个人日均订单"
        INT upgrade_team_daily_orders "升级所需团队日均订单"
        INT upgrade_observation_days "业绩观察天数"
        VARCHAR(64) creator "创建者ID"
        DATETIME create_time "创建时间"
        VARCHAR(64) updater "更新者ID"
        DATETIME update_time "更新时间"
        BIT(1) deleted "逻辑删除"
        BIGINT tenant_id "租户编号"
    }

    agent_relationship {
        BIGINT id PK "主键"
        BIGINT ancestor_id "上级代理商ID"
        BIGINT descendant_id "下级代理商ID"
        INT depth "层级深度"
    }

    agent_selected_pricing_template {
        BIGINT id PK "主键"
        BIGINT superior_agent_id "上级代理商ID"
        BIGINT downline_agent_id "下级代理商ID"
        BIGINT pricing_template_id "价格模板ID"
        VARCHAR(64) creator "创建者ID"
        DATETIME create_time "创建时间"
        VARCHAR(64) updater "更新者ID"
        DATETIME update_time "更新时间"
        BIT(1) deleted "逻辑删除"
        BIGINT tenant_id "租户编号"
    }

    agent_custom_template_request {
        BIGINT id PK "主键"
        BIGINT agent_id "申请代理商ID"
        TEXT requested_template_details "申请模板参数"
        TINYINT status "申请状态"
        BIGINT approved_template_id "批准后模板ID"
        DATETIME requested_at "申请时间"
        DATETIME reviewed_at "审核时间"
        VARCHAR(500) reviewer_comments "审核备注"
        VARCHAR(500) remarks "申请备注"
        VARCHAR(64) creator "创建者ID"
        DATETIME create_time "创建时间"
        VARCHAR(64) updater "更新者ID"
        DATETIME update_time "更新时间"
        BIT(1) deleted "逻辑删除"
        BIGINT tenant_id "租户编号"
    }

    agent_daily_performance_stat {
        BIGINT id PK "主键"
        BIGINT agent_id "代理商ID"
        DATE stat_date "统计日期"
        INT personal_order_count "个人订单数"
        INT team_order_count "团队订单数"
        INT personal_order_amount "个人订单金额（分）"
        INT team_order_amount "团队订单金额（分）"
        INT personal_profit_amount "个人利润（分）"
        INT team_profit_amount "团队利润（分）"
        INT commission_income_amount "分润收入（分）"
        INT bad_debt_count "坏账订单数"
        INT bad_debt_amount "坏账金额（分）"
        INT coupon_deduct_amount "红包核销金额（分）"
        INT subsidy_amount "平台补贴金额（分）"
        INT withdrawal_amount "提现金额（分）"
        INT new_customer_count "新增客户数"
        INT active_customer_count "活跃客户数"
        INT repeat_customer_count "复购客户数"
        VARCHAR(64) creator "创建者ID"
        DATETIME create_time "创建时间"
        VARCHAR(64) updater "更新者ID"
        DATETIME update_time "更新时间"
        BIT(1) deleted "逻辑删除"
        BIGINT tenant_id "租户编号"
    }

    system_tenant {
        BIGINT id PK "主键"
        VARCHAR(100) name "租户名称"
        VARCHAR(100) contact_name "联系人"
        VARCHAR(20) contact_mobile "联系电话"
        TINYINT status "状态"
    }

    agent_agent_profile ||--o{ agent_relationship : "“团队关系”"
    agent_agent_profile ||--o{ agent_selected_pricing_template : "“为下级选择模板”"
    agent_agent_profile ||--o{ agent_custom_template_request : "“自定义模板申请”"
    agent_agent_profile ||--o{ agent_daily_performance_stat : "“日业绩统计”"
    agent_agent_profile }|--|| agent_level_config : "“当前等级”"
    agent_agent_profile }o--|| agent_agent_profile : "“推荐人”"
    agent_agent_profile ||--|| system_tenant : "“系统租户”"

```

# 6 定时任务设计

## 6.1 定时任务模块结构与包路径

- 定时任务实现包路径：
  - `com.yunyi.express2b.module.agent.biz.job`
- 主要定时任务类建议命名：
  - `AgentDailyPerformanceStatJob`（代理商日业绩统计定时任务）
  - 其他如升级检查、团队统计等可扩展为独立Job类

## 6.2 主要定时任务说明

### 6.2.1 代理商日业绩统计定时任务
- **执行时机**：每天零点（可配置）
- **主要流程**：
  1. 查询所有活跃代理商列表（本模块DAL）
  2. 依次统计每个代理商当天的个人订单、团队订单、利润、分润、坏账、红包、客户等多维度数据
  3. 统计数据需依赖以下模块：
     - 订单模块（express2b-module-order）：获取个人/团队订单数、订单金额、坏账订单等
     - 分润模块（express2b-module-commission）：获取分润收入
     - 钱包模块（express2b-module-wallet）：获取提现、红包、补贴等资金流水
  4. 汇总统计结果，写入`agent_daily_performance_stat`表
  5. 触发后续如等级升级检查等业务流程（如有）

- **依赖说明**：
  - 订单、分润、钱包等模块需提供API接口，支持按代理商和日期维度的业绩、分润、资金流水等数据查询。
  - 依赖接口如：`OrderApi.getAgentOrderStats(agentId, date)`、`CommissionApi.getAgentCommission(agentId, date)`、`WalletApi.getAgentWalletStats(agentId, date)`等。

### 6.2.2 代理商等级升级检查定时任务
- **包路径**：`com.yunyi.express2b.module.agent.biz.job`
- **任务类建议命名**：`AgentLevelUpgradeCheckJob`
- **执行时机**：每天零点后（业绩统计完成后，建议在`AgentDailyPerformanceStatJob`之后串行执行）
- **主要流程**：
  1. 查询所有活跃代理商列表（本模块DAL）
  2. 对每个代理商，查询近N天的日业绩统计数据（依赖`agent_daily_performance_stat`表）
  3. 判断是否满足升级条件（如连续N天个人/团队订单数达标）
  4. 若满足升级条件，则调用本模块升级接口（如`AgentApi.updateAgentLevel`）
  5. 记录升级日志，发送通知（如有）
- **依赖说明**：
  - 依赖本模块的`agent_daily_performance_stat`表作为业绩数据来源
  - 升级条件配置可依赖本模块的等级配置表（`agent_level_config`）
  - 订单模块（express2b-module-order）可作为业绩数据的补充来源

#### 代理商等级升级检查定时任务时序图

```mermaid
sequenceDiagram
    participant Scheduler as "定时任务调度器"
    participant UpgradeJob as "AgentLevelUpgradeCheckJob"
    participant AgentDAL as "Agent模块DAL"
    participant StatDAL as "业绩统计表DAL"
    participant LevelConfigDAL as "等级配置DAL"
    participant AgentApi as "AgentApi (升级接口)"

    Scheduler->>UpgradeJob: "每天零点后触发升级检查任务"
    UpgradeJob->>AgentDAL: "查询所有活跃代理商列表"
    loop "遍历每个代理商"
        UpgradeJob->>StatDAL: "查询近N天日业绩统计数据"
        UpgradeJob->>LevelConfigDAL: "获取升级条件配置"
        UpgradeJob->>UpgradeJob: "判定是否满足升级条件"
        alt "满足升级条件"
            UpgradeJob->>AgentApi: "调用升级接口(AgentApi.updateAgentLevel)"
            UpgradeJob->>AgentDAL: "记录升级日志/状态"
        end
    end
    UpgradeJob-->>Scheduler: "升级检查完成"
```

> 说明：
> - "AgentLevelUpgradeCheckJob" 依赖本模块的 "agent_daily_performance_stat" 作为业绩数据来源，依赖 "agent_level_config" 作为升级条件配置。
> - 升级接口调用本模块API，升级结果可用于后续通知、数据看板等。
