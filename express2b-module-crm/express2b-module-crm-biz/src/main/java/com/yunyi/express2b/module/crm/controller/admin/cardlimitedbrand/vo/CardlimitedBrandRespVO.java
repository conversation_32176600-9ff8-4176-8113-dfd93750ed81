package com.yunyi.express2b.module.crm.controller.admin.cardlimitedbrand.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 卡种可使用品牌 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CardlimitedBrandRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "21652")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "卡种id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12947")
    @ExcelProperty("卡种id")
    private Long cardlimitedId;

    @Schema(description = "快递品牌id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21310")
    @ExcelProperty("快递品牌id")
    private Long brandId;

}