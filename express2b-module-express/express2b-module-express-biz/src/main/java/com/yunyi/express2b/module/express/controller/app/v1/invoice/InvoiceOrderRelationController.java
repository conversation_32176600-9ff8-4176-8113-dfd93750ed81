package com.yunyi.express2b.module.express.controller.app.v1.invoice;

import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorderrelation.InvoiceOrderRelationPageDTO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceorderrelation.InvoiceOrderRelationVO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorderrelation.InvoiceOrderRelationDTO;
import com.yunyi.express2b.module.express.dal.dataobject.invoice.InvoiceOrderRelationDO;
import com.yunyi.express2b.module.express.service.invoice.invoiceorderrelation.InvoiceOrderRelationService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;

import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.*;


@Tag(name = "管理后台 - 发票订单关联")
@RestController
@RequestMapping("/express2/invoice-order-relation")
@Validated
public class InvoiceOrderRelationController {

    @Resource
    private InvoiceOrderRelationService invoiceOrderRelationService;

    @PostMapping("/create")
    @Operation(summary = "创建发票订单关联")
    @PreAuthorize("@ss.hasPermission('express2:invoice-order-relation:create')")
    public CommonResult<Long> createInvoiceOrderRelation(@Valid @RequestBody InvoiceOrderRelationDTO createReqVO) {
        return success(invoiceOrderRelationService.createInvoiceOrderRelation(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新发票订单关联")
    @PreAuthorize("@ss.hasPermission('express2:invoice-order-relation:update')")
    public CommonResult<Boolean> updateInvoiceOrderRelation(@Valid @RequestBody InvoiceOrderRelationDTO updateReqVO) {
        invoiceOrderRelationService.updateInvoiceOrderRelation(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除发票订单关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('express2:invoice-order-relation:delete')")
    public CommonResult<Boolean> deleteInvoiceOrderRelation(@RequestParam("id") Long id) {
        invoiceOrderRelationService.deleteInvoiceOrderRelation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得发票订单关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('express2:invoice-order-relation:query')")
    public CommonResult<InvoiceOrderRelationVO> getInvoiceOrderRelation(@RequestParam("id") Long id) {
        InvoiceOrderRelationDO invoiceOrderRelation = invoiceOrderRelationService.getInvoiceOrderRelation(id);
        return success(BeanUtils.toBean(invoiceOrderRelation, InvoiceOrderRelationVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得发票订单关联分页")
    @PreAuthorize("@ss.hasPermission('express2:invoice-order-relation:query')")
    public CommonResult<PageResult<InvoiceOrderRelationVO>> getInvoiceOrderRelationPage(@Valid InvoiceOrderRelationPageDTO pageReqVO) {
        PageResult<InvoiceOrderRelationDO> pageResult = invoiceOrderRelationService.getInvoiceOrderRelationPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InvoiceOrderRelationVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出发票订单关联 Excel")
    @PreAuthorize("@ss.hasPermission('express2:invoice-order-relation:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInvoiceOrderRelationExcel(@Valid InvoiceOrderRelationPageDTO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InvoiceOrderRelationDO> list = invoiceOrderRelationService.getInvoiceOrderRelationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "发票订单关联.xls", "数据", InvoiceOrderRelationVO.class,
                        BeanUtils.toBean(list, InvoiceOrderRelationVO.class));
    }

}