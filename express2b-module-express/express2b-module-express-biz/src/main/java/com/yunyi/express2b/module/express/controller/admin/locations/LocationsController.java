package com.yunyi.express2b.module.express.controller.admin.locations;

import cn.hutool.core.io.FileUtil;
import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.io.FileUtils;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;
import com.yunyi.express2b.module.express.controller.admin.locations.vo.LocationsPageReqVO;
import com.yunyi.express2b.module.express.controller.admin.locations.vo.LocationsRespVO;
import com.yunyi.express2b.module.express.controller.admin.locations.vo.LocationsSaveReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.locations.LocationsDO;
import com.yunyi.express2b.module.express.service.locations.LocationsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

/**
 * 全国行政区信息管理 controller层
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 全国行政区信息管理")
@RestController
@RequestMapping("/express/locations")
@Validated
public class LocationsController {

    @Resource
    private LocationsService locationsService;

    @PostMapping("/create")
    @Operation(summary = "创建全国行政区信息管理")
    @PreAuthorize("@ss.hasPermission('express:locations:create')")
    public CommonResult<Long> createLocations(@Valid @RequestBody LocationsSaveReqVO createReqVO) {
        return success(locationsService.createLocations(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新全国行政区信息管理")
    @PreAuthorize("@ss.hasPermission('express:locations:update')")
    public CommonResult<Boolean> updateLocations(@Valid @RequestBody LocationsSaveReqVO updateReqVO) {
        locationsService.updateLocations(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除全国行政区信息管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('express:locations:delete')")
    public CommonResult<Boolean> deleteLocations(@RequestParam("id") Long id) {
        locationsService.deleteLocations(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "省市区三级联动接口")
    @Parameter(name =  "parentCode", description = "父级代码", required = true)
    @PreAuthorize("@ss.hasPermission('express:locations:query')")
    public CommonResult<List<LocationsRespVO>> getLocations(@RequestParam(value = "parentCode",defaultValue = "100000") String parentCode) {
        List<LocationsDO> locations = locationsService.getLocations(parentCode);
        return success(BeanUtils.toBean(locations, LocationsRespVO.class));
    }
    @GetMapping("/page")
    @Operation(summary = "获得全国行政区信息管理分页")
    @PreAuthorize("@ss.hasPermission('express:locations:query')")
    public CommonResult<PageResult<LocationsRespVO>> getLocationsPage(@Valid LocationsPageReqVO pageReqVO) {
        PageResult<LocationsDO> pageResult = locationsService.getLocationsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LocationsRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出全国行政区信息管理 Excel")
    @PreAuthorize("@ss.hasPermission('express:locations:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLocationsExcel(@Valid LocationsPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LocationsDO> list = locationsService.getLocationsPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "全国行政区信息管理.xls", "数据", LocationsRespVO.class,
                        BeanUtils.toBean(list, LocationsRespVO.class));
    }

}