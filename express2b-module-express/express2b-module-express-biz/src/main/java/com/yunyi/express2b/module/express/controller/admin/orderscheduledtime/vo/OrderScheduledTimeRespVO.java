package com.yunyi.express2b.module.express.controller.admin.orderscheduledtime.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
/**
 * 管理后台 订单预约取件时间  OrderScheduledTimeResp VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 订单预约取件时间 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OrderScheduledTimeRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11647")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6714")
    @ExcelProperty("订单ID")
    private Long orderId;

    @Schema(description = "快递品牌标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("快递品牌标识")
    private String brandKey;

    @Schema(description = "预约取件日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预约取件日期")
    private LocalDate scheduledDate;

    @Schema(description = "预约取件时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预约取件时间")
    private LocalTime scheduledTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}