package com.yunyi.express2b.module.express.controller.app.v1.order.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/2 13:09
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrdersCountQeqVO {

    /**
     * placedOrders: 发单数量统计
     */
    private Integer placedOrders;

    /**
     * pendingPaymentOrders: 待支付订单数量统计
     */
    private Integer pendingPaymentOrders;

    /**
     * deliveredOrders: 已送达订单数量统计
     */
    private Integer deliveredOrders;


    /**
     * inProgressOrders: 进行中订单数量统计
     */
    private Integer inProgressOrders;

    /**
     * canceledOrders: 已取消订单数量统计
     */
    private Integer canceledOrders;
    /**
     * pickupPendingOrders: 待揽收数量统计
     */
    private Integer pickupPendingOrders;

    /**
     * intransitOrders: 运输中数量统计
     */
    private Integer intransitOrders;
}
