package com.yunyi.express2b.module.agent.api.dto;

import lombok.*;

/**
 * 定价模板 DO
 *这个就是从数据库拿值的对象
 * <AUTHOR>
 */
// 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
public class TemplateDTO{
    /**
     * 主键
     */
    private Long id;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 加价类型 (1-比例加价，2-固定金额加价-默认)
     */
    private Integer markupType;
    /**
     * 首重加价值 (固定金额，单位:分)
     */
    private Integer firstWeightMarkup;
    /**
     * 续重加价值 (固定金额，单位:分)
     */
    private Integer additionalWeightMarkup;
    /**
     * 平台零售首重价 (单位:分)
     */
    private Integer platformFirstPrice;
    /**
     * 平台零售续重价 (单位:分)
     */
    private Integer platformAdditionalPrice;
    /**
     * 状态 (0-禁用，1-启用)
     */
    private Integer status;
    /**
     * 描述
     */
    private String description;

}