---
description: 
globs: 
alwaysApply: true
---
# 模块结构规范

## 模块划分

平台采用模块化设计，每个业务模块 (`express2b-module-{module-name}`) 由以下子模块组成：

- **`express2b-module-{module-name}-api`**: 提供对外接口契约、DTO和枚举
- **`express2b-module-{module-name}-biz`**: 实现业务逻辑、控制器和数据访问逻辑

## 包结构规范

### API模块包结构

```
com.yunyi.express2b.module.{module-name}      // API根包
├── api.{moduleName}Api.java                  // 业务模块API接口定义
├── api.dto                                   // 数据传输对象
└── enums                                     // 枚举定义
```

### BIZ模块包结构

```
com.yunyi.express2b.module.{module-name}.biz
├── api         // API接口实现
├── controller  // HTTP控制器
│   ├── admin   // 管理后台API控制器
│   └── app     // 前端API控制器
│       └── v1  // 版本控制
├── service     // 业务服务
│   └── impl    // 服务实现
├── convert     // 对象转换器 (使用MapStruct)
├── dal         // 数据访问层
│   ├── dataobject  // 领域对象(DO)
│   └── mapper      // MyBatis Mapper接口
├── job         // 定时任务
└── listener    // 事件监听器
```

## URL路径规范

- 面向终端用户的接口: `/app-api/v1/{module-name}/...`
- 面向管理后台的接口: `/admin-api/{module-name}/...`

## 模块间通信

- 模块间只能通过API模块定义的接口和DTO进行通信
- 严禁模块间直接引用对方的内部实现类或DO对象
- 模块间的依赖关系应当在架构设计阶段明确定义，避免循环依赖





