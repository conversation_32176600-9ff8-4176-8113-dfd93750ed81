package com.yunyi.express2b.server.Express2bConfig;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "express2b.info")
public class Express2bInfoConfig {

    private String version;
    private String basePackage;

    // Getter 和 Setter 方法
    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getBasePackage() {
        return basePackage;
    }

    public void setBasePackage(String basePackage) {
        this.basePackage = basePackage;
    }




}
