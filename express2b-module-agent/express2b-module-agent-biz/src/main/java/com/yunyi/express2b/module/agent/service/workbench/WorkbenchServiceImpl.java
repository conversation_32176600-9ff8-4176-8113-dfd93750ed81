package com.yunyi.express2b.module.agent.service.workbench;

import com.yunyi.express2b.module.agent.controller.admin.workbench.vo.AgentIncomeTrendVo;
import com.yunyi.express2b.module.agent.controller.admin.workbench.vo.AgentOrderTrendVo;
import com.yunyi.express2b.module.agent.controller.admin.workbench.vo.AgentTotalVo;
import com.yunyi.express2b.module.agent.dal.mysql.agentprofile.AgentProfileMapper;
import com.yunyi.express2b.module.agent.dal.mysql.relationship.RelationshipMapper;
import com.yunyi.express2b.module.express.api.ordertotal.OrderAgentTotalApi;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * 工作台逻辑实现
 *
 * <AUTHOR>
 * @since 17
 * @version 1.0
 * @since 2025/5/27 下午1:17
 */

@Service
public class WorkbenchServiceImpl implements WorkbenchService{

    @Autowired
    private AgentProfileMapper agentProfileMapper;
    @Resource
    private OrderAgentTotalApi orderAgentTotal;
    @Resource
    private RelationshipMapper relationshipMapper;

    /**
     * 获取代理商总览信息
     * @param agentId
     * @return AgentTotalVo
     */
    @Override
    public AgentTotalVo getTotal(Long agentId) {
        AgentTotalVo agentTotalVo = new AgentTotalVo();
//        添加代理商总数 这个就是代理商ID
        agentTotalVo.setTotal(agentProfileMapper.selectCount());
//        添加已下订单的代理商数量
        agentTotalVo.setOrderAgentCount(orderAgentTotal.getAgentCount());
//        添加直接代理商数量 查询关系表下的第一层级,只要有这个关系,查询这个表的所有父级是这个ID的,把获取到的数量减1
        agentTotalVo.setDirectCount(relationshipMapper.selectByAgentId(agentId));
//        添加代理商的间接下级数 (是三级还是全部有)
        List<Long> descendantIds = relationshipMapper.selectByAncestorId(agentId);
//        遍历所有子集ID,并查询出子集ID下有多少代理商进行累加
        Long  indictirectCount = 0L;
        for (Long descendantId : descendantIds) {
            indictirectCount += relationshipMapper.selectByAgentId(descendantId);
        }
        agentTotalVo.setIndirectCount(indictirectCount);
//        返回完整的代理商对象
        return agentTotalVo;
    }

    /**
     * 获取订单趋势图显示
     * @param agentId
     * @return
     */
    @Override
    public AgentOrderTrendVo getorderTrendDisplay(Long agentId) {
//        获取当前的日期
        LocalDate now = LocalDate.now();
//        获取一周前的日期
        LocalDate oneWeekAgo = now.minusWeeks(1);
//        显示表展示的是定向数据所以需要将天数再移一天
        oneWeekAgo = oneWeekAgo.minusDays(1);
//        创建订单计数集合
        List<Long> orderWeekCount = new ArrayList<>();
//        创建订单计数日期集合
        List<String> weekDate = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            LocalDate date = oneWeekAgo.plusDays(1);
//            获取到这个日期的下一天 用来条件查询
            LocalDate dateTomarrow = date.plusDays(1);
//            把日期和当前代理商ID传入获取到当天的订单量
            Long orderCountById = orderAgentTotal.getOrderCount(agentId,date,dateTomarrow);
//            把查询出来的订单数量存储到集合中
            orderWeekCount.add(orderCountById);
//            将当前日期转换成字符串(星期形式)
            String dateStr = date.getDayOfWeek().getDisplayName(TextStyle.FULL, Locale.CHINA);
//            将星期存入集合中
            weekDate.add(dateStr);
//            修改时间下标
            oneWeekAgo = date;
        }
//       将存完的值放入当前对象返回给前端
        AgentOrderTrendVo agentOrderTrendVo = new AgentOrderTrendVo();
        agentOrderTrendVo.setOrderCount(orderWeekCount);
        agentOrderTrendVo.setDate(weekDate);
        return agentOrderTrendVo;
    }

    /**
     * 获取收入趋势图显示
     * @param agentId
     * @return
     */
    @Override
    public AgentIncomeTrendVo getIncomeTrendDisplay(Long agentId) {
//        获取当前的日期
        LocalDate now = LocalDate.now();
//        获取一周前的日期
        LocalDate oneWeekAgo = now.minusWeeks(1);
//        显示表展示的是定向数据所以需要将天数再移一天
        oneWeekAgo = oneWeekAgo.minusDays(1);
//        创建收入总和集合
        List<Long> incomeTotal = new ArrayList<>();
//        创建收入计数日期集合
        List<String> weekDate = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            LocalDate date = oneWeekAgo.plusDays(1);
//            获取到这个日期的下一天 用来条件查询
            LocalDate dateTomarrow = date.plusDays(1);
//            查询出本日收入总和 (暂时是订单实收金额)
            Long sum = orderAgentTotal.getOrderIncome(agentId, date, dateTomarrow);
            sum = (sum != null ? sum : 0L) / 100;
//            将收入总和存入集合中
            incomeTotal.add(sum);
//            将当前日期转换成字符串(星期形式)
            String dateStr = date.getDayOfWeek().getDisplayName(TextStyle.FULL, Locale.CHINA);
//            将星期存入集合中
            weekDate.add(dateStr);
//            修改时间下标
            oneWeekAgo = date;
        }
        AgentIncomeTrendVo agentIncomeTrendVo = new AgentIncomeTrendVo();
        agentIncomeTrendVo.setIncomeTotal(incomeTotal);
        agentIncomeTrendVo.setDate(weekDate);
        return agentIncomeTrendVo;
    }
}