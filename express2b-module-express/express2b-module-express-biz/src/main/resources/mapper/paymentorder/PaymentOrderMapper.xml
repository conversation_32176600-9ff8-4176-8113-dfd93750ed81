<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunyi.express2b.module.express.dal.mysql.paymentorder.PaymentOrderMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getWorkbenchInterface"
            resultType="com.yunyi.express2b.module.express.controller.admin.order.dto.WorkbenchInterfaceDTO">
        SELECT
        COUNT(order_no) AS orderQuantity,
        SUM(CASE WHEN status = 'PAID' THEN 1 ELSE 0 END) AS orderPaymentQuantity,
        SUM(CASE WHEN status = 'CANCELED' THEN 1 ELSE 0 END) AS alreadyCredited,
        SUM(received_amount) AS numberRecordedOrders,
        SUM(CASE WHEN status = 'PAID' THEN 1 ELSE 0 END) / COUNT(order_no) AS salesVolume
        FROM
        express2b_order
        WHERE
        deleted = '0'
        <if test="agentId != null">
            AND agent_id = #{agentId}
        </if>
        <if test="startTime != null and endTime != null">
        AND create_time &gt;= #{startTime} AND create_time &lt;= #{endTime}
        </if>
    </select>
</mapper>