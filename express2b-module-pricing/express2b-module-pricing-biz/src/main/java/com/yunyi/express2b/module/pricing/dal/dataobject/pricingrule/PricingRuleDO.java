package com.yunyi.express2b.module.pricing.dal.dataobject.pricingrule;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 代理商定价（终端零售价）规则 DO
 *
 * <AUTHOR>
 */
@TableName("pricing_agent_rule")
@KeySequence("pricing_agent_rule_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PricingRuleDO extends BaseDO {

    /**
     * 规则ID
     */
    @TableId
    private Long id;
    /**
     * 代理商ID
     */
    private Long agentId;
    /**
     * 加价类型 (1-比例加价，2-固定金额加价-默认)
     */
    private Integer markupType;
    /**
     * 首重加价值 (比例或固定金额，单位是分)
     */
    private Integer firstWeightMarkup;
    /**
     * 续重加价值 (比例或固定金额，单位是分)
     */
    private Integer additionalWeightMarkup;
    /**
     * 品牌标识 (可为空，表示所有品牌)
     */
    private String brandKey;
    /**
     * 始发地adcode (可为空，表示所有始发地)
     */
    private String fromAdcode;
    /**
     * 目的地adcode (可为空，表示所有目的地)
     */
    private String toAdcode;
    /**
     * 状态 (0-禁用，1-启用)
     */
    private Integer status;
    /**
     * 优先级 (数字越大优先级越高)
     */
    private Integer priority;

}