package com.yunyi.express2b.module.express.controller.app.v1.price.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 价格查询响应vo
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/2 下午4:43
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PriceQueryRespVO {
    /**
     * 零售价
     */
    private Integer retailAmount;
    /**
     * 首重价格
     */
    private Integer firstPrice;
    /**
     * 续重价格
     */
    private Integer overPrice;
    /**
     * 阶梯价优惠金额
     */
    private Integer ladderDiscountAmount;
    /**
     * 会员等级优惠价格
     */
    private Integer membersLevelDiscountAmount;
    /**
     * 次卡优惠金额
     */
    private Integer subCardDiscountAmount;
    /**
     * 优惠总金额
     */
    private Integer discountAmount;
    /**
     * 优惠后价格
     */
    private Integer payMoney;
    /**
     * 快递公司码(brandKey)
     */
    private String expressCode;
    /**
     * 快递产品码(productCode)
     */
    private String productCode;
    /**
     * 收件人省份
     */
    private String receiverProvince;
    /**
     * 收件人城市
     */
    private String receiverCity;
    /**
     * 快递公司名称
     */
    private String name;
    /**
     * 快递公司logo地址
     */
    private String logoUrl;
    // 成本价格，单位：分
    private Integer costPrice;

    // 平台成本价格，单位：分
    private Integer routePrice;

    // 分润价格，单位：分
    private Integer profit;
}
