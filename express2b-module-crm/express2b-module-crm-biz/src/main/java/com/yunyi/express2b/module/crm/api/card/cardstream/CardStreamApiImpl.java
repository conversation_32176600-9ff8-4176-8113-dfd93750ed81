package com.yunyi.express2b.module.crm.api.card.cardstream;

import com.yunyi.express2b.framework.common.util.object.BeanUtils;


import com.yunyi.express2b.module.crm.api.card.cardstream.vo.CardStreamSaveReqVO;

import com.yunyi.express2b.module.crm.api.card.cardstream.vo.CardUserVo;
import com.yunyi.express2b.module.crm.dal.dataobject.cardstream.CardStreamDO;
import com.yunyi.express2b.module.crm.dal.dataobject.carduser.CardUserDO;
import com.yunyi.express2b.module.crm.dal.mysql.cardstream.CardStreamMapper;
import com.yunyi.express2b.module.crm.dal.mysql.carduser.CardUserMapper;
import com.yunyi.express2b.module.crm.enums.CardStatusEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * CardStreamSaveVO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/31 下午3:41
 */
@Component
public class CardStreamApiImpl implements CardStreamApi {
    @Resource
    private CardStreamMapper cardStreamMapper;

    @Resource
    private CardUserMapper cardUserMapper;

    /**
     * 创建卡流水
     * @param createReqVO
     * @return
     */
    @Override
    public Long createCardStream(CardStreamSaveReqVO createReqVO) {
        // 插入
        CardStreamDO cardStream = BeanUtils.toBean(createReqVO, CardStreamDO.class);
        cardStreamMapper.insert(cardStream);
        // 返回
        return cardStream.getId();
    }

    /**
     * 更新卡状态根据payOrderSn
     */
    @Override
    public int updatepayOrderSn(String payOrderSn) {
        Integer status = CardStatusEnum.ENABLE.getCode();
        return cardUserMapper.updatepayOrderSn(payOrderSn, status);
    }

    /**
     * 根据payOrderSn查询卡信息
     * @param payOrderSn
     * @return
     */
    @Override
    public CardUserVo selectpayOrderSn(String payOrderSn) {
        CardUserDO cardUserDO = cardUserMapper.selectpayOrderSn(payOrderSn);
        return  BeanUtils.toBean(cardUserDO, CardUserVo.class);
    }


}
