package com.yunyi.express2b.module.crm.controller.app.v1.company;

import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.security.core.util.CrmSecurityFrameworkUtils;
import com.yunyi.express2b.module.crm.controller.admin.companyinfo.vo.CompanyInfoRespVO;
import com.yunyi.express2b.module.crm.controller.admin.personalinfo.vo.PersonalInfoRespVO;
import com.yunyi.express2b.module.crm.dal.dataobject.companyinfo.CompanyInfoDO;
import com.yunyi.express2b.module.crm.service.appcpmpany.AppCompanyService;
import com.yunyi.express2b.module.crm.service.companyinfo.CompanyInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

/**
 * 公司信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/26 11:15
 */
@Tag(name = "app - 公司信息")
@RestController
@RequestMapping("/company")
public class AppCompanyController {
    @Autowired
    private AppCompanyService appCompanyService;

    @Autowired
    private CompanyInfoService companyInfoService;


    @Operation(summary = "app-获取个人的公司信息")
    @GetMapping("/list")
    public CommonResult<CompanyInfoRespVO> getUserCompanyInfo() {
        CompanyInfoDO companyInfo = companyInfoService.getUserCompanyInfo(CrmSecurityFrameworkUtils.getLoginUserId());
        return success(BeanUtils.toBean(companyInfo, CompanyInfoRespVO.class));
    }

    @Operation(summary = "app-认证企业用户")
    @PostMapping("/authenticate-enterprise-user")
    public Boolean authenticateEnterpriseUser( PersonalInfoRespVO personalInfoRespVO ) {
        return appCompanyService.authenticateEnterpriseUser(personalInfoRespVO);
    }

    @Operation(summary = "app-修改企业联系人")
    @PostMapping("/update-contact")
    public boolean upContact(@RequestBody CompanyInfoRespVO companyInfoReqVO) {
        return appCompanyService.updateContact(companyInfoReqVO);
    }
    @PostMapping("/delete")
    @Operation(summary = "注销公司账户")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteCompanyInfo(@RequestParam("id") Long id) {
        companyInfoService.deleteCompany(id);
        return success(true);
    }
}
