package com.yunyi.express2b.module.express.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/28 16:00
 */
@Schema(description = "订单取消 Request VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderCancelVO {
    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotEmpty(message = "订单编号不能为空")
    private String orderNo;

    //运力系统订单号
    private String ucmOrderSn;

    @Schema(description = "退款原因")
    private String refundReason;

    @Schema(description = "用户ID")
    private Long personalId;

}
