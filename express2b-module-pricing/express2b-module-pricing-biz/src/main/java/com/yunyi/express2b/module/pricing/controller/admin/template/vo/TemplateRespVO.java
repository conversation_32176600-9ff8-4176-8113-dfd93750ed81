package com.yunyi.express2b.module.pricing.controller.admin.template.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 定价模板 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TemplateRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "8847")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("模板名称")
    private String templateName;

    @Schema(description = "加价类型 (1-比例加价，2-固定金额加价-默认)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("加价类型 (1-比例加价，2-固定金额加价-默认)")
    private Integer markupType;

    @Schema(description = "首重加价值 (固定金额，单位:分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("首重加价值 (固定金额，单位:分)")
    private Integer firstWeightMarkup;

    @Schema(description = "续重加价值 (固定金额，单位:分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("续重加价值 (固定金额，单位:分)")
    private Integer additionalWeightMarkup;

    @Schema(description = "平台零售首重价 (单位:分)", requiredMode = Schema.RequiredMode.REQUIRED, example = "17154")
    @ExcelProperty("平台零售首重价 (单位:分)")
    private Integer platformFirstPrice;

    @Schema(description = "平台零售续重价 (单位:分)", requiredMode = Schema.RequiredMode.REQUIRED, example = "17967")
    @ExcelProperty("平台零售续重价 (单位:分)")
    private Integer platformAdditionalPrice;

    @Schema(description = "状态 (0-禁用，1-启用)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态 (0-禁用，1-启用)")
    private Integer status;

    @Schema(description = "描述", example = "随便")
    @ExcelProperty("描述")
    private String description;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}