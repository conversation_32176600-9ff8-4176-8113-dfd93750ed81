package com.yunyi.express2b.module.express.service.brand;

import java.util.*;
import jakarta.validation.*;
import com.yunyi.express2b.module.express.controller.admin.brand.vo.*;
import com.yunyi.express2b.module.express.dal.dataobject.brand.BrandDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;

/**
 * 快递品牌信息表 Service 接口
 *
 * <AUTHOR>
 */
public interface BrandService {

    /**
     * 创建快递品牌信息表
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createBrand(@Valid BrandSaveReqVO createReqVO);

    /**
     * 更新快递品牌信息表
     *
     * @param updateReqVO 更新信息
     */
    void updateBrand(@Valid BrandSaveReqVO updateReqVO);

    /**
     * 删除快递品牌信息表
     *
     * @param id 编号
     */
    void deleteBrand(Long id);

    /**
     * 获得快递品牌信息表
     *
     * @param id 编号
     * @return 快递品牌信息表
     */
    BrandDO getBrand(Long id);

    /**
     * 获得快递品牌信息表分页
     *
     * @param pageReqVO 分页查询
     * @return 快递品牌信息表分页
     */
    PageResult<BrandDO> getBrandPage(BrandPageReqVO pageReqVO);
    /**
     * 获取所有品牌信息
     * @return List<BrandDO> 表
     */
List<BrandDO>  getAllBrand();
}