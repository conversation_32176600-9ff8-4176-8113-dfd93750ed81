package com.yunyi.express2b.module.pricing.service.selectedpricingtemplate;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.yunyi.express2b.module.pricing.controller.admin.selectedpricingtemplate.vo.*;
import com.yunyi.express2b.module.pricing.dal.dataobject.selectedpricingtemplate.SelectedPricingTemplateDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;

import com.yunyi.express2b.module.pricing.dal.mysql.selectedpricingtemplate.SelectedPricingTemplateMapper;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.pricing.enums.ErrorCodeConstants.*;

/**
 * 代理商为下级选择的价格模板记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SelectedPricingTemplateServiceImpl implements SelectedPricingTemplateService {

    @Resource
    private SelectedPricingTemplateMapper selectedPricingTemplateMapper;

    @Override
    public Long createSelectedPricingTemplate(SelectedPricingTemplateSaveReqVO createReqVO) {
        // 插入
        SelectedPricingTemplateDO selectedPricingTemplate = BeanUtils.toBean(createReqVO, SelectedPricingTemplateDO.class);
        selectedPricingTemplateMapper.insert(selectedPricingTemplate);
        // 返回
        return selectedPricingTemplate.getId();
    }

    @Override
    public void updateSelectedPricingTemplate(SelectedPricingTemplateSaveReqVO updateReqVO) {
        // 校验存在
        validateSelectedPricingTemplateExists(updateReqVO.getId());
        // 更新
        SelectedPricingTemplateDO updateObj = BeanUtils.toBean(updateReqVO, SelectedPricingTemplateDO.class);
        selectedPricingTemplateMapper.updateById(updateObj);
    }

    @Override
    public void deleteSelectedPricingTemplate(Long id) {
        // 校验存在
        validateSelectedPricingTemplateExists(id);
        // 删除
        selectedPricingTemplateMapper.deleteById(id);
    }

    private void validateSelectedPricingTemplateExists(Long id) {
        if (selectedPricingTemplateMapper.selectById(id) == null) {
            throw exception(SELECTED_PRICING_TEMPLATE_NOT_EXISTS);
        }
    }

    @Override
    public SelectedPricingTemplateDO getSelectedPricingTemplate(Long id) {
        return selectedPricingTemplateMapper.selectById(id);
    }

    @Override
    public PageResult<SelectedPricingTemplateDO> getSelectedPricingTemplatePage(SelectedPricingTemplatePageReqVO pageReqVO) {
        return selectedPricingTemplateMapper.selectPage(pageReqVO);
    }

}