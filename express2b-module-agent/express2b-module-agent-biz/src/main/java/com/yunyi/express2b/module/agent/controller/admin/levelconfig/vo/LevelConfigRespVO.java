package com.yunyi.express2b.module.agent.controller.admin.levelconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;



/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 9:53
 */
@Schema(description = "管理后台 - 代理商等级配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LevelConfigRespVO {

    @Schema(description = "等级配置ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1160")
    @ExcelProperty("等级配置ID")
    private Long id;

    @Schema(description = "等级名称（如V1,V2,V3）", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("等级名称（如V1,V2,V3）")
    private String levelName;

    @Schema(description = "此等级默认关联的平台价格模板ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23614")
    @ExcelProperty("此等级默认关联的平台价格模板ID")
    private Long defaultPlatformPricingTemplateId;

    @Schema(description = "升级到此等级所需个人连续3日日均订单量阈值", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("升级到此等级所需个人连续3日日均订单量阈值")
    private Integer upgradePersonalDailyOrders;

    @Schema(description = "升级到此等级所需团队连续3日日均订单量阈值", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("升级到此等级所需团队连续3日日均订单量阈值")
    private Integer upgradeTeamDailyOrders;

    @Schema(description = "业绩观察天数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("业绩观察天数")
    private Integer upgradeObservationDays;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}