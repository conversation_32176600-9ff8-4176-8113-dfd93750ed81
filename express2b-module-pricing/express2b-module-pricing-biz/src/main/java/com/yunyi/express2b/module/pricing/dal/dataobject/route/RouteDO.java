package com.yunyi.express2b.module.pricing.dal.dataobject.route;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 快递全国运价基础 DO
 *
 * <AUTHOR>
 */
@TableName("express2b_route")
@KeySequence("express2b_route_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RouteDO extends BaseDO {

    /**
     * 运费ID (主键)
     */
    @TableId
    private Long id;
    /**
     * 品牌标识
     */
    private String brandKey;
    /**
     * 线路标识（格式：品牌-始发地-目的地）
     */
    private String lineKey;
    /**
     * 始发地名称
     */
    private String sendProvince;
    /**
     * 目的地名称
     */
    private String receiveProvince;
    /**
     * 始发地adcode
     */
    private String sendProvinceCode;
    /**
     * 目的地adcode
     */
    private String receiveProvinceCode;
    /**
     * 产品名称
     */
    private String productCode;
    /**
     * 首重重量(g)
     */
    private Integer firstWeight;
    /**
     * 续重重量(g)
     */
    private Integer additionalWeight;
    /**
     * 供应首重成本价（分）
     */
    private Integer costFirstPrice;
    /**
     * 供应续重成本价（分）
     */
    private Integer costOverPrice;
    /**
     * 市场首重价格（分）
     */
    private Integer originalFirstPrice;
    /**
     * 市场续重价格（分）
     */
    private Integer originalOverPrice;

}