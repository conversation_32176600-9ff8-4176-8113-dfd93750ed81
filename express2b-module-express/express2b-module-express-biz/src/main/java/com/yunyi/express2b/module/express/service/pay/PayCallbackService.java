package com.yunyi.express2b.module.express.service.pay;

import com.yunyi.express2b.module.express.controller.admin.receivepayment.vo.PayCallBackRespVO;
import com.yunyi.express2b.module.express.dal.dataobject.receivepayment.PayCallback;

/**
 * <AUTHOR>
 * @date 2025-03-27 11:47:20
 */
public interface PayCallbackService {
    /**
     *
     * @param payCallback
     * @return
     */
    PayCallBackRespVO payCallback(PayCallback payCallback);
}