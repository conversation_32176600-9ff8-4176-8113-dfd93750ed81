package com.yunyi.express2b.module.crm.service.apppersonal;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.module.crm.controller.admin.personalinfo.vo.PersonalInfoPageReqVO;
import com.yunyi.express2b.module.crm.controller.admin.personalinfo.vo.PersonalInfoRespVO;
import com.yunyi.express2b.module.crm.controller.app.v1.personal.vo.UserLoginRequest;
import com.yunyi.express2b.module.crm.dal.dataobject.personalinfo.PersonalInfoDO;
import com.yunyi.express2b.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;


/**
 * app端账户service
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/25 20:19
 */


public interface AppPersonalService {
    /**
     * 登录
     *
     * @param loginRequest 登录用户信息
     * @return
     */
    OAuth2AccessTokenRespDTO login(UserLoginRequest loginRequest) ;

    /**
     * 获取用户升级审核结果
     *
     * @param memberId
     * @return
     */
    String getmsg(Long memberId);

    /**
     * 用户信息登记
     *
     * @param personalInfoRespVO
     * @return
     */
    Boolean upUserLevel(PersonalInfoRespVO personalInfoRespVO);

    /**
     * 分页查询
     *
     * @param respVO
     * @return
     */
    PageResult<PersonalInfoDO> selectPage(PersonalInfoPageReqVO respVO);

    /**
     * 注销
     *
     * @param id
     * @return
     */
    Boolean deletePersonalInfo(Long id);

    /**
     * 获取关注状态
     * @param memberId
     * @return
     */
    Integer getFollowStatus(Long memberId);

    /**
     * 修改头像
     *
     * @param memberId
     * @param avatar
     * @return
     */
    Boolean updateHead(Long memberId, String avatar);

    /**
     * 修改昵称
     *
     * @param memberId
     * @param nickname
     * @return
     */
    Boolean updateNickname(Long memberId, String nickname);
/*
     * 注销用户
 */
    void logout(String token, Integer type);


}
