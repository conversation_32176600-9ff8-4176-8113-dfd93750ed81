package com.yunyi.express2b.module.crm.dal.mysql.personalinfo;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;

import com.yunyi.express2b.module.crm.api.personInfo.dto.PersonalInfoSaveReqDTO;
import com.yunyi.express2b.module.crm.dal.dataobject.personalinfo.PersonalInfoDO;
import org.apache.ibatis.annotations.Mapper;
import com.yunyi.express2b.module.crm.controller.admin.personalinfo.vo.*;

/**
 * 个人信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PersonalInfoMapper extends BaseMapperX<PersonalInfoDO> {

    default PageResult<PersonalInfoDO> selectPage(PersonalInfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PersonalInfoDO>()
                .eqIfPresent(PersonalInfoDO::getCompanyId, reqVO.getCompanyId())
                .eqIfPresent(PersonalInfoDO::getMemberId, reqVO.getMemberId())
                .likeIfPresent(PersonalInfoDO::getMemberName, reqVO.getMemberName())
                .eqIfPresent(PersonalInfoDO::getMemberPhone, reqVO.getMemberPhone())
                .eqIfPresent(PersonalInfoDO::getMemberIdCard, reqVO.getMemberIdCard())
                .eqIfPresent(PersonalInfoDO::getFrontCard, reqVO.getFrontCard())
                .eqIfPresent(PersonalInfoDO::getReverseCard, reqVO.getReverseCard())
                .eqIfPresent(PersonalInfoDO::getState, reqVO.getState())
                .eqIfPresent(PersonalInfoDO::getRemarks, reqVO.getRemarks())
                .betweenIfPresent(PersonalInfoDO::getAuditTime, reqVO.getAuditTime())
                .betweenIfPresent(PersonalInfoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PersonalInfoDO::getId));
    }


    /**
     * 根据memberId查询个人信息，memberId对应统一登录平台的ssoUserId
     * @param memberId  应统一登录平台的ssoUserId
     * @return 个人信息
     */
    default PersonalInfoDO selectByMemberId(Long memberId){
        return selectOne(PersonalInfoDO::getMemberId, memberId);
    }

    /**
     * 根据公司ID删除个人信息
     *
     * @param companyId 公司ID，用于指定要删除的个人信息所属的公司
     */
    default void deleteByCompanyId(Long companyId){
        delete(new LambdaQueryWrapperX<PersonalInfoDO>().eq(PersonalInfoDO::getCompanyId,companyId));

    }

    default int updatePersonInfo(PersonalInfoSaveReqDTO personalInfoSaveReqDTO){
        return update(new PersonalInfoDO(),new LambdaUpdateWrapper<PersonalInfoDO>()
                .set(PersonalInfoDO::getWeStatus,personalInfoSaveReqDTO.getWeStatus())
                .eq(PersonalInfoDO::getMemberId,personalInfoSaveReqDTO.getMemberId()));
    }
}