package com.yunyi.express2b.module.express.controller.admin.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/17 17:53
 */

@Data
public class TrackCallBackRequestVO {

    /**
     * 运单号
     */
    private String expressSn;

    /**
     * 物流轨迹数据列表
     */
    private List<LogisticsTrackItem> data;

    /**
     * 物流轨迹明细项
     */
    @Data
    public static class LogisticsTrackItem {
        /**
         * 时间（格式：yyyy-MM-dd HH:mm:ss）
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime time;

        /**
         * 物流轨迹描述
         * 示例："【苏州转运中心】 已发出 下一站 【无锡转运中心公司】"
         */
        private String track;

        /**
         * 运输状态
         * 示例："专线"
         */
        private String status;

        /**
         * 区域名称
         * 示例："苏州"
         */
        private String areaName;

        /**
         * 地理位置坐标（经度,纬度）
         * 示例："120.585315,31.298886"
         */
        private String location;

    }
}
