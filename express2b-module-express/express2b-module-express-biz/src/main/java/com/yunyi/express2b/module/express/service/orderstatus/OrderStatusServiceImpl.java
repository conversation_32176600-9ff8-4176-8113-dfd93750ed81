package com.yunyi.express2b.module.express.service.orderstatus;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.express.controller.admin.orderstatus.vo.OrderStatusPageReqVO;
import com.yunyi.express2b.module.express.controller.admin.orderstatus.vo.OrderStatusSaveReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.orderstatus.OrderStatusDO;
import com.yunyi.express2b.module.express.dal.mysql.orderstatus.OrderStatusMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.ORDER_STATUS_NOT_EXISTS;

/**
 * 订单状态管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class OrderStatusServiceImpl implements OrderStatusService {

    @Resource
    private OrderStatusMapper orderStatusMapper;


    /**
     * 创建订单状态
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Long createOrderStatus(OrderStatusSaveReqVO createReqVO) {
        // 插入
        OrderStatusDO orderStatus = BeanUtils.toBean(createReqVO, OrderStatusDO.class);
        orderStatusMapper.insert(orderStatus);
        log.info("【订单状态创建成功】: {}", orderStatus);
        // 返回
        return orderStatus.getId();
    }

    /**
     * 更新订单状态
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateOrderStatus(OrderStatusSaveReqVO updateReqVO) {
        // 校验存在
        validateOrderStatusExists(updateReqVO.getId());
        // 更新
        OrderStatusDO updateObj = BeanUtils.toBean(updateReqVO, OrderStatusDO.class);
        orderStatusMapper.updateById(updateObj);
    }


    /**
     * 校验订单状态存在
     * @param id
     */
    private void validateOrderStatusExists(Long id) {
        if (orderStatusMapper.selectById(id) == null) {
            throw exception(ORDER_STATUS_NOT_EXISTS);
        }
    }

    /**
     * 获得订单状态
     * @param id 编号
     * @return
     */
    @Override
    public OrderStatusDO getOrderStatus(Long id) {
        return orderStatusMapper.selectById(id);
    }

    /**
     * 获得订单状态分页
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<OrderStatusDO> getOrderStatusPage(OrderStatusPageReqVO pageReqVO) {
        return orderStatusMapper.selectPage(pageReqVO);
    }

    /**
     * 根据订单id更新订单状态
     *
     * @param updateReqVO 分页查询
     */
    @Override
    public void updateByOrderId(OrderStatusSaveReqVO updateReqVO) {

        OrderStatusDO updateObj = BeanUtils.toBean(updateReqVO, OrderStatusDO.class);
        UpdateWrapper<OrderStatusDO> wrapper = new UpdateWrapper<>();
        wrapper.eq("order_id", updateReqVO.getOrderId());
        orderStatusMapper.update(updateObj, wrapper);
    }
}