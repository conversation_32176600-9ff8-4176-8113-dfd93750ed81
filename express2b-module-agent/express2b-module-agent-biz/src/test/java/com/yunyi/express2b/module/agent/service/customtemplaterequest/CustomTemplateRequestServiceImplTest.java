package com.yunyi.express2b.module.agent.service.customtemplaterequest;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.module.agent.controller.admin.customtemplaterequest.vo.CustomTemplateRequestPageReqVO;
import com.yunyi.express2b.module.agent.controller.admin.customtemplaterequest.vo.CustomTemplateRequestSaveReqVO;
import com.yunyi.express2b.module.agent.dal.dataobject.customtemplaterequest.CustomTemplateRequestDO;
import com.yunyi.express2b.module.agent.dal.mysql.customtemplaterequest.CustomTemplateRequestMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import static com.yunyi.express2b.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static com.yunyi.express2b.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertPojoEquals;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertServiceException;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomLongId;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomPojo;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.CUSTOM_TEMPLATE_REQUEST_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link CustomTemplateRequestServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(CustomTemplateRequestServiceImpl.class)
public class CustomTemplateRequestServiceImplTest extends BaseDbUnitTest {

    @Resource
    private CustomTemplateRequestServiceImpl customTemplateRequestService;

    @Resource
    private CustomTemplateRequestMapper customTemplateRequestMapper;

    @Test
    public void testCreateCustomTemplateRequest_success() {
        // 准备参数
        CustomTemplateRequestSaveReqVO createReqVO = randomPojo(CustomTemplateRequestSaveReqVO.class).setId(null);

        // 调用
        Long customTemplateRequestId = customTemplateRequestService.createCustomTemplateRequest(createReqVO);
        // 断言
        assertNotNull(customTemplateRequestId);
        // 校验记录的属性是否正确
        CustomTemplateRequestDO customTemplateRequest = customTemplateRequestMapper.selectById(customTemplateRequestId);
        assertPojoEquals(createReqVO, customTemplateRequest, "id");
    }

    @Test
    public void testUpdateCustomTemplateRequest_success() {
        // mock 数据
        CustomTemplateRequestDO dbCustomTemplateRequest = randomPojo(CustomTemplateRequestDO.class);
        customTemplateRequestMapper.insert(dbCustomTemplateRequest);// @Sql: 先插入出一条存在的数据
        // 准备参数
        CustomTemplateRequestSaveReqVO updateReqVO = randomPojo(CustomTemplateRequestSaveReqVO.class, o -> {
            o.setId(dbCustomTemplateRequest.getId()); // 设置更新的 ID
        });

        // 调用
        customTemplateRequestService.updateCustomTemplateRequest(updateReqVO);
        // 校验是否更新正确
        CustomTemplateRequestDO customTemplateRequest = customTemplateRequestMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, customTemplateRequest);
    }

    @Test
    public void testUpdateCustomTemplateRequest_notExists() {
        // 准备参数
        CustomTemplateRequestSaveReqVO updateReqVO = randomPojo(CustomTemplateRequestSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> customTemplateRequestService.updateCustomTemplateRequest(updateReqVO), CUSTOM_TEMPLATE_REQUEST_NOT_EXISTS);
    }

    @Test
    public void testDeleteCustomTemplateRequest_success() {
        // mock 数据
        CustomTemplateRequestDO dbCustomTemplateRequest = randomPojo(CustomTemplateRequestDO.class);
        customTemplateRequestMapper.insert(dbCustomTemplateRequest);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbCustomTemplateRequest.getId();

        // 调用
        customTemplateRequestService.deleteCustomTemplateRequest(id);
        // 校验数据不存在了
        assertNull(customTemplateRequestMapper.selectById(id));
    }

    @Test
    public void testDeleteCustomTemplateRequest_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> customTemplateRequestService.deleteCustomTemplateRequest(id), CUSTOM_TEMPLATE_REQUEST_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetCustomTemplateRequestPage() {
        // mock 数据
        CustomTemplateRequestDO dbCustomTemplateRequest = randomPojo(CustomTemplateRequestDO.class, o -> { // 等会查询到
            o.setAgentId(null);
            o.setRequestedTemplateDetails(null);
            o.setStatus(null);
            o.setApprovedTemplateId(null);
            o.setRequestedAt(null);
            o.setReviewedAt(null);
            o.setReviewerComments(null);
            o.setRemarks(null);
            o.setCreateTime(null);
        });
        customTemplateRequestMapper.insert(dbCustomTemplateRequest);
        // 测试 agentId 不匹配
        customTemplateRequestMapper.insert(cloneIgnoreId(dbCustomTemplateRequest, o -> o.setAgentId(null)));
        // 测试 requestedTemplateDetails 不匹配
        customTemplateRequestMapper.insert(cloneIgnoreId(dbCustomTemplateRequest, o -> o.setRequestedTemplateDetails(null)));
        // 测试 status 不匹配
        customTemplateRequestMapper.insert(cloneIgnoreId(dbCustomTemplateRequest, o -> o.setStatus(null)));
        // 测试 approvedTemplateId 不匹配
        customTemplateRequestMapper.insert(cloneIgnoreId(dbCustomTemplateRequest, o -> o.setApprovedTemplateId(null)));
        // 测试 requestedAt 不匹配
        customTemplateRequestMapper.insert(cloneIgnoreId(dbCustomTemplateRequest, o -> o.setRequestedAt(null)));
        // 测试 reviewedAt 不匹配
        customTemplateRequestMapper.insert(cloneIgnoreId(dbCustomTemplateRequest, o -> o.setReviewedAt(null)));
        // 测试 reviewerComments 不匹配
        customTemplateRequestMapper.insert(cloneIgnoreId(dbCustomTemplateRequest, o -> o.setReviewerComments(null)));
        // 测试 remarks 不匹配
        customTemplateRequestMapper.insert(cloneIgnoreId(dbCustomTemplateRequest, o -> o.setRemarks(null)));
        // 测试 createTime 不匹配
        customTemplateRequestMapper.insert(cloneIgnoreId(dbCustomTemplateRequest, o -> o.setCreateTime(null)));
        // 准备参数
        CustomTemplateRequestPageReqVO reqVO = new CustomTemplateRequestPageReqVO();
        reqVO.setAgentId(null);
        reqVO.setRequestedTemplateDetails(null);
        reqVO.setStatus(null);
        reqVO.setApprovedTemplateId(null);
        reqVO.setRequestedAt(null);
        reqVO.setReviewedAt(null);
        reqVO.setReviewerComments(null);
        reqVO.setRemarks(null);
        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

        // 调用
        PageResult<CustomTemplateRequestDO> pageResult = customTemplateRequestService.getCustomTemplateRequestPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbCustomTemplateRequest, pageResult.getList().get(0));
    }

}