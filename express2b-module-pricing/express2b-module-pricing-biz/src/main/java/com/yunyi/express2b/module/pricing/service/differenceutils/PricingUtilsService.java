package com.yunyi.express2b.module.pricing.service.differenceutils;


import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.module.agent.api.dto.DetailShareReqDO;
import com.yunyi.express2b.module.pricing.controller.admin.pricingrule.vo.PricingRulePageReqVO;

/**
 * 分润/差价 规则 Service 接口
 *
 * <AUTHOR>
 */
public interface PricingUtilsService {

    /**
     * 分润公共方法
     *
     * @param reqDO
     * @return
     */
    CommonResult<String> sharebenefit(DetailShareReqDO reqDO);


    /**
     * 差价计算公共方法
     *
     * @param reqVO
     * @return
     */
    CommonResult<PricingRulePageReqVO> difference(PricingRulePageReqVO reqVO);

}