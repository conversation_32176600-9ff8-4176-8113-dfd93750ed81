package com.yunyi.express2b.module.express.utils;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/26 15:12
 */

public class OrderConstants {


    // 设定一个常量，表示多少时间（单位：分钟）后未支付的订单会被取消
    public static final long CANCEL_ORDER_TIMEOUT = 30;
    //存储excel文件url
    public static String EXCEL_UPLOAD_URL;
    //存储手动粘贴的地址内容
    public static String content;

    public static Integer channelId = 1;
    //未进行分润的状态
    public static Integer detailShareStatus = 1;

    public static Integer agentId = 1;

    public static Integer targetType = 1;

    public static String msg = "ok";

    public static Integer CODE = 00000;

    public static Integer successCode = 00000;

    public static String code = "00000";

    public static String noUserId = "10006";

    public static String ORDERKEY = "OrderKey";

    public static String PRICINGKEY = "PricingKey";

    public static String PROFITKEY = "ProfitKey";
}
