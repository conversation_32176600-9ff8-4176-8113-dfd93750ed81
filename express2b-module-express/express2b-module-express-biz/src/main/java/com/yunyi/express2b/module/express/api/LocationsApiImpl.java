package com.yunyi.express2b.module.express.api;

import com.yunyi.express2b.module.express.api.locations.LocationsApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * LocationsApiImpl
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/9 上午10:46
 */
@Slf4j
@Service
public class LocationsApiImpl implements LocationsApi {

    /**
     * 查询地址
     * @param name
     * @return
     */
    @Override
    public String selectLocation(String name) {
        return "";
    }
}