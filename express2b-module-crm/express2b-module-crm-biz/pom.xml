<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.yunyi.express2b</groupId>
        <artifactId>express2b-module-crm</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>express2b-module-crm-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
    </description>

    <dependencies>
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-module-crm-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>api-unified-login</artifactId>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-biz-tenant</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-websocket</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId> <!-- 代码生成器，使用它解析表结构 -->
        </dependency>

        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 工具类相关 -->

        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId> <!-- 实现代码生成 -->
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-monitor</artifactId>
        </dependency>

        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-server</artifactId> <!-- 实现 Spring Boot Admin Server 服务端 -->
        </dependency>

        <!-- 三方云服务相关 -->
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId> <!-- 文件客户端：解决 ftp 连接 -->
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId> <!-- 文件客户端：解决 sftp 连接 -->
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId><!-- 文件客户端：解决阿里云、腾讯云、minio 等 S3 连接 -->
        </dependency>

        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId> <!-- 文件客户端：文件类型的识别 -->
        </dependency>
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-module-express-api</artifactId>
            <version>${revision}</version>
        </dependency>


    </dependencies>

</project>
