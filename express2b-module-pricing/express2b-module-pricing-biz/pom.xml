<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.yunyi.express2b</groupId>
        <artifactId>express2b-module-pricing</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>express2b-module-pricing-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        定价模块业务实现，包括Controller、Service、Mapper等
    </description>

    <dependencies>
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-module-pricing-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-module-commission-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-module-agent-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-biz-tenant</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-excel</artifactId>
        </dependency>
        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yunyi.express2b</groupId>
            <artifactId>express2b-module-wallet-biz</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

</project> 