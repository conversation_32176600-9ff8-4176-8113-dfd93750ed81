package com.yunyi.express2b.module.express.dal.dataobject.brandscheduledconfig;

import lombok.*;

import java.time.LocalTime;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 快递品牌预约配置 DO
 *
 * <AUTHOR>
 */
@TableName("express2b_brand_scheduled_config")
@KeySequence("express2b_brand_scheduled_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrandScheduledConfigDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 快递品牌标识
     */
    private String brandKey;
    /**
     * 预约天数
     */
    private Integer scheduledDays;
    /**
     * 预约开始时间
     */
    private LocalTime scheduledTimeStart;
    /**
     * 预约结束时间
     */
    private LocalTime scheduledTimeEnd;
    /**
     * 预约间隔分钟数
     */
    private Integer intervalMinutes;

}