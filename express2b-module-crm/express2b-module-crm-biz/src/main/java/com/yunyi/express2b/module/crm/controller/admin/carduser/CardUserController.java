package com.yunyi.express2b.module.crm.controller.admin.carduser;

import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;
import com.yunyi.express2b.module.crm.controller.admin.carduser.vo.CardUserRespVO;
import com.yunyi.express2b.module.crm.controller.app.v1.carduser.vo.CardUserPageReqVO;
import com.yunyi.express2b.module.crm.controller.app.v1.carduser.vo.CardUserSaveReqVO;
import com.yunyi.express2b.module.crm.dal.dataobject.carduser.CardUserDO;
import com.yunyi.express2b.module.crm.service.carduser.CardUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;
/**
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 持卡用户")
@RestController
@RequestMapping("/crm/card-user")
@Validated
public class CardUserController {

    @Resource
    private CardUserService cardUserService;

    @PostMapping("/create")
    @Operation(summary = "创建持卡用户")
    @PreAuthorize("@ss.hasPermission('express:card-user:create')")
    public CommonResult<Boolean> createCardUser(@Valid @RequestBody CardUserSaveReqVO createReqVO) {
        return success(cardUserService.createCardUser(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新持卡用户")
    @PreAuthorize("@ss.hasPermission('express:card-user:update')")
    public CommonResult<Boolean> updateCardUser(@Valid @RequestBody CardUserSaveReqVO updateReqVO) {
        cardUserService.updateCardUser(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除持卡用户")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('express:card-user:delete')")
    public CommonResult<Boolean> deleteCardUser(@RequestParam("id") Long id) {
        cardUserService.deleteCardUser(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得持卡用户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('express:card-user:query')")
    public CommonResult<CardUserRespVO> getCardUser(@RequestParam("id") Long id) {
        CardUserDO cardUser = cardUserService.getCardUser(id);
        return success(BeanUtils.toBean(cardUser, CardUserRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得持卡用户分页")
    @PreAuthorize("@ss.hasPermission('express:card-user:query')")
    public CommonResult<PageResult<CardUserRespVO>> getCardUserPage(@Valid CardUserPageReqVO pageReqVO) {
        PageResult<CardUserDO> pageResult = cardUserService.getCardUserPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CardUserRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出持卡用户 Excel")
    @PreAuthorize("@ss.hasPermission('express:card-user:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCardUserExcel(@Valid CardUserPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CardUserDO> list = cardUserService.getCardUserPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "持卡用户.xls", "数据", CardUserRespVO.class,
                        BeanUtils.toBean(list, CardUserRespVO.class));
    }






}