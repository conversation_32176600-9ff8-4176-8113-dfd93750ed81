package com.yunyi.express2b.module.agent.controller.admin.workbench;

import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.module.agent.controller.admin.workbench.vo.AgentIncomeTrendVo;
import com.yunyi.express2b.module.agent.controller.admin.workbench.vo.AgentOrderTrendVo;
import com.yunyi.express2b.module.agent.controller.admin.workbench.vo.AgentTotalVo;
import com.yunyi.express2b.module.agent.service.agentprofile.AgentProfileService;
import com.yunyi.express2b.module.agent.service.workbench.WorkbenchService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工作台页面展示
 *
 * <AUTHOR>
 * @since 17
 * @version 1.0
 * @since 2025/5/27 上午11:27
 */

@RestController
@RequestMapping("/agent/workbench")
public class WorkbenchController {

    @Resource
    private AgentProfileService agentProfileService;

    @Resource
    private WorkbenchService workbenchService;
    /**
     * 传递代理商ID查询代理商总数,代理商下单的数量,代理商直属下级的数量显示
     * @return
     */
    @GetMapping("/total")
    @Operation(summary = "查询代理商总数,代理商下单的数量,代理商直属下级的数量,代理商间接下属数量")
    @PreAuthorize("@ss.hasPermission('agent:profile:query')")
    public CommonResult<AgentTotalVo> searchTotal(@RequestParam("agentId")Long agentId){
        return CommonResult.success(workbenchService.getTotal(agentId));
    }

    /**
     * 订单趋势图显示
     * @return
     */
    @GetMapping("/order-trend")
    @Operation(summary = "订单趋势图显示")
    @PreAuthorize("@ss.hasPermission('agent:profile:query')")
    public CommonResult<AgentOrderTrendVo> orderTrendDisplay(Long agentId){
        return CommonResult.success(workbenchService.getorderTrendDisplay(agentId));
    }
    /**
     * 收入趋势图显示
     * @return
     *
     */
    @GetMapping("/income-trend")
    @Operation(summary = "收入趋势图显示")
    @PreAuthorize("@ss.hasPermission('agent:profile:query')")
    public CommonResult<AgentIncomeTrendVo> incomeTrendDisplay(Long agentId){
        return CommonResult.success(workbenchService.getIncomeTrendDisplay(agentId));
    }


}