package com.yunyi.express2b.module.express.controller.admin.workorder;

import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.express.controller.admin.workorder.vo.AdminWorkOrderRespResVO;
import com.yunyi.express2b.module.express.controller.admin.workorder.vo.CallbackReqVO;
import com.yunyi.express2b.module.express.controller.admin.workorder.vo.WorkOrderUpdateReqVO;
import com.yunyi.express2b.module.express.controller.app.v1.workorder.vo.WorkOrderCreateReqVO;
import com.yunyi.express2b.module.express.controller.app.v1.workorder.vo.WorkOrderPageReqVO;
import com.yunyi.express2b.module.express.controller.app.v1.workorder.vo.AppWorkOrderRespResVO;
import com.yunyi.express2b.module.express.controller.app.v1.workorder.vo.WorkOrderMessageReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.workorder.WorkOrderDO;
import com.yunyi.express2b.module.express.enums.InitiatorTypeEnum;
import com.yunyi.express2b.module.express.service.workorder.WorkOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 -  工单管理")
@RestController
@RequestMapping("/express/work-order")
@Validated

public class WorkOrderController {
    //todo 未添加鉴权
    @Resource
    private WorkOrderService workOrderService;

    @GetMapping("/page")
    @Operation(summary = "管理后台获得工单分页")
    public CommonResult<PageResult<AdminWorkOrderRespResVO>> getWorkOrderPage(@Valid WorkOrderPageReqVO pageReqVO) {
        PageResult<WorkOrderDO> workOrderPage = workOrderService.getWorkOrderPage(false,pageReqVO);
        return success(BeanUtils.toBean(workOrderPage, AdminWorkOrderRespResVO.class));
    }

    @GetMapping("/get")
    @Operation(summary = "管理后台根据id获得工单")
    public CommonResult<AdminWorkOrderRespResVO> getWorkOrderById(@RequestParam("id") Long id) {
        WorkOrderDO workOrderDO = workOrderService.getWorkOrderById(false, id);
        AdminWorkOrderRespResVO bean = BeanUtils.toBean(workOrderDO, AdminWorkOrderRespResVO.class);
        return success(workOrderService.getWorkOrderFile(bean));
    }


    @PostMapping("/create")
    @Operation(summary = "管理后台创建工单")//todo 前端页面差异巨大
    public CommonResult<String> createWorkOrder(@Valid @RequestBody WorkOrderCreateReqVO createReqVO) {
        return success(workOrderService.createWorkOrder(InitiatorTypeEnum.SYSTEM,createReqVO));
    }


    @PutMapping("/update")
    @Operation(summary = "管理后台更新工单")
    public CommonResult<Boolean> updateWorkOrder(@Valid @RequestBody WorkOrderUpdateReqVO updateReqVO) {
        int i = workOrderService.updateWorkOrder(updateReqVO);
        return success(i>0);
    }


    @DeleteMapping("/delete")
    @Operation(summary = "管理后台删除工单")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteSubscribe(@RequestParam("id") Long id) {
        workOrderService.deleteWorkOrder(id);
        return success(true);
    }

    @PostMapping("/callback")
    @Operation(summary = "单方平台回调")
    @PermitAll
    public CommonResult<Boolean> createWorkOrder(@Valid @RequestBody CallbackReqVO createReqVO) {
        return success(workOrderService.callback(createReqVO));
    }


}