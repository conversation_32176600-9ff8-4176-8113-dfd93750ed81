package com.yunyi.express2b.module.express.data;

import com.yunyi.express2b.module.express.dal.dataobject.paymentorder.PaymentOrderDO;

import java.time.LocalDateTime;

/**
 * 创建支付订单的测试数据
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/12 09:39
 */
public class GeneratePayment {

    public static PaymentOrderDO generatePaymentSuccess(Long orderId) {
        return PaymentOrderDO.builder()
        .id(1L)
        .transactionNo("TXN202504110001")
        .outTransactionNo("EXP202504110001")
        .orderId(orderId) // 关联订单1
        .amount(100) // 支付金额
        .status(String.valueOf(1)) // 支付状态：1 表示成功
        .paymentMethod("ONLINE") // 支付方式：在线支付
        .content("支付订单EXP202504110001的费用")
        .fee(2) // 支付手续费
        .paymentNumber("PAY1234567890")
        .paymentSubmitTime(LocalDateTime.parse("2025-04-11T10:00:00"))
        .lastStatusUpdateTime(LocalDateTime.parse("2025-04-11T10:01:00"))
        .reconciliationId(1L)
        .reconciliationTime(LocalDateTime.parse("2025-04-12T00:00:00"))
        .build();
    }
}
