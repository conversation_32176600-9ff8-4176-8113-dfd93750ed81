package com.yunyi.express2b.module.express.service.pay;

import com.alibaba.fastjson.JSONObject;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.common.util.servlet.ServletUtils;
import com.yunyi.express2b.framework.security.core.LoginUser;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.crm.api.card.paycard.PayCardApi;
import com.yunyi.express2b.module.crm.api.card.paycard.vo.PayCardVo;
import com.yunyi.express2b.module.express.controller.admin.paymentorder.vo.UnifiedOrderResponseNew;
import com.yunyi.express2b.module.express.controller.app.v1.paymenyorder.vo.PayOrderReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.paymentorder.PaymentOrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.relorderpayment.RelOrderPaymentDO;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.paymentorder.PaymentOrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.relorderpayment.RelOrderPaymentMapper;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import com.yunyi.framework.api.base.config.PaymentConfig;
import com.yunyi.framework.api.login.api.payment.PaymentGatewayApi;
import com.yunyi.framework.api.login.api.payment.vo.UnifiedOrderRequest;
import com.yunyi.framework.api.login.utils.TransactionNoGenerator;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import static com.yunyi.express2b.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.INSERT_PAYMENT_ORDER_FAIL;
import static com.yunyi.express2b.module.express.enums.PaymentStatusTypeEnum.NOTPAY;
import static com.yunyi.framework.api.login.utils.TransactionNoGenerator.*;

/**
 * 支付service实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/11 下午1:12
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PayServiceImpl implements PayService {
    private final Long CLIENTID = 16L;
    //微信支付type
    private final Integer PAYTYPE = 1;

    private final PaymentOrderMapper paymentOrderMapper;

    private final PaymentGatewayApi paymentGateway;

    private final PayCardApi payCardApi;

    private final RelOrderPaymentMapper relOrderPaymentMapper;

    private final PaymentConfig paymentConfig;

    @Resource
    private OrderMapper orderMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UnifiedOrderResponseNew payOrder(PayOrderReqVO payOrderReqVO) {
        //获取实收金额
        Integer receivedAmount = payOrderReqVO.getAmount();
        log.info("【需要支付的订单金额】:{}",receivedAmount);
        //生成交易编号
        String transactionNo = TransactionNoGenerator.generate(PAY, WX, EB2, new Date(), receivedAmount);
        Integer totalReceivedAmount = 0;

        List<Long> orderIds = payOrderReqVO.getOrderIds();
        for (Long orderId : orderIds) {
            //创建支付单,BeanUtils.toBean(payOrderReqVO, PaymentOrderDO.class);
            //5.17进行了修改，从订单表获取实收金额
            OrderDO orderDO = orderMapper.selectOrderById(orderId);
            if (orderDO == null) {
                throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
            }
            // 累加每个订单的实收金额
            Integer receivedAmount1 = orderDO.getReceivedAmount();
            if (receivedAmount1 == null || receivedAmount1 <= 0) {
                throw exception(ErrorCodeConstants.ORDER_AMOUNT_INVALID);
            }
            //totalReceivedAmount += receivedAmount;
            PaymentOrderDO paymentOrder = new PaymentOrderDO();
            paymentOrder.setOrderId(orderId);
            //5.17进行了修改，从订单表获取实收金额
            paymentOrder.setAmount(receivedAmount);
            paymentOrder.setStatus(NOTPAY.getStatus());
            paymentOrder.setPaymentSubmitTime(LocalDateTime.now());
            paymentOrder.setIp(ServletUtils.getClientIP());
            paymentOrder.setClientId(CLIENTID);
            paymentOrder.setTransactionNo(transactionNo);
            //05.19进行修改
            int rows = paymentOrderMapper.insert(paymentOrder);
            if (rows == 0) {
                //插入失败抛出异常
                throw exception(INSERT_PAYMENT_ORDER_FAIL);
            }


            //循环orderId list
            // for (Long orderId : payOrderReqVO.getOrderIds()) {
            //插入支付单和订单关联, todo 批量处理orderId，
            RelOrderPaymentDO relOrderPaymentDO = new RelOrderPaymentDO();
            relOrderPaymentDO.setOrderId(orderId);
            //循环插入orderId
            relOrderPaymentDO.setPaymentOrderId(paymentOrder.getId());
            relOrderPaymentDO.setType(payOrderReqVO.getType());
            if (relOrderPaymentMapper.insert(relOrderPaymentDO) == 0) {
                throw exception(INSERT_PAYMENT_ORDER_FAIL);
            }

            //调用扣减次卡接口 卡流水表里面插入多条数据
            if (payOrderReqVO.getCardNumber() != null) {
                payCardApi.payCard(new PayCardVo(orderId, payOrderReqVO.getCardNumber()));
            }
            //}
        }

        //获取用户
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) {
            throw exception(UNAUTHORIZED);
        }
        Long personId = loginUser.getId();

        //组装请求参数
        UnifiedOrderRequest request = UnifiedOrderRequest.builder()
                .user(Math.toIntExact(personId))
                .payOrderSn(transactionNo)
                .amount(payOrderReqVO.getAmount())
                .notifyUrl(paymentConfig.getPayNotifyUrl())
                .ip(ServletUtils.getClientIP())
                .body(payOrderReqVO.getBody())
                .clientId(CLIENTID.toString())
                .attach(payOrderReqVO.getContent())
                .payType(PAYTYPE)
                .build();

        //5.17进行了修改
        //判断每个订单支付金额的总和支付请求的金额是否一致
//        if (totalReceivedAmount != receivedAmount) {
//            throw exception(ErrorCodeConstants.ORDER_AMOUNT_INVALID);
//        }
        //发送请求
        CommonResult<JSONObject> jsonObjectCommonResult = paymentGateway.unifiedOrder(request);

        if (jsonObjectCommonResult == null) {
            throw exception(ErrorCodeConstants.ADDRESS_PARSE_IS_EMPTY);
        }
        JSONObject data = jsonObjectCommonResult.getData();
        Object resultData = data.get("data");
        JSONObject innerData = (JSONObject) resultData;
        String aPackage = innerData.getString("package");
        UnifiedOrderResponseNew unifiedOrderResponseNew = BeanUtils.toBean(resultData, UnifiedOrderResponseNew.class);
        unifiedOrderResponseNew.setAPackage(aPackage);
        return unifiedOrderResponseNew;
    }
}
