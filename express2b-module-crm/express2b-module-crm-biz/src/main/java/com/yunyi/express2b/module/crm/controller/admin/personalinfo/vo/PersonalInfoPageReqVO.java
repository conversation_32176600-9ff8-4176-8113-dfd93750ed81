package com.yunyi.express2b.module.crm.controller.admin.personalinfo.vo;

import com.yunyi.express2b.framework.common.validation.InEnum;
import com.yunyi.express2b.framework.excel.core.annotations.DictFormat;
import com.yunyi.express2b.module.crm.enums.AuditStateEnum;
import com.yunyi.express2b.module.infra.enums.DictTypeConstants;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
/**
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 个人信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PersonalInfoPageReqVO extends PageParam {

    @Schema(description = "企业账户信息", example = "3053")
    private Long companyId;

    @Schema(description = "账户id", example = "3203")
    private Long memberId;

    @Schema(description = "姓名", example = "李四")
    private String memberName;

    @Schema(description = "联系方式")
    private String memberPhone;

    @Schema(description = "身份证号")
    private String memberIdCard;

    @Schema(description = "身份证正面")
    private String frontCard;

    @Schema(description = "身份证反面")
    private String reverseCard;

    @Schema(description = "状态 0-待审核 1- 审核成功 2- 审核失败")
    @InEnum(value = AuditStateEnum.class)
    private Integer state;

    @Schema(description = "审核备注（失败原因）")
    private String remarks;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}