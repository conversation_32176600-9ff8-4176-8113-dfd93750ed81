# B云运力价格系统优化设计方案

## 文档信息

| 版本号 | 修订日期   | 修订人 | 修订说明     |
|--------|------------|--------|--------------|
| v1.0   | 2025-06-18 | 解蕾 | 初始版本创建 |

## 一、设计背景

### 1.1 项目概述

B云运力是面向中小企业的快递寄件服务平台，为日发单量2-10单的商家提供高效、低成本的快递解决方案。其核心功能包括多平台寄件、批量处理、价格优惠等。价格系统作为整个平台的核心组件，直接影响用户体验和平台盈利能力。

### 1.2 现状分析

通过对现有代码的分析，目前的价格计算系统采用了装饰器模式实现阶梯价格和各类优惠的计算：

1. **基础价格计算** (BaseCountImpl)：计算基础成本价加上利润
2. **阶梯价格计算** (LadderCountDecorator)：基于用户当日发单量应用阶梯优惠
3. **次卡优惠计算** (SubCardCountDecorator)：提供首重价格的优惠

虽然当前设计具有一定的灵活性，但在高并发场景下存在性能瓶颈，且随着业务复杂度增加，代码可维护性面临挑战。

### 1.3 优化目标

本设计方案旨在优化B云运力价格系统，实现以下目标：

1. **性能提升**：减少响应时间，提高系统吞吐量，支持高并发业务场景
2. **架构优化**：提高代码可维护性、可扩展性和可测试性
3. **业务扩展**：更灵活地支持未来新增的价格策略和优惠规则
4. **系统稳定性**：增强系统在异常情况下的容错能力
5. **可观测性**：增强系统监控和问题排查能力

## 二、总体设计

### 2.1 设计原则

本次优化设计遵循以下原则：

1. **单一职责原则**：每个组件只负责单一功能，减少组件间耦合
2. **开闭原则**：系统对扩展开放，对修改关闭，便于添加新的价格计算策略
3. **依赖倒置原则**：高层模块不依赖低层模块，都依赖抽象接口
4. **接口隔离原则**：使用多个专门的接口，而不是单一的总接口
5. **最小知识原则**：一个对象应当对其他对象有尽可能少的了解

### 2.2 系统架构

优化后的价格系统采用分层架构，并与外部运力平台集成：

```mermaid
graph TD
    subgraph "B云运力价格系统"
        A[API层] --> B[服务层]
        B --> C[策略层]
        B --> D[数据访问层]
        C --> E[缓存层]
        D --> F[数据库]
        E --> F
    end

    subgraph "外部依赖"
        G[统一运力平台]
    end

    B --> G

    subgraph "API层"
    A1["价格查询API"] --> A2["批量价格查询API"]
    end
    
    subgraph "服务层"
    B1["价格引擎服务"] --> B2["价格规则服务"]
    B1 --> B3["用户服务(内部)"]
    end
    
    subgraph "策略层"
    C1["基础价格策略(调用外部API)"] --> C2["阶梯优惠策略"]
    C2 --> C3["次卡优惠策略"]
    C3 --> C4["会员等级策略"]
    end
    
    subgraph "缓存层"
    E1["本地缓存(Caffeine)"] --> E2["分布式缓存(Redis)"]
    end

    subgraph "数据存储"
        F[("MySQL")]
    end
```

### 2.3 核心组件

1.  **价格引擎（PriceEngine）**：负责协调各个价格计算策略，提供统一的价格计算入口。
2.  **价格计算策略（PriceCalculationStrategy）**：负责具体的价格计算逻辑。
    - **特别说明**：`BasePriceStrategy`将封装对外部**统一运力平台**API的调用，获取基础运价。
3.  **价格规则服务（PricingRuleService）**：负责获取和管理**折扣**规则（阶梯价、会员折扣等）。
4.  **缓存管理器（CacheManager）**：负责缓存策略的实现和管理，重点缓存外部API的响应。
5.  **并发控制器（ConcurrencyController）**：负责控制并发计算和资源利用。

## 三、详细设计

### 3.1 模块设计

#### 3.1.1 价格引擎 (PriceEngine)

价格引擎采用策略模式和责任链模式相结合的设计。

```mermaid
classDiagram
    class PriceEngine {
        -List~PriceCalculationStrategy~ strategies
        +calculatePrice(PriceContext context) PriceResult
    }
    class PriceCalculationStrategy {
        <<interface>>
        +apply(PriceContext context, PriceResult result)
    }
    class BasePriceStrategy {
        -ExpressApi expressApi
        +apply(PriceContext context, PriceResult result)
    }
    class LadderDiscountStrategy {
        +apply(PriceContext context, PriceResult result)
    }
    class MembershipDiscountStrategy {
        +apply(PriceContext context, PriceResult result)
    }
    class CouponStrategy {
        +apply(PriceContext context, PriceResult result)
    }

    PriceEngine o-- "1..*" PriceCalculationStrategy
    PriceCalculationStrategy <|.. BasePriceStrategy
    PriceCalculationStrategy <|.. LadderDiscountStrategy
    PriceCalculationStrategy <|.. MembershipDiscountStrategy
    PriceCalculationStrategy <|.. CouponStrategy
```

-   **PriceEngine**: 核心协调器，注入所有`PriceCalculationStrategy`实现。它会按照预定义的顺序（例如：基础价 -> 阶梯优惠 -> 会员折扣 -> 优惠券）依次调用策略链。
-   **PriceCalculationStrategy**: 统一定义了价格计算策略的接口。
    -   `BasePriceStrategy`实现将注入`ExpressApi` Feign客户端，负责调用外部接口获取基础运费，并将其填充到`PriceResult`中。
    -   其他折扣策略（如`LadderDiscountStrategy`）则基于`BasePriceStrategy`计算出的基础价格进行折扣计算。

#### 3.1.2 价格规则服务 (PricingRuleService)

此服务专门负责价格规则的读取和管理，并集成缓存逻辑。

- **功能**:
  - 提供按条件查询价格规则的方法（如 `getPricingRule(area, weight, expressCode)`）。
  - 内置缓存逻辑，优先从缓存获取规则，缓存未命中则查询数据库并回填缓存。
  - 提供规则更新和失效的接口，保证缓存与数据库的一致性。

### 3.2 数据模型设计

#### 3.2.1 核心数据对象

为支持新的价格计算模型，我们将定义以下核心数据对象(VO/DTO)，遵循单一职责原则。

- **PriceContext (DTO)**: 价格计算上下文，用于封装所有计算所需的输入信息。
  ```java
  // 位于 com.yunyi.express2b.module.express.api.dto.price
  public class PriceContext {
      private Long userId;          // 用户ID
      private String startAreaCode; // 起始地编码
      private String endAreaCode;   // 目的地编码
      private BigDecimal weight;    // 重量 (kg)
      private String expressCode;   // 快递公司编码
      private List<String> couponIds; // 使用的优惠券ID
      // ... 其他可能影响价格的因素
  }
  ```

- **PriceResult (VO)**: 价格计算结果，用于封装计算后的各项价格明细。
  ```java
  // 位于 com.yunyi.express2b.module.express.controller.app.vo.price
  public class PriceResult {
      private BigDecimal totalAmount;       // 渠道成本价/原始总额
      private BigDecimal baseFreight;       // 基础运费（来自外部API）
      private BigDecimal ladderDiscount;    // 阶梯优惠金额
      private BigDecimal membershipDiscount; // 会员折扣金额
      private BigDecimal couponDiscount;    // 优惠券抵扣金额
      private BigDecimal finalAmount;       // 实付金额
      private Map<String, Object> details; // 其他明细
  }
  ```

#### 3.2.2 数据库表设计 (概要)

由于基础运价由外部API提供，原`express_pricing_rule`表不再需要。我们仅需专注于折扣规则的存储。

- **express_discount_rule (折扣规则表)**
  | 字段名 | 类型 | 注释 |
  | --- | --- | --- |
  | `id` | `bigint` | 主键 |
  | `rule_name` | `varchar(100)` | 规则名称 |
  | `discount_type` | `varchar(30)` | 折扣类型 (LADDER, MEMBERSHIP) |
  | `condition_json` | `json` | 触发条件 (如: `{"min_orders": 10, "max_orders": 50}`)|
  | `action_json` | `json` | 折扣动作 (如: `{"discount_amount": 2.0}` 或 `{"discount_percent": 0.95}`) |
  | `...` | | 其他通用字段 |

### 3.3 接口设计

#### 3.3.1 对外接口 (Controller)

- **接口分类**: `app-api`
- **包路径**: `com.yunyi.express2b.module.express.controller.app.v2.price` (建议新开v2版本)
- **接口路径**: `/app-api/v2/prices/calculate`
- **HTTP 方法**: `POST`
- **功能描述**: 根据输入条件计算单个订单的运费详情。
- **请求体**: `CommonResult<PriceContext>`
- **响应体**: `CommonResult<PriceResult>`
- **错误处理**:
  - `GlobalErrorCodeConstants.BAD_REQUEST`: 参数校验失败。
  - `ExpressErrorCodeConstants.PRICE_RULE_NOT_FOUND`: 未找到匹配的运价规则。

---

- **接口路径**: `/app-api/v2/prices/batch-calculate`
- **HTTP 方法**: `POST`
- **功能描述**: 批量计算多个订单的运费详情。
- **请求体**: `CommonResult<List<PriceContext>>`
- **响应体**: `CommonResult<List<PriceResult>>`

#### 3.3.2 模块间接口

- **接口名称**: `PriceApi`
- **方法名称**: `calculatePrice`
- **功能描述**: 供 `order` 模块在创建订单时调用，计算最终价格。
- **输入参数**: `PriceContext` (DTO)
- **返回值**: `PriceResult` (DTO)
- **定义位置**: `express2b-module-express-api`

```java
// com.yunyi.express2b.module.express.api.PriceApi
public interface PriceApi {
    PriceResult calculatePrice(PriceContext context);
}
``` 

### 3.4 性能优化设计

#### 3.4.1 缓存策略

为了在高并发下提升性能，我们将采用多级缓存策略，**重点缓存外部API的响应**。

```mermaid
sequenceDiagram
    participant App as "应用"
    participant Caffeine as "本地缓存"
    participant Redis as "分布式缓存"
    participant CapacityPlatform as "统一运力平台"

    App->>+Caffeine: 1. 查询基础运价
    Caffeine-->>-App: 2. 本地缓存命中，返回结果
    
    alt 本地缓存未命中
        App->>+Redis: 3. 查询基础运价
        Redis-->>-App: 4. Redis命中，返回结果
        App->>Caffeine: 5. 结果写入本地缓存
    end
    
    alt Redis也未命中
        App->>+CapacityPlatform: 6. 调用API查询
        CapacityPlatform-->>-App: 7. 返回API响应
        App->>+Redis: 8. 结果写入Redis
        App->>+Caffeine: 9. 结果写入本地缓存
    end
```

- **缓存键（Cache Key）**: 缓存键的设计至关重要，应包含所有影响基础运价的参数，例如 `price_cache:express_code:start_area:end_area:weight`。
- **本地缓存 (Caffeine)**: 用于缓存最热点的线路价格，减少对分布式缓存和外部API的访问。
- **分布式缓存 (Redis)**: 作为二级缓存，存储更全面的线路价格，进一步降低对外部API的依赖。
- **缓存更新**:
    - **被动更新**: 缓存设置合理的过期时间（例如1小时），到期后自动失效，下次查询时重新从API获取。
    - **主动更新**: 如果运力平台提供价格变更的通知机制（如Webhook或消息队列），可以订阅通知并主动清除相关缓存。

#### 3.4.2 并发处理

对于批量价格计算接口，将使用`CompletableFuture`进行并行计算，充分利用多核CPU资源，缩短响应时间。

```java
// PricingServiceImpl.java 伪代码
public List<PriceResult> batchCalculatePrice(List<PriceContext> contexts) {
    // 使用自定义线程池，避免耗尽Tomcat线程
    ExecutorService executor = ...; 
    
    List<CompletableFuture<PriceResult>> futures = contexts.stream()
        .map(context -> CompletableFuture.supplyAsync(() -> calculatePrice(context), executor))
        .collect(Collectors.toList());

    return futures.stream()
        .map(CompletableFuture::join)
        .collect(Collectors.toList());
}
```

### 3.5 可靠性设计

#### 3.5.1 错误处理与熔断

- **外部API调用保护**:
    - **Sentinel熔断**: 必须对`BasePriceStrategy`中调用外部运力平台的API操作进行重点保护。当API调用失败率或延迟超过阈值时，立即熔断。
    - **降级策略**: 熔断期间，可返回一个默认价格或提示信息（如"价格获取失败，请稍后重试"），或者根据历史数据返回一个估算价。
    - **超时控制**: 对外部API的调用设置严格且合理的超时时间（例如500ms），防止长时间等待拖垮整个系统。

- **优雅降级**: 当价格计算依赖的**内部**下游服务（如用户服务、优惠券服务）不可用时，系统应能优雅降级，例如只计算基础运费，并明确告知用户部分优惠暂时不可用。

#### 3.5.2 数据一致性

- **金额计算**: 所有涉及金额的计算，必须使用`BigDecimal`类型，避免精度丢失问题。
- **缓存数据**: 由于缓存的是外部系统的数据，一致性问题相对简单。主要通过设置合理的过期时间来保证数据的最终一致性。

## 四、实施计划

| 阶段 | 主要任务 | 预计时间 | 产出物 |
|---|---|---|---|
| 1. 准备阶段 | - 详细设计评审 <br/> - 创建项目分支和任务 | 3天 | - 评审通过的设计文档 <br/> - JIRA任务 |
| 2. 开发阶段 | - 数据库表结构变更 <br/> - DTO/VO/Convert定义 <br/> - 价格策略实现 <br/> - 价格引擎和服务层开发 <br/> - 接口开发与联调 | 10天 | - 功能完整的代码 |
| 3. 测试阶段 | - 单元测试编写 <br/> - 集成测试 <br/> - 性能压测 | 5天 | - 测试报告 <br/> - Bug修复记录 |
| 4. 上线阶段 | - 灰度发布 <br/> - 生产环境监控与验证 | 2天 | - 上线报告 |

## 五、风险评估

| 风险点 | 可能性 | 影响程度 | 应对措施 |
|---|---|---|---|
| 价格规则配置复杂导致线上错误 | 中 | 高 | - 加强后台管理功能，提供规则校验和预览功能。<br/> - 上线前进行充分的测试。|
| 缓存数据与数据库不一致 | 低 | 高 | - 采用可靠的缓存更新策略（如订阅binlog）。<br/> - 增加数据核对定时任务。|
| 性能优化后未达到预期 | 中 | 中 | - 在测试阶段进行充分的性能压测，提前发现瓶颈。<br/> - 准备好回滚方案。|
