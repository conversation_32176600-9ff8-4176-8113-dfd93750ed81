package com.yunyi.express2b.module.express.dal.dataobject.invoice;

import lombok.*;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 订单发票 DO
 *
 * <AUTHOR>
 */
@TableName("express2_invoice_order")
@KeySequence("express2_invoice_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceOrderDO extends BaseDO {

    /**
     * 记录ID
     */
    @TableId
    private Long id;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 发票code
     */
    private String invoiceCode;
    /**
     * 发票编号
     */
    private String invoiceNumber;
    /**
     * 发票url
     */
    private String invoiceUrl;
    /**
     * 发票金额
     */
    private Double amount;
    /**
     * 接收邮箱
     */
    private String email;
    /**
     * 开票状态(APPROVAL-待审核, INVOICING-开票中, INVOICED-已开票, INVOICE_FAILED-开票失败, REJECTED-已驳回, CANCELLED-已作废)
     */
    private String invoiceStatus;
    /**
     * 驳回/失败原因
     */
    private String rejectReason;
    /**
     * 审核人ID
     */
    private String reviewerId;
    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;
    /**
     * 开票时间
     */
    private LocalDateTime invoiceTime;
    /**
     * 客服工号
     */
    private String customerServiceNo;

}