package com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorderrelation;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 发票订单关联新增/修改 Request VO")
@Data
public class InvoiceOrderRelationDTO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "8470")
    private Long id;

    @Schema(description = "发票ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18102")
    @NotNull(message = "发票ID不能为空")
    private Long invoiceId;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "订单编号不能为空")
    private String orderNo;

    @Schema(description = "订单金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单金额不能为空")
    private Double orderAmount;

    @Schema(description = "发票金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "发票金额不能为空")
    private Double amount;

    @Schema(description = "创建者")
    private String creator;

}