package com.yunyi.express2b.module.express.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @description: 生成订单号（不好检验码18位）
 * @since 2025/3/24 14:11
 */

public class GenerateOrderNumberUtil {
    public static String generateOrderNumber(String businessType, String userId, String brandId) {
        // 1. 业务类型前缀（2位）
        String businessPrefix = businessType.toUpperCase();

        // 2. 时间戳（6位） - 当前日期格式为YYMMDD
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyMMdd");
        String timestamp = dateFormat.format(new Date());

        // 3. 随机数（6位）
        Random random = new Random();
        String randomPart = String.format("%06d", random.nextInt(1000000));

        // 4. 用户标志（2位） - 用户ID的最后2位
        String userMarker = userId.length() >= 2 ? userId.substring(userId.length() - 2) : userId;

        // 5. 商户标志（2位） - 商户标志的最后2位
        String merchantMarker = brandId.length() >= 2 ? brandId.substring(brandId.length() - 2) : brandId;

        // 拼接前5部分
        StringBuilder orderNumberWithoutCheck = new StringBuilder();
        orderNumberWithoutCheck.append(businessPrefix)
                .append(timestamp)
                .append(randomPart)
                .append(userMarker)
                .append(merchantMarker);

        // 最终订单号(不含检验码)
        return orderNumberWithoutCheck.toString();
    }


    //生成任务id
    /**
     * 生成唯一任务ID
     *
     * @param taskType 任务类型（2位，如 "TS", "JOB"）
     * @param userId   用户ID
     * @param brandId  商户ID
     * @return 唯一任务ID（18位）
     */
    public static String generateTaskId(String taskType, String userId, String brandId) {
        // 1. 类型标识（2位）
        String typePrefix = taskType.length() >= 2 ? taskType.substring(0, 2).toUpperCase() : taskType.toUpperCase();

        // 2. 时间戳（8位）- 格式：HHmmssSS（小时分钟秒+毫秒前两位）
        SimpleDateFormat dateFormat = new SimpleDateFormat("HHmmssSS");
        String timestamp = dateFormat.format(new Date());

        // 3. 随机数（6位）
        Random random = new Random();
        String randomPart = String.format("%06d", random.nextInt(1000000));

        // 4. 用户标识（2位）
        String userMarker = userId.length() >= 2 ? userId.substring(userId.length() - 2) : userId;

        // 5. 商户标识（2位）
        String merchantMarker = brandId.length() >= 2 ? brandId.substring(brandId.length() - 2) : brandId;

        // 拼接成任务ID
        return new StringBuilder()
                .append(typePrefix)
                .append(timestamp)
                .append(randomPart)
                .append(userMarker)
                .append(merchantMarker)
                .toString();
    }
}
