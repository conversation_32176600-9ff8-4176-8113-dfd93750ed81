package com.yunyi.express2b.module.pricing.controller.app.v1.template.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 定价模板新增/修改 Request VO")
@Data
public class TemplateSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "8847")
    private Long id;

    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "模板名称不能为空")
    private String templateName;

    @Schema(description = "加价类型 (1-比例加价，2-固定金额加价-默认)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "加价类型 (1-比例加价，2-固定金额加价-默认)不能为空")
    private Integer markupType;

    @Schema(description = "首重加价值 (固定金额，单位:分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "首重加价值 (固定金额，单位:分)不能为空")
    private Integer firstWeightMarkup;

    @Schema(description = "续重加价值 (固定金额，单位:分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "续重加价值 (固定金额，单位:分)不能为空")
    private Integer additionalWeightMarkup;

    @Schema(description = "平台零售首重价 (单位:分)", requiredMode = Schema.RequiredMode.REQUIRED, example = "17154")
    @NotNull(message = "平台零售首重价 (单位:分)不能为空")
    private Integer platformFirstPrice;

    @Schema(description = "平台零售续重价 (单位:分)", requiredMode = Schema.RequiredMode.REQUIRED, example = "17967")
    @NotNull(message = "平台零售续重价 (单位:分)不能为空")
    private Integer platformAdditionalPrice;

    @Schema(description = "状态 (0-禁用，1-启用)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态 (0-禁用，1-启用)不能为空")
    private Integer status;

    @Schema(description = "描述", example = "随便")
    private String description;

}