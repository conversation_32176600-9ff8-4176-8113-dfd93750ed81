package com.yunyi.express2b.module.agent.dal.dataobject.dailyperformancestat;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 代理商日业绩统计 DO
 *
 * <AUTHOR>
 */
@TableName("agent_daily_performance_stat")
@KeySequence("agent_daily_performance_stat_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DailyPerformanceStatDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 代理商ID
     */
    private Long agentId;
    /**
     * 统计日期
     */
    private LocalDate statDate;
    /**
     * 当日个人完成订单数
     */
    private Integer personalOrderCount;
    /**
     * 当日团队完成订单总数
     */
    private Integer teamOrderCount;
    /**
     * 当日个人订单总金额（终端销售价，单位：分）
     */
    private Integer personalOrderAmount;
    /**
     * 当日团队订单总金额（单位：分）
     */
    private Integer teamOrderAmount;
    /**
     * 当日个人利润（终端销售价-平台零售价-红包，单位：分）
     */
    private Integer personalProfitAmount;
    /**
     * 当日团队利润（下级订单带来的利润，单位：分）
     */
    private Integer teamProfitAmount;
    /**
     * 当日获得的分润收入（单位：分）
     */
    private Integer commissionIncomeAmount;
    /**
     * 当日坏账订单数
     */
    private Integer badDebtCount;
    /**
     * 当日坏账总金额（单位：分）
     */
    private Integer badDebtAmount;
    /**
     * 当日红包核销金额（单位：分）
     */
    private Integer couponDeductAmount;
    /**
     * 当日平台补贴金额（如有，单位：分）
     */
    private Integer subsidyAmount;
    /**
     * 当日提现金额（如有，单位：分）
     */
    private Integer withdrawalAmount;
    /**
     * 当日新增客户数
     */
    private Integer newCustomerCount;
    /**
     * 当日活跃客户数
     */
    private Integer activeCustomerCount;
    /**
     * 当日复购客户数
     */
    private Integer repeatCustomerCount;

}