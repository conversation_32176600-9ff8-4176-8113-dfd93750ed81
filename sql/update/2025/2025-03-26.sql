
-- 朱建安，增加预约取件时间的相关表。
CREATE TABLE `express2b_brand_scheduled_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID', -- 唯一标识每条记录
  `brand_key` varchar(50) NOT NULL COMMENT '快递品牌标识', -- 存储快递品牌标识
  `scheduled_days` int NOT NULL COMMENT '预约天数', -- 预约天数
  `scheduled_time_start` time NOT NULL COMMENT '预约开始时间', -- 预约开始时间
  `scheduled_time_end` time NOT NULL COMMENT '预约结束时间', -- 预约结束时间
  `interval_minutes` int NOT NULL COMMENT '预约间隔分钟数', -- 预约间隔分钟数
  `creator` varchar(64) DEFAULT '' COMMENT '创建者', -- 记录创建者
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', -- 记录创建时间
  `updater` varchar(64) DEFAULT '' COMMENT '更新者', -- 记录更新者
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', -- 记录更新时间
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除', -- 是否已删除
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号', -- 租户编号
  PRIMARY KEY (`id`) USING BTREE -- 主键
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='快递品牌预约配置表';

CREATE TABLE `express2b_order_scheduled_time` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID', -- 唯一标识每条记录
  `order_id` bigint NOT NULL COMMENT '订单ID', -- 存储订单ID
  `brand_key` varchar(50) NOT NULL COMMENT '快递品牌标识', -- 存储快递品牌标识
  `scheduled_date` date NOT NULL COMMENT '预约取件日期', -- 预约取件日期
  `scheduled_time` time NOT NULL COMMENT '预约取件时间', -- 预约取件时间
  `creator` varchar(64) DEFAULT '' COMMENT '创建者', -- 记录创建者
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', -- 记录创建时间
  `updater` varchar(64) DEFAULT '' COMMENT '更新者', -- 记录更新者
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', -- 记录更新时间
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除', -- 是否已删除
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号', -- 租户编号
  PRIMARY KEY (`id`) USING BTREE, -- 主键
  UNIQUE INDEX `order_id_unique`(`order_id` ASC) USING BTREE COMMENT '按订单ID去重', -- 唯一索引，确保同一个订单ID不可重复
  INDEX `brand_key_index`(`brand_key` ASC) USING BTREE COMMENT '品牌标识索引' -- 索引，加速品牌相关查询
) ENGINE=InnoDB AUTO_INCREMENT=1 COMMENT='订单预约取件时间表';


ALTER TABLE express2b_payment_order
ADD COLUMN IP varchar(30) COMMENT 'IP地址',
ADD COLUMN clientId bigint COMMENT '客户端id',
ADD COLUMN body varchar(255) COMMENT '商品信息';

-- 张洋，增加用户信息是否关注微信公众号
ALTER TABLE express2b_personal_info
ADD COLUMN we_status TINYINT NOT NULL DEFAULT 0 COMMENT '关注状态(1-已关注 0-未关注)';