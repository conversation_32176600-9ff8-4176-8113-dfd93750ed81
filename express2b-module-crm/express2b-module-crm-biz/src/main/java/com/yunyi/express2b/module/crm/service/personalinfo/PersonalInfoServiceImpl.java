package com.yunyi.express2b.module.crm.service.personalinfo;
import cn.hutool.core.bean.BeanUtil;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.crm.controller.admin.personalinfo.vo.PersonalInfoPageReqVO;
import com.yunyi.express2b.module.crm.controller.admin.personalinfo.vo.PersonalInfoSaveReqVO;
import com.yunyi.express2b.module.crm.controller.admin.personalinfo.vo.SendMessageVo;
import com.yunyi.express2b.module.crm.dal.dataobject.personalinfo.PersonalInfoDO;
import com.yunyi.express2b.module.crm.dal.mysql.personalinfo.PersonalInfoMapper;
import com.yunyi.express2b.module.infra.api.config.ConfigApi;
import com.yunyi.framework.api.login.api.message.MessageApi;
import com.yunyi.framework.api.login.api.message.vo.MessageRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import java.util.List;
import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.crm.enums.ErrorCodeConstants.PERSONAL_INFO_NOT_EXISTS;

/**
 * 个人信息 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class PersonalInfoServiceImpl implements PersonalInfoService {

    @Resource
    private PersonalInfoMapper personalInfoMapper;

    @Resource
    private MessageApi messageApi;

    @Resource
    private ConfigApi configApi;

    public static  final String PERSON="普通用户";
    public static  final String COMPANY="企业用户";
    public static  final String CLIENTID="会员";
    /**
     * 创建用户
     * */
    @Override
    public Long createPersonalInfo(PersonalInfoSaveReqVO createReqVO) {
        // 插入
        PersonalInfoDO personalInfo = BeanUtils.toBean(createReqVO, PersonalInfoDO.class);
        personalInfoMapper.insert(personalInfo);
        // 返回
        return personalInfo.getId();
    }
    /**
     *更新个人信息
     *  */
    @Override
    public void updatePersonalInfo(PersonalInfoSaveReqVO updateReqVO) {
        // 校验存在
        validatePersonalInfoExists(updateReqVO.getId());
        // 更新
        PersonalInfoDO updateObj = BeanUtils.toBean(updateReqVO, PersonalInfoDO.class);
        personalInfoMapper.updateById(updateObj);
    }
    /**
     * 删除用户
     * */
    @Override
    public void deletePersonalInfo(Long id) {
        // 校验存在
        validatePersonalInfoExists(id);
        // 删除
        personalInfoMapper.deleteById(id);
    }
    /**
     * 验证是否存在
     * */
    private void validatePersonalInfoExists(Long id) {
        if (personalInfoMapper.selectById(id) == null) {
            throw exception(PERSONAL_INFO_NOT_EXISTS);
        }
    }
    /**
     * 获取单个用户信息
     * */
    @Override
    public PersonalInfoDO getPersonalInfo(Long memberId) {
       return personalInfoMapper.selectByMemberId(memberId);
    }

    /**
     * 获取用户分页
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<PersonalInfoDO> getPersonalInfoPage(PersonalInfoPageReqVO pageReqVO) {
        return personalInfoMapper.selectPage(pageReqVO);
    }

    /**
     * 发送消息
     * @param sendMessageVo
     */
    @Override
    public void sendMessage(List<SendMessageVo> sendMessageVo) {
        String clientId = configApi.getConfigValueByKey(CLIENTID);
        for (SendMessageVo messageVo : sendMessageVo) {
            MessageRequest messageRequest = BeanUtil.toBean(messageVo, MessageRequest.class);

            messageRequest.setClientId(Integer.valueOf(clientId));
            //messageRequest.setUser(messageVo.getMemberId());
            messageApi.sendMessage(messageRequest);
        }
    }

    /**
     * 获取用户类型
     * @param memberId
     * @return
     */
    @Override
    public CommonResult<String> getCompany(Long memberId) {
        PersonalInfoDO personalInfoDO =personalInfoMapper.selectById(memberId);
        if(personalInfoDO.getCompanyId()==null){
            return CommonResult.success(PERSON);
        }else{
            return CommonResult.success(COMPANY);
        }
    }

}
