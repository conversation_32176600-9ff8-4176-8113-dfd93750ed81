package com.yunyi.express2b.module.crm.api.card.cardstream;

import com.yunyi.express2b.module.crm.api.card.cardstream.vo.CardStreamSaveReqVO;
import com.yunyi.express2b.module.crm.api.card.cardstream.vo.CardUserVo;


/**
 * CarfStreamApi
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/31 下午3:51
 */

public interface CardStreamApi {
    /**
     * 创建卡流水
     *
     * @param createReqVO
     * @return
     */
    Long createCardStream(CardStreamSaveReqVO createReqVO);

    /**
     * 更新卡流水
     * @param payOrderSn
     * @return
     */
    int updatepayOrderSn(String payOrderSn);

    /**
     * 根据流水号查询卡信息
     * @param payOrderSn
     * @return
     */
    CardUserVo selectpayOrderSn(String payOrderSn);

}
