package com.yunyi.express2b.module.express.service.pay.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 退款接口响应dto
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/15 上午9:14
 */
@Data
@AllArgsConstructor
public class RefundOrderRespDTO {
    /**
     * 退款状态
     * 0:发起退款成功 1:发起退款失败
     */
    private Integer status;

    /**
     * 退款金额
     */
    private Integer amount;

    public static RefundOrderRespDTO success(Integer amount) {
        return new RefundOrderRespDTO(0, amount);
    }
}
