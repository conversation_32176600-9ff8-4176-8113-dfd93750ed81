<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="getOrdersCount"
            resultType="com.yunyi.express2b.module.express.controller.app.v1.order.vo.OrdersCountQeqVO">
        SELECT
            /* 发单量 */
            SUM(CASE
                    WHEN o.status IN
                         ('CREATED', 'NOTPAY', 'PAID',
                          'PICKUP_PENDING', 'WAIT', 'PENDING_ADDITIONAL_FEE',
                          'IN_TRANSIT', 'PARTIAL_PAYMENT_PENDING', 'CANCELED_AFTER_PAYMENT',
                          'REFUND_PROCESSING', 'COMPLETED', 'REFUNDED', 'INTERCEPTED')
                        THEN 1
                    ELSE 0
                END)                                                     AS placedOrders,
            /* 已完成 */
            SUM(CASE
                    WHEN o.status IN
                         ('COMPLETED')
                        THEN 1
                    ELSE 0
                END)                                                     AS deliveredOrders,
            /* 已签收 */
            SUM(CASE WHEN o.status = 'COMPLETED' THEN 1 ELSE 0 END)      AS completedOrders,
            /* 待付款 */
            SUM(CASE
                    WHEN o.status IN
                         ('CREATED', 'NOTPAY', 'PENDING_ADDITIONAL_FEE')
                        THEN 1
                    ELSE 0
                END)                                                     AS pendingPaymentOrders,
            /* 进行中 */
            SUM(CASE
                    WHEN o.status IN
                         ('PAID', 'WAIT', 'PICKUP_PENDING', 'PARTIAL_PAYMENT_PENDING', 'IN_TRANSIT')
                        THEN 1
                    ELSE 0
                END)                                                     AS inProgressOrders,
            /* 运输中 */
            SUM(CASE WHEN o.status = 'IN_TRANSIT' THEN 1 ELSE 0 END)     AS intransitOrders,
            /* 已取消 */
            SUM(CASE
                    WHEN o.status IN
                         ('CANCELED_AFTER_PAYMENT', 'CANCELED', 'REFUND_PROCESSING', 'REFUNDED')
                        THEN 1
                    ELSE 0
                END)                                                     AS canceledOrders,
            /* 待揽收（待取件） */
            SUM(CASE WHEN o.status = 'PICKUP_PENDING' THEN 1 ELSE 0 END) AS pickupPendingOrders
        FROM express2b_order o
        WHERE o.personal_id = #{personalId}
    </select>
    <select id="selectUnPayOrder" resultType="com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO">
        select o.id,
               o.order_no                as orderNo,
               o.batch_id                as batchId,
               o.personal_id             as personalId,
               o.brand_id                as brandId,
               o.pricing_key             as pricingKey,
               o.profit_key              as profitKey,
               o.status                  as status,
               o.cost_amount             as costAmount,
               o.retail_amount           as retailAmount,
               o.received_amount         as receivedAmount,
               o.discount_amount         as discountAmount,
               o.agent_commission        as agentCommission,
               o.group_leader_commission as groupLeaderCommission,
               o.tracking_number         as trackingNumber,
               o.insured_amount          as insuredAmount,
               o.receiver_staff_name     as receiverStaffName,
               o.pay_type                as payType,
               o.member_ip               as memberIp,
               po.status                 as paymentStatus
        from express2b_order o
                 left join express2b_payment_order po
                           on po.order_id = o.id
        where o.personal_id = #{personalId}
          and po.status = #{status}
    </select>

    <select id="selectStatusOrders" resultType="java.lang.Integer">
        SELECT IFNULL(SUM(received_amount), 0) AS receivedAmount
        FROM express2b_order
        WHERE personal_id = #{personalId}
        AND status IN
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="startTime != null and endTime != null">
            AND create_time BETWEEN #{startTime} AND #{endTime}
        </if>
    </select>


    <select id="selectOrderDetails"
            resultType="com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO">
        select o.id,
               o.order_no                as orderNo,
               o.brand_id                as brandId,
               o.status                  as status,
               o.cost_amount             as costAmount,
               o.retail_amount           as retailAmount,
               o.received_amount         as receivedAmount,
               o.discount_amount         as discountAmount,
               o.agent_commission        as agentCommission,
               o.group_leader_commission as groupLeaderCommission,
               o.tracking_number         as trackingNumber,
               o.insured_amount          as insuredAmount,
               o.receiver_staff_name     as receiverStaffName,
               o.pay_type                as payType,
               o.create_time             as createTime,
               o.ucm_order_sn            as ucmOrderSn
        from express2b_order o
                 left join express2b_products po on po.order_id = o.id
        where o.id = #{id}
          and o.personal_id = #{userId}
    </select>

    <select id="selectOrderByPaymentOrderId"
            resultType="com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO">
        select o.id,
               o.order_no            as orderNo,
               o.status              as status,
               o.cost_amount         as costAmount,
               o.retail_amount       as retailAmount,
               o.received_amount     as receivedAmount,
               o.discount_amount     as discountAmount,
               o.agent_commission    as agentCommission,
               o.insured_amount      as insuredAmount,
               o.receiver_staff_name as receiverStaffName,
               o.pay_type            as payType,
               o.create_time         as createTime,
               o.ucm_order_sn        as ucmOrderSn,
               o.card_number         as cardNumber
        from express2b_order o
                 left join express2b_payment_order po on po.order_id = o.id
        where po.id = #{paymentOrderId}
    </select>
    <select id="selectOrderCounts"
            resultType="com.yunyi.express2b.module.express.api.order.vo.OrderCountsResponse">
        SELECT
            /* 总订单数 */
            SUM(CASE
                    WHEN o.status IN
                         ('CREATED', 'PAID',
                          'PICKUP_PENDING', 'WAIT', 'PENDING_ADDITIONAL_FEE',
                          'IN_TRANSIT', 'PARTIAL_PAYMENT_PENDING', 'CANCELED_AFTER_PAYMENT',
                          'REFUND_PROCESSING', 'COMPLETED', 'REFUNDED', 'INTERCEPTED')
                        THEN 1
                    ELSE 0
                END) AS selectAllOrderCount,
            /* 已完成订单数 */
            SUM(CASE
                    WHEN o.status IN
                         ('COMPLETED')
                        THEN 1
                    ELSE 0
                END) AS selectCompletedOrderCount,
            /* 已支付订单数 (目前记录的是曾经有过支付动作的状态订单)*/
            SUM(CASE
                    WHEN o.status IN
                         ('PAID', 'WAIT', 'PICKUP_PENDING', 'IN_TRANSIT', 'PARTIAL_PAYMENT_PENDING',
                          'CANCELED_AFTER_PAYMENT', 'REFUND_PROCESSING', 'COMPLETED', 'REFUNDED')
                        THEN 1
                    ELSE 0
                END) AS selectPaidOrderCount
        FROM express2b_order o
        WHERE o.agent_id = #{agentId}
    </select>

</mapper>