<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunyi.express2b.module.express.dal.mysql.brandscheduledconfig.BrandScheduledConfigMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <!-- 使用JOIN替代子查询，提升查询性能 -->

    <select id="selectTime"
            resultType="com.yunyi.express2b.module.express.dal.dataobject.brandscheduledconfig.BrandScheduledConfigDO">
        SELECT
            ebc.brand_key,
            ebc.scheduled_days,
            ebc.scheduled_time_start,
            ebc.scheduled_time_end,
            ebc.interval_minutes
        FROM
            express2b_brand_scheduled_config ebc
        WHERE
            ebc.brand_key = #{brandKey}
    </select>
</mapper>