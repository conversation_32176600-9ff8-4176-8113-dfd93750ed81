# B云运力价格方案

- 2025-03-17，解蕾
   - 更新阶梯运价的业务逻辑说明。
   - 纠正佣金计算基数为实收运价-成本价
---

为提高用户粘性，增加下单量，提高市场竞争力，优先考虑阶梯价方案实现小B云运力的发件价格。目前考虑以下几种方案，请各位同事评审，积极给出意见。

# **运价计算基础公式**

成本运价 = 地址首重价 + 重量加价

零售价 = 运力资源价格清单 + 利润指标

实收运价 = 零售价 - 活动优惠 - 用户等级优惠

代理商佣金 = （实收运价 - 成本价） \*  分配利润

团长佣金 = （实收运价 - 成本价） \*  分配利润

 注意：代理商及团长分润以与实际运价为准，在进行价格组成时，需精算运费组成，避免资金风险。

![image](assets/resources/FNoFIbKWVYm_t-xLuW9JkxJ968XEw0sZW2IVRG2tUng.png)



# 一、成本运价

本系统将对接多个快递企业资源，对同一家快递企业有也多个接口的情况，在本系统中统一称呼为运力资源，对所有运力资源统一运价变量，抽象为**首重，首重价，续重，续重价**。在价格计算形式上，会存在有接口获取与本地计算两种情况。

地址首重及重量加价：出发地到目的地，这两个地址会出现省-省，市-市，暂不考虑省-市，和市-省的情况。

在计算时，首先判定货品是否超过首重，未超过首重则只收取首重价；如超过首重，则对超出部分根据续重价进行计算，每超过一个续重单位，进行一次加价。

特殊商品加价是直需要特殊途径运输的货物，如冷链运输情况，快递公司额外收取的费用。

注意：对于大体积清重量的货品，我们称为轻浮物，对待轻浮物系统将根据体积转化公式，将物品体积转化为重量后继续计算。

 注意：成本运价不可对外展示！成本运价仅用于企业盈利计算，不参与其他运算，且该价格均由运力接口获取。

1. ## 运费计算公式

```plain
成本运价 = 地址首重价 + 重量加价 + 特殊商品加价
```
 
2. ## 分步计算逻辑

-  计费重量计算

```plain
体积重量 = (长 × 宽 × 高) / 体积转化系数
计费重量 = max(实际重量, 体积重量)
```
 
-  基础运费计算 （地址对首重/续重规则）：  续重价

```plain
if 计费重量 ≤ 首重:
    基础运费 = 首重价
else:
    续重次数 = ceil((计费重量 - 首重) / 续重)
    基础运费 = 首重价 + 续重次数 × 续重价
```
 
3. ## 精度规则

- 重量值保留2位小数（四舍五入）
- 金额保留2位小数（向上取整）

4. ## 公式变量说明

| **变量名**   | **含义**                                              |
|--------------|-------------------------------------------------------|
| 实际重量     | 货品实际称重值（单位：kg）                               |
| 体积重量     | 通过体积转换公式计算的重量值（单位：kg）                 |
| 体积转化系数 | 快递企业定义的轻抛比（如 6000, 5000，单位：cm³/kg）       |
| 首重         | 地址对（省-省/市-市）的首重阈值（单位：kg）                |
| 首重价       | 地址对对应的首重价格（单位：元）                         |
| 续重         | 地址对对应的续重单位（单位：kg，如每 1kg 为一个续重单位） |
| 续重价       | 地址对对应的续重单价（单位：元）                         |

5. ## 示例场景

普通商品（非轻浮物）：实际重量 3kg，体积重量 2kg，首重（省-省）= 1kg/10元，续重=1kg/5元。计费重量 = max(3,2) = 3kg基础运费 = 10元 + ceil((3-1)/1) × 5 = 10 + 10 = 20元

轻浮物：实际重量 2kg，体积重量= (40×30×20)/5000=4.8kg，首重（市-市）= 1kg/8元，续重=0.5kg/3元。计费重量 = max(2,4.8) = 4.8kg → 向上取整为5kg（部分企业规则）基础运费 = 8元 + ceil((5-1)/0.5) × 3 = 8 + (4/0.5=8次) ×3 = 8+24=32元

# 二、零售价

 该模块通过“运力资源价格清单”配置，动态生成零售价。清单包含每条线路的成本运价、企业利润及续重利润比例，零售价基于首重单价、企业利润和续重价计算生成，支持一次性加价和按比例续重利润。系统提供在线配置和Excel上传功能，实现批量管理和实时价格更新，确保零售价的精确性和业务灵活性，满足企业盈利目标与运营效率的双重需求。

1. ## 组成

- **成本运价参数**：由运力企业提供的运输成本价，是零售价计算的基础。
- **企业利润**：企业在每笔业务中期望的盈利目标金额，分为两部分：
   - **一次性加价**：在首重价中直接加入企业利润。
   - **续重利润比例**：在续重单价基础上增加固定利润。
- **线路配置**：每一条支持的线路都需配置对应的成本运价、企业利润和续重利润。

2. ## 生成规则

- **基础零售价（首重价）**：成本运价中的首重单价 + 企业利润。
- **续重价**：续重单价 × 续重次数，每次续重都会加收续重利润。
- 总零售公式

 $总零售价 = （首重单价 + 企业利润） + （续重单价 × 续重次数 × 续重利润比例）$ 

3. ## 配置方式

- **在线配置**：支持用户通过系统界面直接配置“运力资源价格清单”。
- **Excel上传**：支持用户通过上传Excel文件批量配置清单内容。

4. ## 实现逻辑
1. **配置管理**：系统提供功能和模板，允许用户配置或上传“运力资源价格清单”。
2. **价格计算**：根据清单中的参数（成本运价、企业利润、续重利润比例）自动生成零售价。
3. **线路适配**：每条线路的价格计算需基于其单独配置的参数。
4. **价格更新**：当清单发生变化时，系统需即时更新零售价。
5. ## 风控点

- 价格的更新需要超管的短信验证码授权才能生成.
- 对所有的数据操作行为均需留痕。

# 三、实收运价

基础运价是计算用户付款金额，及代理商的分佣比例的基础，在确认运力的成本

1. ## 活动优惠

- ### 阶梯定价（常规）

根据用户日发单量动态调整价格，确保成本覆盖的同时激励用户提升发单量。

#### 实现效果

| 日发单量 | 省内优惠 | 省外优惠 | 逻辑说明                |
|----------|----------|----------|-------------------------|
| 0-1单    | 0元      | 0元      |                         |
| 2-4单    | 0.03元   | 0.02元   | 基础价，确保覆盖成本     |
| 5-9单    | 0.04元   | 0.03元   | 中量折扣，激励用户多发单 |
| ≥10单    | 0.05元   | 0.04元   | 高量优惠，提上用户粘性   |

#### 功能流程

1. 用户下单时，系统自动统计其当日发单量。
2. 根据发单量匹配对应的阶梯档位，计算运价。
3. 向用户展示最终价格，完成订单流程。
4. 在下单界面清晰的提示越多越便宜。

#### 业务逻辑

阶梯价的颗粒度到线路，即为品牌，线路，省内优惠，省外优惠。

按照每日的发单量进行统计，昨日订单量不对今日影响。

数量阶梯式变更，后续的单价不对已下单订单影响。

批量下单根据录入顺序进行批量数量，如批量下单10单，根据录入的顺序第一单无优惠，2-4单有优惠A，5-9单有优惠B。

优惠的金额为固定价。

- ### 广告优惠(待定）

#### 实现效果

通过互联网广告收益降低用户实际支付成本，用户可多次浏览广告，多次降低价格。每次可降低的金额以广告平台的CPM为准。

![image](assets/resources/au7f3NZ0hNjhIibt3hdJM0l_9gSE8P9sugnuu5jG5Y4.png)

#### 优点

-  用户降价感知明显，愿意为低价“买单”。
-  自主选择，降低抵触情绪。
-  玩法灵活，可以结合其他方案一同使用。
-  企业可获取额外收益。

#### 缺点

-  对企业端用户吸引力不足，企业端发件人对价格不铭感，无法吸引用户，如通过积分商城形式补充，额外增加建设成本。
-  技术复杂度与维护成本高 ，需开发独立的广告服务，增加开发难度和运维成本。

2. ## 用户等级优惠

用户等级无需购买，而是根据下单量累计计算。系统每月统计订单量，并在次月根据上月的下单量确定用户等级，等级有效期为一个月。若当月下单量未达到当前等级要求，等级将相应降低；若下单量达到更高一级的要求，则在次月自动升级。简而言之，每月会根据上月的下单量确定用户等级，并在当月享受相应等级的权益。

| 等级名称 | 说明                                  | 单量**需求** | 有效期 |
|----------|---------------------------------------|--------------|--------|
| 骑手级   | 默认级别                              | 无           | 永久   |
| 包裹级   | 吸引小单用户，以基础权益建立粘性       | 50~100单     | 自然月 |
| 货仓级   | 代表中型运力，适配中等发单量客户       | 101~200单    | 自然月 |
| 专列级   | 类比火车专列，服务稳定且规模化（高单量） | ≥200单       | 自然月 |

### 生效与调整逻辑

-  等级计算周期 ：每月1日至最后1日的累计下单量。
-  升级机制 ：
-  每月1日统计上月订单量，判定当月等级（如 5月等级由4月单量决定 ）。
-  若上月单量达到更高等级门槛，次月1日自动升级并解锁权益。
-  保级机制 ：
-  当前等级权益有效期内（如5月为黄金级），若当月订单量未达当前等级最低要求（如5月发单量为180单＜黄金级门槛201单）， 次月（6月）自动降级 至匹配的单量对应等级（白银级）。
-  多周期连续考核 ：
-  用户需在 每个自然月 均满足上一个月的单量要求，否则后续月份将动态调整。

权益示例（按等级差异化）

| **权益类型** | **骑手级** | **包裹级** | **货仓级**      | **专列级**     |
|--------------|------------|------------|-----------------|----------------|
| 订单补助     | 无         | 0.1        | 0.2             | 0.3            |
| 专属客服     | 工单服务   | 工单服务   | 工作日8小时在线 | 24小时优先响应 |
| 物流报价补贴 | 无         | 无         | 10%补贴         | 20%补贴        |

# 四、代理商及团长分润策略

继承公司已有方案


