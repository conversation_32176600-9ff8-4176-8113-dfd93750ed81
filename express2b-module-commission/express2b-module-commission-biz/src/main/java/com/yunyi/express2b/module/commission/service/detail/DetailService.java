package com.yunyi.express2b.module.commission.service.detail;

import java.util.*;
import jakarta.validation.*;
import com.yunyi.express2b.module.commission.controller.admin.detail.vo.*;
import com.yunyi.express2b.module.commission.dal.dataobject.detail.DetailDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;

/**
 * 分润明细 Service 接口
 *
 * <AUTHOR>
 */
public interface DetailService {

    /**
     * 创建分润明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDetail(@Valid DetailSaveReqVO createReqVO);

    /**
     * 更新分润明细
     *
     * @param updateReqVO 更新信息
     */
    void updateDetail(@Valid DetailSaveReqVO updateReqVO);

    /**
     * 删除分润明细
     *
     * @param id 编号
     */
    void deleteDetail(Long id);

    /**
     * 获得分润明细
     *
     * @param id 编号
     * @return 分润明细
     */
    DetailDO getDetail(Long id);

    /**
     * 获得分润明细分页
     *
     * @param pageReqVO 分页查询
     * @return 分润明细分页
     */
    PageResult<DetailDO> getDetailPage(DetailPageReqVO pageReqVO);


    /**
     * 我可以查询分润明细，按时间、来源、下级等维度统计
     * @param pageReqVO
     * @return
     */
    PageResult<DetailDO> getcommissiondetails(DetailPageReqVO pageReqVO);
}