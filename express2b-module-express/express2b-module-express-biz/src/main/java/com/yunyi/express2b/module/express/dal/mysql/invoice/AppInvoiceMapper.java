package com.yunyi.express2b.module.express.dal.mysql.invoice;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.UnInvoicedOrderDTO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AppInvoiceMapper extends BaseMapperX<OrderDO> {

    default PageResult<OrderDO> getUnInvoicedOrderPage(UnInvoicedOrderDTO unInvoicedOrderDTO) {
        return selectPage(unInvoicedOrderDTO, new LambdaQueryWrapperX<OrderDO>()
                .eq(OrderDO::getPersonalId, unInvoicedOrderDTO.getPersonalId())
                .and(wrapper->wrapper
                        .eq(OrderDO::getInvoiceStatus, "NOT_INVOICED")
                        .or()
                        .eq(OrderDO::getInvoiceStatus, "INVOICE_FAILED"))
                .eq(OrderDO::getStatus, "COMPLETED"));
    }
}
