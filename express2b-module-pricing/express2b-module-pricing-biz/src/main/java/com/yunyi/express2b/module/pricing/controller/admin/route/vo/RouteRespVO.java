package com.yunyi.express2b.module.pricing.controller.admin.route.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 快递全国运价基础 Response VO")
@Data
@ExcelIgnoreUnannotated
public class RouteRespVO {

    @Schema(description = "运费ID (主键)", requiredMode = Schema.RequiredMode.REQUIRED, example = "9371")
    @ExcelProperty("运费ID (主键)")
    private Long id;

    @Schema(description = "品牌标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("品牌标识")
    private String brandKey;

    @Schema(description = "线路标识（格式：品牌-始发地-目的地）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("线路标识（格式：品牌-始发地-目的地）")
    private String lineKey;

    @Schema(description = "始发地名称")
    @ExcelProperty("始发地名称")
    private String sendProvince;

    @Schema(description = "目的地名称")
    @ExcelProperty("目的地名称")
    private String receiveProvince;

    @Schema(description = "始发地adcode", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("始发地adcode")
    private String sendProvinceCode;

    @Schema(description = "目的地adcode", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("目的地adcode")
    private String receiveProvinceCode;

    @Schema(description = "产品名称")
    @ExcelProperty("产品名称")
    private String productCode;

    @Schema(description = "首重重量(g)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("首重重量(g)")
    private Integer firstWeight;

    @Schema(description = "续重重量(g)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("续重重量(g)")
    private Integer additionalWeight;

    @Schema(description = "供应首重成本价（分）", requiredMode = Schema.RequiredMode.REQUIRED, example = "22335")
    @ExcelProperty("供应首重成本价（分）")
    private Integer costFirstPrice;

    @Schema(description = "供应续重成本价（分）", requiredMode = Schema.RequiredMode.REQUIRED, example = "28776")
    @ExcelProperty("供应续重成本价（分）")
    private Integer costOverPrice;

    @Schema(description = "市场首重价格（分）", example = "24127")
    @ExcelProperty("市场首重价格（分）")
    private Integer originalFirstPrice;

    @Schema(description = "市场续重价格（分）", example = "4647")
    @ExcelProperty("市场续重价格（分）")
    private Integer originalOverPrice;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}