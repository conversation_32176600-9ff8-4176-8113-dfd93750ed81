package com.yunyi.express2b.module.express.controller.admin.locations.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 全国行政区信息管理 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 全国行政区信息管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LocationsRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "11789")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "父级ID，表示当前记录的父级记录的ID", example = "4024")
    @ExcelProperty("父级ID，表示当前记录的父级记录的ID")
    private Integer parentId;

    @Schema(description = "父级代码，表示当前记录的父级记录的代码")
    @ExcelProperty("父级代码，表示当前记录的父级记录的代码")
    private String parentCode;

    @Schema(description = "级别，表示地址的层级（如省、市、区、街道等）")
    @ExcelProperty("级别，表示地址的层级（如省、市、区、街道等）")
    private String level;

    @Schema(description = "城市代码，表示所在城市的代码")
    @ExcelProperty("城市代码，表示所在城市的代码")
    private String citycode;

    @Schema(description = "行政区划代码，表示所在行政区划的代码")
    @ExcelProperty("行政区划代码，表示所在行政区划的代码")
    private String adcode;

    @Schema(description = "名称，表示地址的具体名称（如省名、市名、区名、街道名等）", example = "张三")
    @ExcelProperty("名称，表示地址的具体名称（如省名、市名、区名、街道名等）")
    private String name;

    @Schema(description = "GPS纬度，表示地址的纬度坐标")
    @ExcelProperty("GPS纬度，表示地址的纬度坐标")
    private BigDecimal gpsLat;

    @Schema(description = "GPS经度，表示地址的经度坐标")
    @ExcelProperty("GPS经度，表示地址的经度坐标")
    private BigDecimal gpsLon;

    @Schema(description = "最后更新时间，表示记录的最后更新时间")
    @ExcelProperty("最后更新时间，表示记录的最后更新时间")
    private LocalDate lastTime;

    @Schema(description = "状态，表示记录的状态（通常用于标识记录是否有效等）", example = "2")
    @ExcelProperty("状态，表示记录的状态（通常用于标识记录是否有效等）")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}