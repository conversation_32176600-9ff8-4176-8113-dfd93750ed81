package com.yunyi.express2b.module.commission.controller.admin.summary.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 分润汇总 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SummaryRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "16728")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "代理商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5384")
    @ExcelProperty("代理商ID")
    private Long targetId;

    @Schema(description = "汇总日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("汇总日期")
    private LocalDate summaryDate;

    @Schema(description = "汇总金额（单位：分）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("汇总金额（单位：分）")
    private Integer totalAmount;

    @Schema(description = "明细数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "26394")
    @ExcelProperty("明细数量")
    private Integer detailCount;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}