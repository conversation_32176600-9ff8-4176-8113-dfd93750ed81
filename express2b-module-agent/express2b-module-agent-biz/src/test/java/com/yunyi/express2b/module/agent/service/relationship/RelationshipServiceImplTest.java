package com.yunyi.express2b.module.agent.service.relationship;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.module.agent.controller.admin.relationship.vo.RelationshipPageReqVO;
import com.yunyi.express2b.module.agent.controller.admin.relationship.vo.RelationshipSaveReqVO;
import com.yunyi.express2b.module.agent.dal.dataobject.relationship.RelationshipDO;
import com.yunyi.express2b.module.agent.dal.mysql.relationship.RelationshipMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import static com.yunyi.express2b.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertPojoEquals;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertServiceException;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomLongId;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomPojo;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.RELATIONSHIP_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link RelationshipServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(RelationshipServiceImpl.class)
public class RelationshipServiceImplTest extends BaseDbUnitTest {

    @Resource
    private RelationshipServiceImpl relationshipService;

    @Resource
    private RelationshipMapper relationshipMapper;

    @Test
    public void testCreateRelationship_success() {
        // 准备参数
        RelationshipSaveReqVO createReqVO = randomPojo(RelationshipSaveReqVO.class).setId(null);

        // 调用
        Long relationshipId = relationshipService.createRelationship(createReqVO);
        // 断言
        assertNotNull(relationshipId);
        // 校验记录的属性是否正确
        RelationshipDO relationship = relationshipMapper.selectById(relationshipId);
        assertPojoEquals(createReqVO, relationship, "id");
    }

    @Test
    public void testUpdateRelationship_success() {
        // mock 数据
        RelationshipDO dbRelationship = randomPojo(RelationshipDO.class);
        relationshipMapper.insert(dbRelationship);// @Sql: 先插入出一条存在的数据
        // 准备参数
        RelationshipSaveReqVO updateReqVO = randomPojo(RelationshipSaveReqVO.class, o -> {
            o.setId(dbRelationship.getId()); // 设置更新的 ID
        });

        // 调用
        relationshipService.updateRelationship(updateReqVO);
        // 校验是否更新正确
        RelationshipDO relationship = relationshipMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, relationship);
    }

    @Test
    public void testUpdateRelationship_notExists() {
        // 准备参数
        RelationshipSaveReqVO updateReqVO = randomPojo(RelationshipSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> relationshipService.updateRelationship(updateReqVO), RELATIONSHIP_NOT_EXISTS);
    }

    @Test
    public void testDeleteRelationship_success() {
        // mock 数据
        RelationshipDO dbRelationship = randomPojo(RelationshipDO.class);
        relationshipMapper.insert(dbRelationship);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbRelationship.getId();

        // 调用
        relationshipService.deleteRelationship(id);
        // 校验数据不存在了
        assertNull(relationshipMapper.selectById(id));
    }

    @Test
    public void testDeleteRelationship_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> relationshipService.deleteRelationship(id), RELATIONSHIP_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetRelationshipPage() {
        // mock 数据
        RelationshipDO dbRelationship = randomPojo(RelationshipDO.class, o -> { // 等会查询到
            o.setAncestorId(null);
            o.setDescendantId(null);
            o.setDepth(null);
        });
        relationshipMapper.insert(dbRelationship);
        // 测试 ancestorId 不匹配
        relationshipMapper.insert(cloneIgnoreId(dbRelationship, o -> o.setAncestorId(null)));
        // 测试 descendantId 不匹配
        relationshipMapper.insert(cloneIgnoreId(dbRelationship, o -> o.setDescendantId(null)));
        // 测试 depth 不匹配
        relationshipMapper.insert(cloneIgnoreId(dbRelationship, o -> o.setDepth(null)));
        // 准备参数
        RelationshipPageReqVO reqVO = new RelationshipPageReqVO();
        reqVO.setAncestorId(null);
        reqVO.setDescendantId(null);
        reqVO.setDepth(null);

        // 调用
        PageResult<RelationshipDO> pageResult = relationshipService.getRelationshipPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbRelationship, pageResult.getList().get(0));
    }

}