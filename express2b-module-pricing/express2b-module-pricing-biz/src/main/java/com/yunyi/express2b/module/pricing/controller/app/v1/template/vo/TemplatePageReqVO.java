package com.yunyi.express2b.module.pricing.controller.app.v1.template.vo;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 定价模板分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TemplatePageReqVO extends PageParam {

    @Schema(description = "模板名称", example = "芋艿")
    private String templateName;

    @Schema(description = "加价类型 (1-比例加价，2-固定金额加价-默认)", example = "1")
    private Integer markupType;

    @Schema(description = "首重加价值 (固定金额，单位:分)")
    private Integer firstWeightMarkup;

    @Schema(description = "续重加价值 (固定金额，单位:分)")
    private Integer additionalWeightMarkup;

    @Schema(description = "平台零售首重价 (单位:分)", example = "17154")
    private Integer platformFirstPrice;

    @Schema(description = "平台零售续重价 (单位:分)", example = "17967")
    private Integer platformAdditionalPrice;

    @Schema(description = "状态 (0-禁用，1-启用)", example = "1")
    private Integer status;

    @Schema(description = "描述", example = "随便")
    private String description;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}