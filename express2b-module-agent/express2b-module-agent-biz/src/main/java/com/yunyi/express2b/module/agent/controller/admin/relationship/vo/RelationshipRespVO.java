package com.yunyi.express2b.module.agent.controller.admin.relationship.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import com.alibaba.excel.annotation.*;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 9:53
 */
@Schema(description = "管理后台 - 代理商团队关系 Response VO")
@Data
@ExcelIgnoreUnannotated
public class RelationshipRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10635")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "上级代理商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25381")
    @ExcelProperty("上级代理商ID")
    private Long ancestorId;

    @Schema(description = "下级代理商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8554")
    @ExcelProperty("下级代理商ID")
    private Long descendantId;

    @Schema(description = "层级深度", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("层级深度")
    private Integer depth;

}