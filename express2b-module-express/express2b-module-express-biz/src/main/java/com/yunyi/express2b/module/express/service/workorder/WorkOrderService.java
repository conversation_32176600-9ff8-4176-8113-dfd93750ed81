package com.yunyi.express2b.module.express.service.workorder;

import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.module.express.controller.admin.workorder.vo.BaseWorkOrderResVO;
import com.yunyi.express2b.module.express.controller.admin.workorder.vo.CallbackReqVO;
import com.yunyi.express2b.module.express.controller.admin.workorder.vo.WorkOrderUpdateReqVO;
import com.yunyi.express2b.module.express.controller.app.v1.workorder.vo.*;
import com.yunyi.express2b.module.express.dal.dataobject.workorder.WorkOrderDO;
import com.yunyi.express2b.module.express.enums.InitiatorTypeEnum;
import jakarta.validation.*;
import com.yunyi.express2b.framework.common.pojo.PageResult;

/**
 * 工单 Service 接口
 *
 * <AUTHOR>
 */
public interface WorkOrderService {

    /**
     * 【用户】创建工单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createWorkOrder(InitiatorTypeEnum initiatorTypeEnum, @Valid WorkOrderCreateReqVO createReqVO);

    /**
     * 用户工单留言
     *
     */
    CommonResult<Boolean> sendWorkOrderMessage(@Valid WorkOrderMessageReqVO messageReqVO);

    /**
     * 删除工单
     * @param id
     * @return
     */
    CommonResult<Boolean> deleteWorkOrder(Long id);

    /**
     * 三方平台回调方法
     * @param createReqVO
     * @return
     */
    Boolean callback(@Valid CallbackReqVO createReqVO);

    /**
     * 获得工单列表分页
     *
     * @param pageReqVO 分页查询
     * @return 工单分页
     */
    PageResult<WorkOrderDO> getWorkOrderPage(Boolean isUser, WorkOrderPageReqVO pageReqVO);


    /**
     * 根据id查询工单
     * @param id
     * @return
     */
    WorkOrderDO  getWorkOrderById(Boolean isUser,Long id);

    /**
     * 查询附件信息
     * @param baseWorkOrderVO
     * @return
     * @param <T>
     */
   <T extends BaseWorkOrderResVO> T getWorkOrderFile(T baseWorkOrderVO);


    /**
     * 更新工单内容
     * @param updateReqVO
     * @return
     */
   int updateWorkOrder(WorkOrderUpdateReqVO updateReqVO);

}