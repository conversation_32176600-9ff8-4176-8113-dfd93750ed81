package com.yunyi.express2b.module.express.service.products;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.express.controller.admin.products.vo.ProductsPageReqVO;
import com.yunyi.express2b.module.express.controller.admin.products.vo.ProductsSaveReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.products.ProductsDO;
import com.yunyi.express2b.module.express.dal.mysql.products.ProductsMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.PRODUCTS_NOT_EXISTS;

/**
 * 物品信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ProductsServiceImpl implements ProductsService {

    @Resource
    private ProductsMapper productsMapper;

    /**
     * 创建物品信息
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Long createProducts(ProductsSaveReqVO createReqVO) {
        // 插入
        ProductsDO products = BeanUtils.toBean(createReqVO, ProductsDO.class);
        productsMapper.insert(products);
        // 返回
        return products.getId();
    }

    /**
     * 更新物品信息
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateProducts(ProductsSaveReqVO updateReqVO) {
        // 校验存在
        validateProductsExists(updateReqVO.getId());
        // 更新
        ProductsDO updateObj = BeanUtils.toBean(updateReqVO, ProductsDO.class);
        productsMapper.updateById(updateObj);
    }

    /**
     * 删除物品信息
     * @param id 编号
     */
    @Override
    public void deleteProducts(Long id) {
        // 校验存在
        validateProductsExists(id);
        // 删除
        productsMapper.deleteById(id);
    }

    /**
     * 校验物品信息是否存在
     * @param id
     */
    private void validateProductsExists(Long id) {
        if (productsMapper.selectById(id) == null) {
            throw exception(PRODUCTS_NOT_EXISTS);
        }
    }

    /**
     * 获得物品信息
     * @param id 编号
     * @return
     */
    @Override
    public ProductsDO getProducts(Long id) {
        return productsMapper.selectById(id);
    }

    /**
     * 获得物品信息分页
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<ProductsDO> getProductsPage(ProductsPageReqVO pageReqVO) {
        return productsMapper.selectPage(pageReqVO);
    }

    /**
     * 获取物品信息
     * @param id
     * @return
     */
    @Override
    public List<ProductsDO> selectProductsInfo(Long id) {
        List<ProductsDO> productsDOS = productsMapper.selectProductList(id);
        return productsDOS;
    }


}