<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunyi.express2b.module.crm.dal.mysql.cardlimited.CardlimitedMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->



    <select id="selectRedeemCode" resultType="java.lang.Boolean">
        SELECT EXISTS(SELECT 1 FROM express2b_cardlimited_redeem WHERE redeem_code = #{redeemCode} AND deleted = 0)

    </select>

    <select id="selectUserCard" resultType="java.lang.String">
        SELECT description FROM express2b_cardlimited
        WHERE id = #{cardlimitedId}
          AND deleted = 0;
    </select>
    <select id="selectCardShopPage"
            resultType="com.yunyi.express2b.module.crm.controller.admin.cardlimited.vo.CardShopVo">
        SELECT
            c.id,
            c.cardlimited_name,
            c.price,
            c.number,
            c.description,
            GROUP_CONCAT(b.brand_name) AS brand_names,
            c.validity_period AS cardEndTime
        FROM
            express2b_cardlimited c
               left join
            express2b_cardlimited_brand a ON c.id = a.cardlimited_id
               left join
            express2b_brand b ON a.brand_id = b.id
        GROUP BY
            c.id, c.cardlimited_name, c.price, c.number, c.description,c.validity_period
        ORDER BY
            c.id;
    </select>


    <select id="selectUserCardAll1" resultType="com.yunyi.express2b.module.crm.controller.app.v1.carflimited.vo.CardUserVO">
        SELECT
        a.cardlimited_id,
        a.card_name,
        a.remaining,
        a.card_start_date,
        a.card_end_date,
        c.description,
        c.validity_period AS cardEndTime,
        a.card_number,
        GROUP_CONCAT(b.brand_name) AS brand_names
        FROM
        express2b_card_user a
        left join
        express2b_cardlimited c ON a.cardlimited_id = c.id
        left join
        express2b_cardlimited_brand d ON c.id = d.cardlimited_id
        left join
        express2b_brand b ON d.brand_id = b.id
        WHERE
        1 = 1
        <if test="personalId != null">
            AND a.personal_id = #{personalId}
        </if>
        <if test="status != null">
            AND a.status = #{status}
        </if>
        <if test="time != null">
            AND a.card_end_date &gt;= #{time}
        </if>
        GROUP BY
        a.cardlimited_id, a.card_name, a.remaining, a.card_end_date, c.description, a.card_number,c.validity_period,a.card_start_date
        ORDER BY
        a.cardlimited_id;
    </select>


    <select id="selectUserCardAll" resultType="com.yunyi.express2b.module.crm.controller.app.v1.carflimited.vo.CardUserVO">
        SELECT
            a.cardlimited_id,
            a.card_name,
            a.remaining,
            a.card_start_date,
            a.card_end_date,
            c.description,
            c.validity_period AS cardEndTime,
            a.card_number,
            GROUP_CONCAT(b.brand_name) AS brand_names
        FROM
            express2b_card_user a
               left join
            express2b_cardlimited c ON a.cardlimited_id = c.id
               left join
            express2b_cardlimited_brand d ON c.id = d.cardlimited_id
               left join
            express2b_brand b ON d.brand_id = b.id
        WHERE
        1 = 1
        <if test="personalId != null">
            AND a.personal_id = #{personalId}
        </if>
        <if test="status != null">
            AND a.status = #{status}
        </if>
        <if test="time != null">
            AND a.card_end_date &lt;= #{time}
        </if>
        GROUP BY
            a.cardlimited_id, a.card_name, a.remaining, a.card_end_date, c.description, a.card_number,c.validity_period,a.card_start_date
        ORDER BY
            a.cardlimited_id;
    </select>
    <select id="getCardlimitedOne"
            resultType="com.yunyi.express2b.module.crm.controller.app.v1.carflimited.vo.CardLimitedVO">
        SELECT
            c.id,
            c.cardlimited_name,
            c.validity_period,
            c.price,
            c.number,
            c.description,
            GROUP_CONCAT(b.brand_name) AS brand_names
        FROM
            express2b_cardlimited c
                JOIN
            express2b_cardlimited_brand a ON c.id = a.cardlimited_id AND c.id=#{id}
                JOIN
            express2b_brand b ON a.brand_id = b.id
        GROUP BY
            c.id, c.cardlimited_name, c.price, c.number, c.description
        ORDER BY
            c.id;

    </select>
    <select id="getBuyCardPage" resultType="com.yunyi.express2b.module.crm.controller.admin.cardlimited.vo.CardShopVo">
        SELECT
            c.id,
            c.cardlimited_name,
            c.price,
            c.number,
            c.description,
            GROUP_CONCAT(b.brand_name) AS brand_names
        FROM
            express2b_cardlimited c
              left join express2b_cardlimited_brand a ON c.id = a.cardlimited_id
              left join express2b_brand b ON a.brand_id = b.id
        GROUP BY
            c.id, c.cardlimited_name, c.price, c.number, c.description
        HAVING
            c.id =  #{id};
    </select>

</mapper>