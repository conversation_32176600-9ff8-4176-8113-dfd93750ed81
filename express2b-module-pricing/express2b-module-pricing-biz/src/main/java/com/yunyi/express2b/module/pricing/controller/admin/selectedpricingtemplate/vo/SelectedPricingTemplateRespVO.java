package com.yunyi.express2b.module.pricing.controller.admin.selectedpricingtemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 代理商为下级选择的价格模板记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SelectedPricingTemplateRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "21845")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "操作选择的上级代理商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18109")
    @ExcelProperty("操作选择的上级代理商ID")
    private Long superiorAgentId;

    @Schema(description = "被指定模板的下级代理商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10787")
    @ExcelProperty("被指定模板的下级代理商ID")
    private Long downlineAgentId;

    @Schema(description = "选择的价格模板ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9485")
    @ExcelProperty("选择的价格模板ID")
    private Long pricingTemplateId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}