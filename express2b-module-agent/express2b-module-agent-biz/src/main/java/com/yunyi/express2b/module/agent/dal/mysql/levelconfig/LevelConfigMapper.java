package com.yunyi.express2b.module.agent.dal.mysql.levelconfig;

import java.util.*;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.AgentProfileDO;
import com.yunyi.express2b.module.agent.dal.dataobject.levelconfig.LevelConfigDO;
import org.apache.ibatis.annotations.Mapper;
import com.yunyi.express2b.module.agent.controller.admin.levelconfig.vo.*;

/**
 * 代理商等级配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface LevelConfigMapper extends BaseMapperX<LevelConfigDO> {

    default PageResult<LevelConfigDO> selectPage(LevelConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<LevelConfigDO>()
                .likeIfPresent(LevelConfigDO::getLevelName, reqVO.getLevelName())
                .eqIfPresent(LevelConfigDO::getDefaultPlatformPricingTemplateId, reqVO.getDefaultPlatformPricingTemplateId())
                .eqIfPresent(LevelConfigDO::getUpgradePersonalDailyOrders, reqVO.getUpgradePersonalDailyOrders())
                .eqIfPresent(LevelConfigDO::getUpgradeTeamDailyOrders, reqVO.getUpgradeTeamDailyOrders())
                .eqIfPresent(LevelConfigDO::getUpgradeObservationDays, reqVO.getUpgradeObservationDays())
                .betweenIfPresent(LevelConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(LevelConfigDO::getId));
    }

    /**
     * 通过代理商id查询代理商等级名称
     * @param agentId
     * @return
     */
    default LevelConfigDO selectAgentAndLevelNameById(Long agentId){
        return selectOne(new MPJLambdaWrapperX<LevelConfigDO>()
                .select(LevelConfigDO::getLevelName)
                .leftJoin(AgentProfileDO.class,AgentProfileDO::getLevelId,LevelConfigDO::getId)
                .eq(AgentProfileDO::getId,agentId)
        );

    }

}