yunyi:
  # 超时时间
  timeout: 10
  # 统一登录平台的Appid
  appidForLogin: yy20250328xiaobyl1
  # 统一运力平台Appid
  appidForLogistics: 20250501
  # 统一推广平台的Appid
  appidForMarketing: yy20250328xiaobyl1
  # appkey -- 2025-05-30作废
  appkey: faad0e0a26a9875407c0cc446dde5171

  loginSecret: faad0e0a26a9875407c0cc446dde5171

  logisticsSecret: 368247671A9C3AE963699B046B4B825E

  marketingSecret: faad0e0a26a9875407c0cc446dde5171
  # clientId
  clientId: 16
  # API版本
  version: v1.0
  #推广平台默认用户id（2025.06.09）
  ssoUserId: 14686742
  #默认首页path路径（2025.06.11）
  path: pages/init
  # 登录平台基础地址
  baseUrl: http://192.168.10.89:8181/api/v1.0
  # 代理平台基础地址
  baseMarketingUrl: http://192.168.10.89:8183/agent/v1.0
#  # 运力平台基础地址-ucm
#  baseLogisticsUcmUrl: http://192.168.10.91:8282/ucm/v1.0
#  # 运力平台基础地址-ucs
#  baseLogisticsUcsUrl: http://192.168.10.91:8282/ucm/v1.0
#  # 运力平台基础地址-ca
#  baseLogisticsCaUrl: http://192.168.10.91:8282/ucm/v1.0

  # 运力平台基础地址-ucm
  baseLogisticsUcmUrl: http://192.168.10.91:8283/ucm/v1.0
  # 运力平台基础地址-ucs
  baseLogisticsUcsUrl: http://192.168.10.91:8283/ucm/v1.0
  # 运力平台基础地址-ca
  baseLogisticsCaUrl: http://192.168.10.91:8283/ucm/v1.0
  # 退款接口
  refundOrder: /pay/refundOrder
  # 统一下单接口
  unifiedOrder: /pay/unifiedorder
  # 用户登录接口
  userLogin: /user/login
  # 发送消息
  messageSend: /message/send
  # 模板列表接口路径
  templateList: /template/templatesPage
  # 上传图片
  imageUpload: /image/upload
  # 获取客服
  customerServices: /cs/customerServices
  # 运价查询
  expressPrice: /express/queryPrice
  # 运单轨迹查询
  orderTrack: /express/queryOrderTrack
  # 快递公司列表查询
  expressCompanyList: /express/list
  # 快递下单
  createOrder: /express/createOrder
  # 订单查询
  queryOrder: /express/queryOrder
  # 取消订单
  cancelOrder: /express/cancelOrder
  # 地址解析
  addressList: /address/recognition
#=====# 回调地址的配置
  # 支付相关地址的回调
  pay:
    base-service-url: https://ydl.le-kc.com
    pay-notify-url: ${express2b.pay.base-service-url}/111admin-api/express/paycallback/notifyurl # 支付渠道的【支付】回调地址
    refund-notify-url: ${express2b.pay.base-service-url}/222admin-api/express/refundcallback/notifyurl # 支付渠道的【退款】回调地址
    transfer-notify-url: ${express2b.pay.base-service-url}/333admin-api/pay/notify/transfer # 支付渠道的【转账】回调地址

  # 订单相关地址的回调
  order:
    base-service-url: https://ydl.le-kc.com
    create-order-notify-url: ${express2b.order.base-service-url}/444admin-api/express/order/order-callback # 运力【下单】回调地址

  #工单相关地址的回调
  work-order:
    callback: http://*************:48080/admin-api/express/work-order/callback