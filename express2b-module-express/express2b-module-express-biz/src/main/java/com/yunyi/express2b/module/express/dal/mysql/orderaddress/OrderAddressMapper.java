package com.yunyi.express2b.module.express.dal.mysql.orderaddress;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.yunyi.express2b.module.express.dal.dataobject.brand.BrandDO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.orderaddress.OrderAddressDO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * order_address表对应mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/7 上午9:44
 */
@Mapper
public interface OrderAddressMapper extends BaseMapperX<OrderAddressDO> {
    /**
     * 查询订单地址品牌信息
     * @param id 订单Id
     */
   // public Map<String, Object> selectOrderAddressInfo(Long id);
    @MapKey("selectOrderAddressInfo")
    public Map<String, Object> selectOrderAddressInfo(Long id);
}
