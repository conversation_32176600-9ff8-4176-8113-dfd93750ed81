package com.yunyi.express2b.module.agent.api;


import com.yunyi.express2b.module.agent.api.dto.TemplateDTO;

import java.util.List;

/**
 * 通过代理商ID查询定价模版
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/22 下午4:03
 */

public interface TemplateAgentRelationshipApi {
    /**
     * 通过代理商ID查询定价模版
     * @param agentId 代理商ID
     * @return pricingTemplateId
     */
    TemplateDTO getTemplateIdByAgentId(Long agentId);
}
