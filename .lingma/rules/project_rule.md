---
description: 
globs: 
alwaysApply: true
---

You are an expert in Java programming, Spring Boot, Spring Framework, Maven, JUnit, and related Java technologies.

Code Style and Structure
- Write clean, efficient, and well-documented Java code with accurate Spring Boot examples.
- Use Spring Boot best practices and conventions throughout your code.
- Implement RESTful API design patterns when creating web services.
- Use descriptive method and variable names following camelCase convention.
- Structure Spring Boot applications: controllers, services, repositories, models, configurations.

Spring Boot Specifics
- Use Spring Boot starters for quick project setup and dependency management.
- Implement proper use of annotations (e.g., @SpringBootApplication, @RestController, @Service).
- Utilize Spring Boot's auto-configuration features effectively.
- Implement proper exception handling using @ControllerAdvice and @ExceptionHandler.

Naming Conventions
- Use PascalCase for class names (e.g., UserController, OrderService).
- Use camelCase for method and variable names (e.g., findUserById, isOrderValid).
- Use ALL_CAPS for constants (e.g., MAX_RETRY_ATTEMPTS, DEFAULT_PAGE_SIZE).

Java and Spring Boot Usage
- Use Java 17 or later features when applicable (e.g., records, sealed classes, pattern matching).
- Leverage Spring Boot 3.x features and best practices.
- Use Spring Data JPA for database operations when applicable.
- Implement proper validation using Bean Validation (e.g., @Valid, custom validators).

Configuration and Properties
- Use application.properties or application.yml for configuration.
- Implement environment-specific configurations using Spring Profiles.
- Use @ConfigurationProperties for type-safe configuration properties.

Dependency Injection and IoC
- Use constructor injection over field injection for better testability.
- Leverage Spring's IoC container for managing bean lifecycles.

Testing
- Write unit tests using JUnit 5 and Spring Boot Test.
- Use MockMvc for testing web layers.
- Implement integration tests using @SpringBootTest.
- Use @DataJpaTest for repository layer tests.

Performance and Scalability
- Implement caching strategies using Spring Cache abstraction.
- Use async processing with @Async for non-blocking operations.
- Implement proper database indexing and query optimization.

Security
- Implement Spring Security for authentication and authorization.
- Use proper password encoding (e.g., BCrypt).
- Implement CORS configuration when necessary.

Logging and Monitoring
- Use SLF4J with Logback for logging.
- Implement proper log levels (ERROR, WARN, INFO, DEBUG).
- Use Spring Boot Actuator for application monitoring and metrics.

API Documentation
- Use Springdoc OpenAPI (formerly Swagger) for API documentation.

Data Access and ORM
- Use Spring Data JPA for database operations.
- Implement proper entity relationships and cascading.
- Use database migrations with tools like Flyway or Liquibase.

Build and Deployment
- Use Maven for dependency management and build processes.
- Implement proper profiles for different environments (dev, test, prod).
- Use Docker for containerization if applicable.

Follow best practices for:
- RESTful API design (proper use of HTTP methods, status codes, etc.).
- Microservices architecture (if applicable).
- Asynchronous processing using Spring's @Async or reactive programming with Spring WebFlux.

Adhere to SOLID principles and maintain high cohesion and low coupling in your Spring Boot application design.

---
description: 
globs: 
alwaysApply: true
---
# API设计规范

## Controller层设计

### 接口分类

- **前端接口**: 面向终端客户的接口，用于小程序等前端应用访问
  - **URL前缀**: `app-api` (由框架统一控制，Controller中无需声明)
  - **包路径**: `com.yunyi.express2b.module.{module-name}.controller.app`
  - **版本控制**: 在app目录下使用子目录表示版本，如`v1`、`v2`等，实现接口版本管理
  - **功能组织**: 按业务领域划分子包，保持与后端接口相似的结构
  - **前端接口VO对象**：
    - 存放位置：com.yunyi.express2b.module.{module-name}.controller.app.vo
    - 版本控制：可按版本进一步划分子目录，如app.v1.vo、app.v2.vo等
    - 适用场景：面向小程序等终端客户的接口返回对象
  
- **后端接口**: 面向管理Web系统的接口
  - **URL前缀**: `admin-api` (由框架统一控制，Controller中无需声明)
  - **包路径**: `com.yunyi.express2b.module.{module-name}.controller.admin`
  - **功能组织**: 按业务领域划分子包，保持与前端接口相似的结构
  - **后端接口VO对象**：
    - **存放位置**：com.yunyi.express2b.module.{module-name}.controller.admin.vo
    - **适用场景**：面向管理Web系统的接口返回对象

- **命名规范**
  - **请求参数VO**: 功能+ReqVO，如UserCreateReqVO、OrderQueryReqVO
  - **响应结果VO**: 功能+RespVO，如UserProfileRespVO、OrderDetailRespVO
  - **分页请求对象**：实体+PageReqVO，如UserPageReqVO

### 接口设计原则

- **RESTful风格**: 严格按照RESTful API设计规范实现
  - GET: 查询资源
  - POST: 创建资源
  - PUT: 更新资源（全量更新）
  - PATCH: 部分更新资源
  - DELETE: 删除资源
  
- **URL命名规范**:
  - 使用名词复数形式表示资源集合: `/users`, `/orders`
  - 使用名词单数+ID表示具体资源: `/user/{id}`, `/order/{id}`
  - 子资源使用子路径: `/user/{id}/addresses`
  - 查询参数使用query string: `/users?status=active`
  - **URL字符要求**: URL地址不允许出现大写字符，只能使用小写字符、横线和数字

- **统一响应格式**: 所有接口返回`CommonResult<T>`响应结构

- **版本兼容性**: 不同版本接口保持向后兼容性
  - 添加新字段时不应影响旧版本客户端
  - 修改字段含义时使用新的字段名称
  - 重大变更通过版本升级处理

- **安全控制**: 根据接口类型实施不同的安全认证机制
  - 前端接口: 用户Token认证
  - 后端接口: 管理员Token认证和权限控制
  - 敏感操作需要额外的权限验证

- **幂等性设计**: 关键操作实现接口幂等性
  - GET、PUT、DELETE方法本身应是幂等的
  - POST方法通过唯一标识符实现幂等性

### 接口文档

- 使用Swagger (Knife4j) 自动生成API文档
- 对每个接口添加详细的注释说明
- 包含请求参数说明、响应示例、错误码说明等
- 文档示例：

```java
@ApiOperation(value = "创建用户", notes = "创建新用户并返回用户ID")
@ApiImplicitParams({
    @ApiImplicitParam(name = "createUserVO", value = "用户创建信息", required = true, dataType = "CreateUserVO")
})
@ApiResponses({
    @ApiResponse(code = 200, message = "操作成功", response = CommonResult.class),
    @ApiResponse(code = 400, message = "参数校验失败"),
    @ApiResponse(code = 500, message = "系统内部错误")
})
@PostMapping("/users")
public CommonResult<Long> createUser(@RequestBody @Valid CreateUserVO createUserVO) {
    // 方法实现
}
```

## 统一返回对象

系统使用 `CommonResult<T>` 类作为所有接口的统一返回结构，该类位于 `com.yunyi.express2b.framework.common.pojo` 包中。

### CommonResult 主要属性

* `code`: Integer类型，表示响应状态码。成功时为预定义的成功代码，失败时为具体的错误代码。
* `data`: T类型，泛型数据字段，仅在请求成功时存在。
* `msg`: String类型，描述错误信息，成功时不包含此字段。

### 使用规范

* **成功响应**: 使用 `CommonResult.success(T data)` 方法返回成功结果
* **错误响应**: 使用多种重载的 `CommonResult.error(...)` 方法返回错误结果
* **异常处理**: Controller层应统一捕获异常并通过CommonResult返回标准化错误

## 错误码管理

### 错误码命名规范

- 错误码格式：A-BB-CCC，其中：
  - A: 分类（1-系统错误，2-业务错误）
  - BB: 模块编号（00-通用，01-用户，02-订单等）
  - CCC: 具体错误编号

### 两级错误码体系

- **全局错误码**: 所有模块共享的错误码，定义在 `com.yunyi.express2b.framework.common.exception.GlobalErrorCodeConstants`
- **模块错误码**: 特定模块的错误码，定义在 `com.yunyi.express2b.module.{module-name}.api.enums.{ModuleName}ErrorCodeConstants`

### 错误码使用原则

- 在抛出业务异常时，使用错误码枚举，避免直接使用错误码数字值
- 模块内部错误使用模块错误码，跨模块错误使用全局错误码
- 错误码定义必须包含错误码、错误信息与错误详细描述
- 错误信息应当简洁明了，便于前端显示

## 模块间接口设计

- 模块间接口定义在`api`子模块中
- 方法命名应清晰表达功能
- 参数和返回值应使用DTO而非DO
- 明确异常处理策略，区分业务异常和系统异常

## 接口契约稳定性

- API接口一旦发布，应保持稳定
- 接口变更应通过版本管理
- 避免频繁修改已发布的接口参数和返回值结构
- 弃用旧接口时应提供充分的时间和明确的迁移路径




---
description: 
globs: 
alwaysApply: true
---
# 数据对象使用规范

## 数据对象类型

系统中严格遵循三种数据对象的使用规范：

### 1. 领域对象 (Domain Object, DO)

- **定义**: 与数据库表结构一一对应的Java对象
- **命名规则**: `实体名+DO`，如 `UserDO`，驼峰命名法。
- **使用范围**: 仅限于`biz`模块的`dal`层和`service`层内部
- **禁止事项**: 严禁跨模块传递，严禁在Controller层及以上使用
- **举例**: `UserDO`, `OrderDO`, `ProductDO`

### 2. 数据传输对象 (Data Transfer Object, DTO)

- **定义**: 模块间通信的数据载体
- **命名规则**: `功能+DTO`，如 `UserInfoDTO`
- **使用范围**: 用于`api`子模块定义的接口参数和返回值，以及跨服务调用
- **特点**: 关注数据的传输和解耦，不包含业务逻辑
- **举例**: `UserInfoDTO`, `OrderDetailDTO`, `ProductQueryDTO`

### 3. 视图对象 (View Object, VO)

- **定义**: 与前端或HTTP客户端交互的数据对象
- **命名规则**: 
  - 请求参数VO: `功能+ReqVO`，如 `UserCreateReqVO`、`OrderQueryReqVO`
  - 响应结果VO: `功能+RespVO`，如 `UserProfileRespVO`、`OrderDetailRespVO`
- **使用范围**: 用于`controller`层的方法参数和返回值
- **特点**: 面向展示层的数据封装，可能包含前端所需的特定格式或合并字段
- **举例**: `UserProfileRespVO`, `OrderListRespVO`, `ProductDetailRespVO`, `UserCreateReqVO`

### 分页查询VO对象规则

- **分页请求对象**:
  - 所有分页查询请求VO对象必须继承`com.yunyi.express2b.framework.common.pojo.PageParam`基类
  - 命名规则: `实体+PageReqVO`，如`UserPageReqVO`、`OrderPageReqVO`
  - 只需在子类中定义额外的查询条件字段，基础分页参数(pageNo、pageSize)由父类提供

- **分页响应对象**:
  - 分页查询统一使用`PageResult<T>`作为响应结构，其中T为具体业务对象的RespVO类型
  - 分页接口返回时必须使用`CommonResult<PageResult<XXXVO>>`的嵌套结构
  - Controller层需要使用适当的工具类将DO的分页结果转换为VO的分页结果

- **示例代码**:
  ```java
  // 分页请求VO示例
  @Schema(description = "用户分页查询请求")
  @Data
  public class UserPageReqVO extends PageParam {
      @Schema(description = "用户名关键字", example = "张")
      private String username;
      
      @Schema(description = "手机号码", example = "1399999999")
      private String mobile;
      
      @Schema(description = "创建时间区间起点")
      @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
      private LocalDateTime createTimeBegin;
      
      @Schema(description = "创建时间区间终点")
      @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
      private LocalDateTime createTimeEnd;
  }
  
  // Controller方法示例
  @GetMapping("/page")
  @Operation(summary = "获得用户分页列表")
  @PreAuthorize("@ss.hasPermission('system:user:query')")
  public CommonResult<PageResult<UserRespVO>> getUserPage(@Valid UserPageReqVO pageReqVO) {
      PageResult<UserDO> pageResult = userService.getUserPage(pageReqVO);
      return success(BeanUtils.toBean(pageResult, UserRespVO.class));
  }
  ```

## 对象转换

- 使用MapStruct在不同类型对象间进行转换
- 转换器应放在`com.yunyi.express2b.module.{module-name}.biz.convert`包下
- 严禁在没有转换的情况下跨层使用不同类型的对象
- 对象转换应遵循"失败快速"原则，对于无法转换的情况应当早期发现并处理

```java
// 示例转换器接口
@Mapper(componentModel = "spring")
public interface UserConvert {
    UserConvert INSTANCE = Mappers.getMapper(UserConvert.class);
    
    UserRespVO convert(UserDO user);
    
    UserDTO convertToDTO(UserDO user);
    
    UserDO convertToDO(CreateUserReqVO createUserReqVO);
}
```

## 响应格式

所有Controller层接口必须返回统一的`CommonResult<T>`响应结构：

```java
public class CommonResult<T> {
    private Integer code; // 状态码
    private String msg;   // 提示信息
    private T data;       // 泛型数据字段
    
    // 静态工厂方法
    public static <T> CommonResult<T> success(T data) { /*...*/ }
    public static <T> CommonResult<T> error(Integer code, String msg) { /*...*/ }
}
```

## 参数校验

- 在VO和DTO对象上使用JSR-303注解（@NotNull, @Size等）进行校验
- Controller层使用@Valid或@Validated触发校验
- 对于复杂校验逻辑，应在Service层实现，而不是依赖注解



---
description: 
globs: 
alwaysApply: true
---
# 数据库设计规范

## 表命名规范

- 表名使用小写字母，单词间使用下划线分隔
- 表名以业务领域前缀开头，如`user_`、`order_`、`product_`
- 表名应使用名词或名词短语，表达实体的含义
- 中间表命名应体现关联关系，如`user_role`

## 列命名规范

- 列名使用小写字母，单词间使用下划线分隔
- 主键统一命名为`id`，数据类型`bigint`，自动增长
- 创建者ID统一命名为`creator`，数据类型：`varchar(64)`，默认值`'`
- 创建时间统一命名为`create_time`，数据类型：`datetime`，默认值`CURRENT_TIMESTAMP`
- 更新者ID统一命名为`updater`，数据类型：`varchar(64)`，默认值`'`
- 更新时间统一命名为`update_time`，数据类型：`datetime`，默认值`CURRENT_TIMESTAMP`，更新时默认值为`CURRENT_TIMESTAMP`
- 逻辑删除标识统一命名为`deleted`，数据类型：`bit(1)`，默认值`b'0'`
- 租户编号统一命名为`tenant_id`，数据类型：`bigint`，默认值`0`
- 外键命名规则为`关联表名_id`，如`user_id`、`order_id`
- 避免mysql等数据库的保留字

## 数据类型选择

- 整数类型:
  - TINYINT: 适用于状态、标志等小范围数值
  - INT: 适用于一般计数、编号等中等范围数值 
  - BIGINT: 适用于ID、大范围数值
  
- 字符串类型:
  - VARCHAR: 变长字符串，指定最大长度
  - TEXT: 大文本字段，如描述、内容等
  
- 日期时间类型:
  - DATE: 只存储日期
  - DATETIME: 存储日期和时间
  - TIMESTAMP: 带时区的日期时间

- 浮点数类型:
  - DECIMAL: 固定精度的小数，用于金额等精确计算场景
  - DOUBLE: 双精度浮点数，用于非精确计算场景

## 表结构设计规范

- 每个表必须有主键，优先使用`BIGINT`类型的`id`作为主键
- 每个表必须包含`created_at`和`updated_at`字段，类型为`DATETIME`
- 不使用物理删除，统一使用`deleted`字段(BOOLEAN)标识逻辑删除
- 所有字段都应该有注释，说明其用途
- 枚举值等状态字段应在注释中说明每个状态值的含义
- 根据业务需求，合理设置字段的默认值
- 避免使用外键约束，通过应用程序保证数据完整性

## 索引设计规范

- 每个表的主键必须创建主键索引
- 经常用于查询条件的字段应创建索引
- 外键字段应创建索引
- 索引命名规则：`idx_字段名`，如`idx_user_id`
- 唯一索引命名规则：`uk_字段名`，如`uk_mobile`
- 联合索引命名规则：`idx_字段1_字段2`，如`idx_user_id_role_id`
- 控制索引数量，避免过多索引影响写入性能


## 数据访问策略

- 使用MyBatis Plus框架简化CRUD操作
- 复杂查询通过自定义Mapper XML实现
- 服务层负责事务管理和业务逻辑
- 对频繁访问的数据考虑使用Redis缓存
- 大数据量查询应使用分页查询
- 避免使用连表查询处理大数据量表之间的关联
- 对于需要高性能的查询，考虑冗余必要的字段减少关联

---
description: 
globs: 
alwaysApply: true
---
# 异常处理规范

## 异常分类

系统中的异常分为以下几类：

1. **系统异常**: 由系统底层抛出的非预期异常，如数据库连接异常、NullPointerException等
2. **业务异常**: 由业务逻辑抛出的可预期异常，通常是基于业务规则的校验结果
3. **参数校验异常**: 由参数校验框架(如JSR-303)抛出的校验失败异常

## 异常基类设计

```java
/**
 * 业务异常基类
 */
public class BusinessException extends RuntimeException {
    /**
     * 错误码
     */
    private final Integer code;
    
    /**
     * 错误消息
     */
    private final String message;
    
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    public BusinessException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
    }
    
    // Getters...
}
```

## 模块异常设计

- 每个业务模块应定义自己的异常类，继承自BusinessException
- 每个模块的异常类名应遵循`{模块名}Exception`的命名规则
- 模块异常应与模块错误码配合使用

```java
/**
 * 用户模块异常
 */
public class UserException extends BusinessException {
    public UserException(Integer code, String message) {
        super(code, message);
    }
    
    public UserException(UserErrorCode errorCode) {
        super(errorCode.getCode(), errorCode.getMessage());
    }
}
```

## 错误码规范

- 错误码应当具有语义化，便于排查问题
- 错误码格式：ABBCCC，其中：
  - A: 错误类型(1-系统错误, 2-业务错误)
  - BB: 模块编号(00-通用, 01-用户, 02-订单等)
  - CCC: 具体错误编号

```java
/**
 * 用户模块错误码
 */
public enum UserErrorCode implements ErrorCode {
    /**
     * 用户不存在
     */
    USER_NOT_FOUND(201001, "用户不存在"),
    
    /**
     * 用户已存在
     */
    USER_ALREADY_EXISTS(201002, "用户已存在"),
    
    /**
     * 密码错误
     */
    PASSWORD_INCORRECT(201003, "密码错误");
    
    private final Integer code;
    private final String message;
    
    UserErrorCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    
    @Override
    public Integer getCode() {
        return code;
    }
    
    @Override
    public String getMessage() {
        return message;
    }
}
```

## 全局异常处理

- 使用Spring MVC的`@ControllerAdvice`和`@ExceptionHandler`进行全局异常处理
- 针对不同类型的异常返回统一的响应格式
- 对系统异常隐藏具体错误信息，避免信息泄露

```java
/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public CommonResult<Void> handleBusinessException(BusinessException ex) {
        log.warn("[业务异常] {}", ex.getMessage());
        return CommonResult.error(ex.getCode(), ex.getMessage());
    }
    
    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public CommonResult<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        BindingResult bindingResult = ex.getBindingResult();
        FieldError fieldError = bindingResult.getFieldError();
        String message = fieldError != null 
            ? fieldError.getDefaultMessage() 
            : "参数校验错误";
        log.warn("[参数校验异常] {}", message);
        return CommonResult.error(100001, message);
    }
    
    /**
     * 处理系统异常
     */
    @ExceptionHandler(Exception.class)
    public CommonResult<Void> handleException(Exception ex) {
        log.error("[系统异常]", ex);
        return CommonResult.error(100000, "系统繁忙，请稍后再试");
    }
}
```

## 异常使用最佳实践

1. **在适当的层次抛出异常**:
   - 数据访问层(DAO)应当只捕获并转换底层异常为业务异常
   - 业务层(Service)应当进行业务验证，抛出具体业务异常
   - 控制层(Controller)通常不应直接捕获异常，而是由全局异常处理器处理

2. **提供有用的异常信息**:
   - 异常信息应当清晰表述问题
   - 包含关键参数便于定位问题
   - 避免在异常信息中包含敏感数据

3. **避免异常堆栈**:
   - 异常传递时保留原始异常作为cause
   - 避免捕获异常后丢弃堆栈信息
   - 使用`@Slf4j`注解和日志框架记录完整异常信息

4. **谨慎使用异常流程控制**:
   - 异常不应当用于正常流程控制
   - 对于可预期的情况，应当使用条件判断而非异常处理
   - 异常应当保留给真正的异常情况使用

---
description: 
globs: 
alwaysApply: true
---
# 模块结构规范

## 模块划分

平台采用模块化设计，每个业务模块 (`express2b-module-{module-name}`) 由以下子模块组成：

- **`express2b-module-{module-name}-api`**: 提供对外接口契约、DTO和枚举
- **`express2b-module-{module-name}-biz`**: 实现业务逻辑、控制器和数据访问逻辑

## 包结构规范

### API模块包结构

```
com.yunyi.express2b.module.{module-name}      // API根包
├── api.{moduleName}Api.java                  // 业务模块API接口定义
├── api.dto                                   // 数据传输对象
└── enums                                     // 枚举定义
```

### BIZ模块包结构

```
com.yunyi.express2b.module.{module-name}.biz
├── api         // API接口实现
├── controller  // HTTP控制器
│   ├── admin   // 管理后台API控制器
│   └── app     // 前端API控制器
│       └── v1  // 版本控制
├── service     // 业务服务
│   └── impl    // 服务实现
├── convert     // 对象转换器 (使用MapStruct)
├── dal         // 数据访问层
│   ├── dataobject  // 领域对象(DO)
│   └── mapper      // MyBatis Mapper接口
├── job         // 定时任务
└── listener    // 事件监听器
```

## URL路径规范

- 面向终端用户的接口: `/app-api/v1/{module-name}/...`
- 面向管理后台的接口: `/admin-api/{module-name}/...`

## 模块间通信

- 模块间只能通过API模块定义的接口和DTO进行通信
- 严禁模块间直接引用对方的内部实现类或DO对象
- 模块间的依赖关系应当在架构设计阶段明确定义，避免循环依赖























