package com.yunyi.express2b.module.crm.service.cardlimited;
import com.alibaba.fastjson.JSONObject;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.common.util.servlet.ServletUtils;
import com.yunyi.express2b.framework.security.core.LoginUser;
import com.yunyi.express2b.framework.security.core.util.CrmSecurityFrameworkUtils;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.crm.controller.admin.cardlimited.vo.CardShopVo;
import com.yunyi.express2b.module.crm.controller.admin.cardlimited.vo.CardlimitedPageReqVO;
import com.yunyi.express2b.module.crm.controller.admin.cardlimited.vo.CardlimitedSaveReqVO;
import com.yunyi.express2b.module.crm.controller.app.v1.carduser.vo.CardUserSaveReqVO;
import com.yunyi.express2b.module.crm.controller.app.v1.carflimited.vo.CardLimitedVO;
import com.yunyi.express2b.module.crm.controller.app.v1.carflimited.vo.CardUserVO;
import com.yunyi.express2b.module.crm.controller.app.v1.carflimited.vo.RedeemVO;
import com.yunyi.express2b.module.crm.dal.dataobject.cardlimited.CardlimitedBrandDO;
import com.yunyi.express2b.module.crm.dal.dataobject.cardlimited.CardlimitedDO;
import com.yunyi.express2b.module.crm.dal.dataobject.cardlimited.CardlimitedIgnoreCitiesDO;
import com.yunyi.express2b.module.crm.dal.mysql.cardlimited.CardlimitedBrandMapper;
import com.yunyi.express2b.module.crm.dal.mysql.cardlimited.CardlimitedIgnoreCitiesMapper;
import com.yunyi.express2b.module.crm.dal.mysql.cardlimited.CardlimitedMapper;
import com.yunyi.express2b.module.crm.dal.mysql.carduser.CardUserMapper;
import com.yunyi.express2b.module.crm.enums.CardStatusEnum;
import com.yunyi.express2b.module.crm.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.crm.service.carduser.CardUserService;
import com.yunyi.framework.api.base.config.PaymentConfig;
import com.yunyi.framework.api.login.api.payment.PaymentGatewayApi;
import com.yunyi.framework.api.login.api.payment.vo.UnifiedOrderRequest;
import com.yunyi.framework.api.login.utils.TransactionNoGenerator;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import static com.yunyi.express2b.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.crm.enums.ErrorCodeConstants.*;
import static com.yunyi.express2b.module.crm.utils.CardNumberUtil.generateCardNumber;
import static com.yunyi.framework.api.login.utils.TransactionNoGenerator.*;
/**
 * 卡种信息 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class CardlimitedServiceImpl implements CardlimitedService {


    @Resource
    private CardlimitedMapper cardlimitedMapper;
    @Resource
    private CardlimitedBrandMapper cardlimitedBrandMapper;
    @Resource
    private CardlimitedIgnoreCitiesMapper cardlimitedIgnoreCitiesMapper;
    @Resource
    private CardUserMapper cardUserMapper;
    @Resource
    private CardUserService cardUserService;
    @Resource
    private PaymentGatewayApi paymentGateway;
    @Resource
    private PaymentConfig paymentConfig;

    private final Long CLIENTID = 16L;
    //微信支付type
    private final Integer PAYTYPE = 1;



    /**
     * 创建卡种信息
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createCardlimited(CardlimitedSaveReqVO createReqVO) {
        // 插入
        CardlimitedDO cardlimited = BeanUtils.toBean(createReqVO, CardlimitedDO.class);
        cardlimitedMapper.insert(cardlimited);

        // 插入子表
        createCardlimitedBrandList(cardlimited.getId(), createReqVO.getCardlimitedBrands());
        createCardlimitedIgnoreCitiesList(cardlimited.getId(), createReqVO.getCardlimitedIgnoreCitiess());
        // 返回
        return cardlimited.getId();
    }

    /**
     * 更新卡种信息
     * @param updateReqVO 更新信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCardlimited(CardlimitedSaveReqVO updateReqVO) {
        // 校验存在
        validateCardlimitedExists(updateReqVO.getId());
        // 更新
        CardlimitedDO updateObj = BeanUtils.toBean(updateReqVO, CardlimitedDO.class);
        cardlimitedMapper.updateById(updateObj);

        // 更新子表
        updateCardlimitedBrandList(updateReqVO.getId(), updateReqVO.getCardlimitedBrands());
        updateCardlimitedIgnoreCitiesList(updateReqVO.getId(), updateReqVO.getCardlimitedIgnoreCitiess());
    }

    /**
     * 删除卡种信息
     * @param id 编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCardlimited(Long id) {
        // 校验存在
        validateCardlimitedExists(id);
        // 删除
        cardlimitedMapper.deleteById(id);

        // 删除子表
        deleteCardlimitedBrandByCardlimitedId(id);
        deleteCardlimitedIgnoreCitiesByCardlimitedId(id);
    }

    /**
     * 校验卡种信息是否存在
     * @param id 编号
     */
    private void validateCardlimitedExists(Long id) {
        if (cardlimitedMapper.selectById(id) == null) {
            throw exception(CARDLIMITED_NOT_EXISTS);
        }
    }

    /**
     * 获得卡种信息
     * @param id 编号
     * @return
     */
    @Override
    public CardlimitedDO getCardlimited(Long id) {
        return cardlimitedMapper.selectById(id);
    }

    /**
     * 获得卡种信息分页
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<CardlimitedDO> getCardlimitedPage(CardlimitedPageReqVO pageReqVO) {
        return cardlimitedMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（卡种可使用品牌） ====================

    /**
     * 获得卡种可使用品牌列表
     * @param cardlimitedId 卡种id
     * @return
     */
    @Override
    public List<CardlimitedBrandDO> getCardlimitedBrandListByCardlimitedId(Long cardlimitedId) {
        return cardlimitedBrandMapper.selectListByCardlimitedId(cardlimitedId);
    }

    /**
     * 创建卡种可使用品牌列表
     * @param cardlimitedId
     * @param list
     */
    private void createCardlimitedBrandList(Long cardlimitedId, List<CardlimitedBrandDO> list) {
        list.forEach(o -> o.setCardlimitedId(cardlimitedId));
        cardlimitedBrandMapper.insertBatch(list);
    }

    /**
     * 更新卡种可使用品牌列表
     * @param cardlimitedId
     * @param list
     */
    private void updateCardlimitedBrandList(Long cardlimitedId, List<CardlimitedBrandDO> list) {
        deleteCardlimitedBrandByCardlimitedId(cardlimitedId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createCardlimitedBrandList(cardlimitedId, list);
    }

    /**
     * 删除卡种可使用品牌列表
     * @param cardlimitedId
     */
    private void deleteCardlimitedBrandByCardlimitedId(Long cardlimitedId) {
        cardlimitedBrandMapper.deleteByCardlimitedId(cardlimitedId);
    }

    // ==================== 子表（卡种禁用地区） ====================

    /**
     * 获得卡种禁用地区列表
     * @param cardlimitedId 卡种id
     * @return
     */
    @Override
    public List<CardlimitedIgnoreCitiesDO> getCardlimitedIgnoreCitiesListByCardlimitedId(Long cardlimitedId) {
        return cardlimitedIgnoreCitiesMapper.selectListByCardlimitedId(cardlimitedId);
    }

    /**
     * 创建卡种禁用地区列表
     * @param cardlimitedId
     * @param list
     */
    private void createCardlimitedIgnoreCitiesList(Long cardlimitedId, List<CardlimitedIgnoreCitiesDO> list) {
        list.forEach(o -> o.setCardlimitedId(cardlimitedId));
        cardlimitedIgnoreCitiesMapper.insertBatch(list);
    }

    /**
     * 更新卡种禁用地区列表
     * @param cardlimitedId
     * @param list
     */
    private void updateCardlimitedIgnoreCitiesList(Long cardlimitedId, List<CardlimitedIgnoreCitiesDO> list) {
        deleteCardlimitedIgnoreCitiesByCardlimitedId(cardlimitedId);
		list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createCardlimitedIgnoreCitiesList(cardlimitedId, list);
    }

    /**
     * 删除卡种禁用地区列表
     * @param cardlimitedId
     */
    private void deleteCardlimitedIgnoreCitiesByCardlimitedId(Long cardlimitedId) {
        cardlimitedIgnoreCitiesMapper.deleteByCardlimitedId(cardlimitedId);
    }


    /**
     * 获取门店卡种列表
     * @return
     */
    @Override
    public List<CardShopVo> getShopCardPage() {
        List<CardShopVo> resultList = cardlimitedMapper.selectCardShopPage();
        return resultList;
    }

    /**
     * 获取用户卡种列表
     * @param personalId 个人标识，用于标识特定的个人用户
     * @param status
     * @param time
     * @param type
     * @return
     */
    @Override
    public List<CardUserVO> getUserCardAll(Long personalId, Integer status,LocalDateTime time,Integer type) {
        List<CardUserVO> resultList = null;
        if (type == 1 ||  type == 2){
             resultList = cardlimitedMapper.selectUserCardAll1(personalId,status,time);
        }else {
              resultList =cardlimitedMapper.selectUserCardAll(personalId,status,time);
        }
        return  resultList;
    }


    /**
     * 发送消息接口
     * @param redeemVo 兑换信息对象，包含发送消息所需的各种参数
     * @return
     */
    @Override
    public CommonResult<String> sendMessage(RedeemVO redeemVo) {
        //比较输入的redeemVo.getRedeemCode()和数据库中的优惠卷兑换码是否匹配
        if (cardlimitedMapper.selectRedeemCode(redeemVo.getRedeemCode())) {
            // 优惠卷兑换码匹配成功，执行相应的操作
            // 例如，更新数据库中的优惠卷兑换码状态，或者发送优惠卷兑换码到用户邮箱
            // ...

            // 优惠卷兑换码匹配成功，发送成功消息
            return CommonResult.success("优惠卷兑换码匹配成功，发送成功消息");
        } else {
            // 优惠卷兑换码匹配失败，发送失败消息
            return CommonResult.error(REDEEM_FAIL);
        }
    }

    /**
     * 获取卡限制信息
     * @param id 卡限制的唯一标识符，用于标识特定的卡限制信息
     * @return
     */
    @Override
    public CardLimitedVO getCardlimitedOne(Long id) {
        CardLimitedVO cardLimitedVO =cardlimitedMapper.getCardlimitedOne(id);
        return cardLimitedVO;
    }


    /**
     * 用户购买卡接口
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<JSONObject> userBuyCard(Long id) {
        try {
            // 获取登录用户信息并校验
            // 查询卡片信息
            CardShopVo cardShopVo = cardlimitedMapper.getBuyCardPage(id);

            //获取用户
            LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();

            Long personId = loginUser.getId();
            //生成交易编号
            String transactionNo = TransactionNoGenerator.generate(PAY,WX,EB2,new Date(),cardShopVo.getPrice());
            //组装请求参数
            UnifiedOrderRequest request = UnifiedOrderRequest.builder()
                    .user(Math.toIntExact(personId))
                    .payOrderSn(transactionNo)
                    .amount(cardShopVo.getPrice())
                    .notifyUrl(paymentConfig.getPayNotifyUrl())
                    .ip(ServletUtils.getClientIP())
                    .body(cardShopVo.getDescription())
                    .clientId(CLIENTID.toString())
                    .attach(cardShopVo.getCardlimitedName())
                    .payType(PAYTYPE)
                    .build();

            // 支付金额校验
            if (!isAmountValid(Long.valueOf(cardShopVo.getPrice()))) {
                log.error("支付金额无效，金额={}", cardShopVo.getPrice());
                return CommonResult.error(ErrorCodeConstants.CARD_PAY_NOT_EXISTS);
            }

            //发送请求
            CommonResult<JSONObject> jsonObjectCommonResult = paymentGateway.unifiedOrder(request);
            // 判断请求是否成功
            if (!jsonObjectCommonResult.isSuccess()) {
                // 记录错误日志
                log.error("调用 paymentGateway.unifiedOrder 失败，错误码: {}, 错误信息: {}",
                        jsonObjectCommonResult.getCode(), jsonObjectCommonResult.getMsg());
                return CommonResult.error(ErrorCodeConstants.BUY_CARD_FAIL);
            }else{
                // 构建请求对象
                CardUserSaveReqVO cardUserSaveReqVO = buildCardUserSaveReqVO(cardShopVo, CrmSecurityFrameworkUtils.getLoginUserId());
                cardUserSaveReqVO.setStatus(1);
                cardUserSaveReqVO.setPayOrderSn(transactionNo);
                // 创建用户卡片
                cardUserService.createCardUser(cardUserSaveReqVO);
                return CommonResult.success(jsonObjectCommonResult.getData());
            }
        } catch (Exception e) {
            // 记录异常日志
            log.error("创建用户卡片失败，id={}", id, e);
            return CommonResult.error(ErrorCodeConstants.BUY_CARD_FAIL);
        }
    }

    /**
     * 检查卡片是否可用
     * @param cardShopVo 卡片信息对象
     * @return 卡片是否可用
     */
    private boolean isCardAvailable(CardShopVo cardShopVo) {
        // 卡片信息校验
        if (cardShopVo == null) {
            log.error("卡片信息不存在，id={}", cardShopVo);
            throw exception(CARDLIMITED_NOT_EXISTS);
        }
        if (!isCardAvailable(cardShopVo)) {
            log.error("卡片不可用，id={}", cardShopVo);
            throw exception(CARDLIMITED_NOT_EXISTS);
        }
        return true;
    }

    /**
     * 检查用户是否有购买权限
     * @param loginUser 登录用户信息
     * @return 用户是否有购买权限
     */
    private boolean isUserAllowedToBuy(LoginUser loginUser) {
        if(loginUser==null){
            throw exception(UNAUTHORIZED);
        }

        // 用户信息校验
        if (!isUserAllowedToBuy(loginUser)) {
            log.error("用户没有购买权限，userId={}", loginUser.getId());
            throw exception(CARD_PERMISSION_NOT_EXISTS);
        }

        return true;
    }

    /**
     * 检查支付金额是否有效
     * @param amount 支付金额
     * @return 支付金额是否有效
     */
    private boolean isAmountValid(Long amount) {
        return amount != null && amount > 0;
    }


    /**
     * 构建卡用户保存请求对象
     * 卡片状态被设置为“OPEN”，表示卡片是可用的状态
     *
     * @param cardShopVo 卡片信息对象，包含卡片的详细信息
     * @param memberId 用户ID
     * @return 返回构建好的CardUserSaveReqVO对象
     */
    private CardUserSaveReqVO buildCardUserSaveReqVO(CardShopVo cardShopVo, Long memberId) {
      String cardNumber =  generateCardNumber();
        CardUserSaveReqVO cardUserSaveReqVO = new CardUserSaveReqVO();
        cardUserSaveReqVO.setCardlimitedId(cardShopVo.getId());
        cardUserSaveReqVO.setCardName(cardShopVo.getCardlimitedName());
        cardUserSaveReqVO.setTotal(cardShopVo.getNumber());
        cardUserSaveReqVO.setRemaining(cardShopVo.getNumber());
        cardUserSaveReqVO.setPersonalId(memberId);
        cardUserSaveReqVO.setCardNumber(cardNumber);
        cardUserSaveReqVO.setCardStartDate(LocalDateTime.now());
        // 正确计算卡有效期结束时间
        LocalDateTime cardEndDate = cardUserSaveReqVO.getCardStartDate().plusDays(30); // 增加30天
        cardUserSaveReqVO.setCardEndDate(cardEndDate);

        // 安全地设置状态
        try {
            cardUserSaveReqVO.setStatus(CardStatusEnum.ENABLE.getCode());
        } catch (NumberFormatException e) {
            throw exception(ErrorCodeConstants.BUY_CARD_FAIL);
        }
        return cardUserSaveReqVO;
    }
    /**
     * 每天凌晨0点更新所有卡片用户的状态为2
     */
    @Override
    public void updateCardUserStatusToTwo() {
        LocalDateTime now = LocalDateTime.now();
        cardUserMapper.updateAllStatusToTwo(now);
    }


}