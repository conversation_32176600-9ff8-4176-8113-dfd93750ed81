package com.yunyi.express2b.module.express.controller.app.v1.order.vo;

import com.yunyi.express2b.module.express.controller.admin.order.vo.OrderSaveReqVO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.RecipientVO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgreePayVo {
    /**
     * 支付金额
     */
   // private OrderDO orderDO;
    private Integer receivedAmount;
    /**
     * 订单号
     */
    private List<String> orderNos;
}
