package com.yunyi.express2b.module.express.data;

import com.yunyi.express2b.module.express.controller.admin.order.vo.OrderCreateRequest;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 生成订单测试数据
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/11 20:05
 */
public class GenerateOrderTestData {

    /**
     * 生成成功的订单。
     */
    public static OrderDO generateOrderSuccess() {
        return OrderDO.builder()
                //.id(380L) // id
                .orderNo("EA25042240026597035") // order_no
                //.batchId(33L) // batch_id
                .personalId(1L) // personal_id
                .brandId(445566L) // brand_id
                .pricingKey("pricingKey") // pricing_key
                .profitKey("profitKey") // profit_key
                .status("NOTPAY") // STATUS
                .senderName("张三") // sender_name
                .senderPhone("13800138000") // sender_phone
                .senderAddress("沈阳市和平区中山路150号沈阳大悦城B座10层") // sender_address
                .senderAddressId(1L) // sender_address_id
                .receiverName("李刚") // receiver_name
                .receiverPhone("13900139000") // receiver_phone
                .receiverAddress("沈阳市铁西区建设东路22号沈阳科技大厦5楼") // receiver_address
                .receiverAddressId(2L) // receiver_address_id
                .costAmount(1500) // cost_amount
                .retailAmount(2000) // retail_amount
                .receivedAmount(13) // received_amount
                .discountAmount(50) // discount_amount
                .agentCommission(100) // agent_commission
                .groupLeaderCommission(200) // group_leader_commission
                .trackingNumber("") // tracking_number
                .itemName("鞋子") // item_name
                .itemCategory("日用品") // item_category
                .itemQuantity(1) // item_quantity
                .itemWeight(2) // item_weight
                .itemValue(new BigDecimal("5000.00")) // item_value
                .packagingDetails(null) // packaging_details
                .isInsured(false) // is_insured
                .insuredAmount(new BigDecimal("5000.00")) // insured_amount
                .scheduledDate(LocalDate.of(2025, 3, 28)) // scheduled_date
                //.scheduledStartTime(LocalDateTime.of(2025, 3, 28, 14, 30)) // scheduled_start_time
                //.scheduledEndTime(null) // scheduled_end_time
                .receiverStaffName("Mike Smith") // receiver_staff_name
                .payType("微信") // pay_type
                .memberIp("***********") // member_ip
                .ucmOrderSn("") // ucm_order_sn
                .cardNumber("123")
                .build();
    }

    public static OrderCreateRequest yunliOrder(){
        return OrderCreateRequest.builder()
                .orderNo("EA25042240026597035") // order_no
                .productCode("STO-INT") // express_code
                .personalId(14406297L) // personal_id (from '14406297')
                .pricingKey("PricingKey") // pricing_key
                .profitKey("ProfitKey") // profit_key
                .status("PAID") // status
                .senderName("李四") // sender_name
                .senderPhone("13900000000") // sender_phone
                .senderAddress("恒大二期") // sender_address
                .senderAddressId(1L) // sender_address_id
                .receiverName("张三") // receiver_name
                .receiverPhone("13600000000") // receiver_phone
                .receiverAddress("外滩1号") // receiver_address
                .receiverAddressId(2L) // receiver_address_id
                .costAmount(null) // cost_amount (if null, set to null as in the database)
                .retailAmount(13) // retail_amount
                .receivedAmount(13) // received_amount
                .discountAmount(0) // discount_amount
                .agentCommission(null) // agent_commission (if null, set to null)
                .groupLeaderCommission(null) // group_leader_commission (if null, set to null)
                .trackingNumber("D20250421092406546580") // tracking_number
                .itemName("示例商品") // item_name
                .itemCategory("电子产品") // item_category
                .itemQuantity(1) // item_quantity
                .itemWeight(1) // item_weight
                .itemValue(null) // item_value (if null, set to null)
                .packagingDetails(null) // packaging_details
                .isInsured(false) // is_insured (b'0' means false)
                .insuredAmount(null) // insured_amount (null)
                .scheduledDate(LocalDate.of(2025, 4, 21)) // scheduled_date (based on '2025-04-21' as placeholder, adjust if needed)
                .receiverStaffName(null) // receiver_staff_name (null)
                .payType("微信") // pay_type (adjust as necessary)
                .memberIp("***********") // member_ip
                .ucmOrderSn(null) // ucm_order_sn (null)
                .cardNumber("2") // card_number
                .build();
    }

}
