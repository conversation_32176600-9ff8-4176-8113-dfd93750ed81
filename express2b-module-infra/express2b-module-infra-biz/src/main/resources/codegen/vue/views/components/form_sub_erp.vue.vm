#set ($subTable = $subTables.get($subIndex))##当前表
#set ($subColumns = $subColumnsList.get($subIndex))##当前字段数组
#set ($subSimpleClassName = $subSimpleClassNames.get($subIndex))
#set ($subJoinColumn = $subJoinColumns.get($subIndex))##当前 join 字段
<template>
  <div class="app-container">
    <!-- 对话框(添加 / 修改) -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="45%" v-dialogDrag append-to-body>
      <el-form ref="formRef" :model="formData" :rules="formRules" v-loading="formLoading" label-width="100px">
          #foreach($column in $subColumns)
              #if ($column.createOperation || $column.updateOperation)
                  #set ($dictType = $column.dictType)
                  #set ($javaField = $column.javaField)
                  #set ($javaType = $column.javaType)
                  #set ($AttrName = $column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                  #set ($comment = $column.columnComment)
                  #if ( $column.id == $subJoinColumn.id) ## 特殊：忽略主子表的 join 字段，不用填写
                  #elseif ($column.htmlType == "input" && !$column.primaryKey)## 忽略主键，不用在表单里
                    <el-form-item label="${comment}" prop="${javaField}">
                      <el-input v-model="formData.${javaField}" placeholder="请输入${comment}" />
                    </el-form-item>
                  #elseif($column.htmlType == "imageUpload")## 图片上传
                      #set ($hasImageUploadColumn = true)
                    <el-form-item label="${comment}" prop="${javaField}">
                      <ImageUpload v-model="formData.${javaField}"/>
                    </el-form-item>
                  #elseif($column.htmlType == "fileUpload")## 文件上传
                      #set ($hasFileUploadColumn = true)
                    <el-form-item label="${comment}" prop="${javaField}">
                      <FileUpload v-model="formData.${javaField}"/>
                    </el-form-item>
                  #elseif($column.htmlType == "editor")## 文本编辑器
                      #set ($hasEditorColumn = true)
                    <el-form-item label="${comment}" prop="${javaField}">
                      <editor v-model="formData.${javaField}" :min-height="192"/>
                    </el-form-item>
                  #elseif($column.htmlType == "select")## 下拉框
                    <el-form-item label="${comment}" prop="${javaField}">
                      <el-select v-model="formData.${javaField}" placeholder="请选择${comment}">
                          #if ("" != $dictType)## 有数据字典
                            <el-option v-for="dict in this.getDictDatas(DICT_TYPE.$dictType.toUpperCase())"
                                       :key="dict.value" :label="dict.label" #if ($column.javaType == "Integer" || $column.javaType == "Long"):value="parseInt(dict.value)"#else:value="dict.value"#end />
                          #else##没数据字典
                            <el-option label="请选择字典生成" value="" />
                          #end
                      </el-select>
                    </el-form-item>
                  #elseif($column.htmlType == "checkbox")## 多选框
                    <el-form-item label="${comment}" prop="${javaField}">
                      <el-checkbox-group v-model="formData.${javaField}">
                          #if ("" != $dictType)## 有数据字典
                            <el-checkbox v-for="dict in this.getDictDatas(DICT_TYPE.$dictType.toUpperCase())"
                                         :key="dict.value" #if($column.javaType == "Integer" || $column.javaType == "Long"):label="parseInt(dict.value)"#else:label="dict.value"#end>{{dict.label}}</el-checkbox>
                          #else##没数据字典
                            <el-checkbox>请选择字典生成</el-checkbox>
                          #end
                      </el-checkbox-group>
                    </el-form-item>
                  #elseif($column.htmlType == "radio")## 单选框
                    <el-form-item label="${comment}" prop="${javaField}">
                      <el-radio-group v-model="formData.${javaField}">
                          #if ("" != $dictType)## 有数据字典
                            <el-radio v-for="dict in this.getDictDatas(DICT_TYPE.$dictType.toUpperCase())"
                                      :key="dict.value" #if($column.javaType == "Integer" || $column.javaType == "Long"):label="parseInt(dict.value)"
                                      #else:label="dict.value"#end>{{dict.label}}</el-radio>
                          #else##没数据字典
                            <el-radio label="1">请选择字典生成</el-radio>
                          #end
                      </el-radio-group>
                    </el-form-item>
                  #elseif($column.htmlType == "datetime")## 时间框
                    <el-form-item label="${comment}" prop="${javaField}">
                      <el-date-picker clearable v-model="formData.${javaField}" type="date" value-format="timestamp" placeholder="选择${comment}" />
                    </el-form-item>
                  #elseif($column.htmlType == "textarea")## 文本框
                    <el-form-item label="${comment}" prop="${javaField}">
                      <el-input v-model="formData.${javaField}" type="textarea" placeholder="请输入内容" />
                    </el-form-item>
                  #end
              #end
          #end
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import * as ${simpleClassName}Api from '@/api/${table.moduleName}/${table.businessName}';
      #if ($hasImageUploadColumn)
      import ImageUpload from '@/components/ImageUpload';
      #end
      #if ($hasFileUploadColumn)
      import FileUpload from '@/components/FileUpload';
      #end
      #if ($hasEditorColumn)
      import Editor from '@/components/Editor';
      #end
  export default {
    name: "${subSimpleClassName}Form",
    components: {
        #if ($hasImageUploadColumn)
          ImageUpload,
        #end
        #if ($hasFileUploadColumn)
          FileUpload,
        #end
        #if ($hasEditorColumn)
          Editor,
        #end
    },
    data() {
      return {
        // 弹出层标题
        dialogTitle: "",
        // 是否显示弹出层
        dialogVisible: false,
        // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
        formLoading: false,
        // 表单参数
        formData: {
            #foreach ($column in $subColumns)
                #if ($column.createOperation || $column.updateOperation)
                    #if ($column.htmlType == "checkbox")
                            $column.javaField: [],
                    #else
                            $column.javaField: undefined,
                    #end
                #end
            #end
        },
        // 表单校验
        formRules: {
            #foreach ($column in $subColumns)
                #if (($column.createOperation || $column.updateOperation) && !$column.nullable && !${column.primaryKey})## 创建或者更新操作 && 要求非空 && 非主键
                    #set($comment=$column.columnComment)
                        $column.javaField: [{ required: true, message: "${comment}不能为空", trigger: #if($column.htmlType == "select")"change"#else"blur"#end }],
                #end
            #end
        },
      };
    },
    methods: {
      /** 打开弹窗 */
      async open(id, ${subJoinColumn.javaField}) {
        this.dialogVisible = true;
        this.reset();
        this.formData.${subJoinColumn.javaField} = ${subJoinColumn.javaField};
        // 修改时，设置数据
        if (id) {
          this.formLoading = true;
          try {
            const res = await ${simpleClassName}Api.get${subSimpleClassName}(id);
            this.formData = res.data;
            this.dialogTitle = "修改${subTable.classComment}";
          } finally {
            this.formLoading = false;
          }
        }
        this.dialogTitle = "新增${subTable.classComment}";
      },
      /** 提交按钮 */
      async submitForm() {
        await this.#[[$]]#refs["formRef"].validate();
        this.formLoading = true;
        try {
            const data = this.formData;
            // 修改的提交
            if (data.${primaryColumn.javaField}) {
            await  ${simpleClassName}Api.update${subSimpleClassName}(data);
            this.#[[$modal]]#.msgSuccess("修改成功");
            this.dialogVisible = false;
            this.#[[$]]#emit('success');
              return;
            }
            // 添加的提交
              await ${simpleClassName}Api.create${subSimpleClassName}(data);
              this.#[[$modal]]#.msgSuccess("新增成功");
              this.dialogVisible = false;
              this.#[[$]]#emit('success');
        }finally {
          this.formLoading = false;
        }
      },
      /** 表单重置 */
      reset() {
        this.formData = {
            #foreach ($column in $subColumns)
                #if ($column.createOperation || $column.updateOperation)
                    #if ($column.htmlType == "checkbox")
                            $column.javaField: [],
                    #else
                            $column.javaField: undefined,
                    #end
                #end
            #end
        };
        this.resetForm("formRef");
      },
    }
  };
</script>
